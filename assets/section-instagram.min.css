.scrolling-banner{position:relative;overflow:hidden}.scrolling-banner .banner{display:flex;align-items:center;list-style-type:none}.scrolling-banner .banner,.scrolling-banner .banner-item{margin:0}.scrolling-banner .banner-item{display:flex;flex-shrink:0;padding-left:var(--scrolling-banner-gutter-mobile);padding-right:var(--scrolling-banner-gutter-mobile)}@media screen and (min-width:481px){.scrolling-banner .banner-item{padding-left:var(--scrolling-banner-gutter-desktop);padding-right:var(--scrolling-banner-gutter-desktop)}}.scrolling-banner .show-fade:after,.scrolling-banner .show-fade:before{content:"";position:absolute;z-index:1;top:0;width:30%;height:100%;opacity:1;pointer-events:none}.scrolling-banner .show-fade:before{left:0;background:linear-gradient(90deg,var(--color-scheme-background-color) 15%,rgba(255,255,255,0) 85%)}.scrolling-banner .show-fade:after{right:0;background:linear-gradient(270deg,var(--color-scheme-background-color) 15%,rgba(255,255,255,0) 85%)}.scrolling-banner .divider{display:block;height:100%;border-right:1px solid var(--color-scheme-border-color);margin:0 var(--scrolling-banner-gutter-mobile)}@media screen and (min-width:481px){.scrolling-banner .divider{margin:0 var(--scrolling-banner-gutter-desktop)}}.scrolling-banner .icon--placeholder{max-width:unset;width:auto}.scrolling-banner .content-image{width:auto}.scrolling-banner scrolling-banner{rotate:var(--scrolling-banner-rotate)}@media (prefers-reduced-motion){scrolling-banner{display:flex;overflow:hidden}scrolling-banner .banner[data-duplicated]{display:none}.scrolling-banner .banner{flex-wrap:wrap;align-items:normal}.scrolling-banner .banner-item{align-items:center}.scrolling-banner .divider{display:flex;height:unset}}@media (prefers-reduced-motion:no-preference){scrolling-banner{display:grid;grid:auto/auto-flow max-content;transition:transform .6s ease-out}scrolling-banner.animation-direction-normal{justify-content:start}scrolling-banner.animation-direction-normal .banner{animation-name:translateLeft}scrolling-banner.animation-direction-reverse{justify-content:end}scrolling-banner.animation-direction-reverse .banner{animation-name:translateRight}scrolling-banner .banner{animation-duration:var(--scrolling-banner-animation-speed);animation-timing-function:linear;animation-iteration-count:infinite}scrolling-banner:hover .banner{animation-play-state:paused}}@keyframes translateLeft{0%{transform:translate(0)}100%{transform:translate(-100%)}}@keyframes translateRight{0%{transform:translate(0)}100%{transform:translate(100%)}}



.instafeed-hover-layer {
    position: absolute;
    background-color: rgba(0, 0, 0, .5);
    opacity: 0;
    width: 100%;
    height: 100%;
    transition: opacity .3s ease;
    cursor: pointer;
  top: 0;
}
.instafeed-hover-icon {
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 15px;
    display: flex;
    align-items: center;
    z-index: 1000;
}
scrolling-banner li.banner-item:hover .instafeed-hover-layer {
    opacity: 1;
}
.scrolling-banner .banner-item,
.scrolling-banner .banner-item a {
    position: relative;
}
h2.instagram-hedding.h2 {
    font-weight: 700;
    font-size: 62px;
    line-height: 100%;
    text-align: center;
    vertical-align: middle;
    text-transform: uppercase;
  
    margin: 40px 0;
}
scrolling-banner .banner {
    gap: 0;
    padding: 0;
}
 @media only screen and (min-width: 481px) and (max-width: 768px) {
h2.instagram-hedding.h2 {
    font-family: Meltmino;
    font-size: 45px;
    margin: 20px 0;
  padding: 0 60px;
}  
}
 @media only screen and (max-width: 480px) {
h2.instagram-hedding.h2 {
    font-size: 30px;
     padding: 0 20px;
}
}