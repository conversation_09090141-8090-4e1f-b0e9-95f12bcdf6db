/* Extreme spacing reduction for mobile */

@media screen and (max-width: 749px) {
  /* Create a direct connection between slider and reviews */
  #shopify-section-slider_whats_inside {
    margin-bottom: -50px !important; /* Extreme negative margin */
    padding-bottom: 0 !important;
    z-index: 1;
    position: relative;
  }
  
  #shopify-section-17372431293a76cefd {
    margin-top: -50px !important; /* Extreme negative margin */
    padding-top: 0 !important;
    position: relative;
    z-index: 2;
  }
  
  /* Target the JudgeMe container */
  #judgeme_product_reviews {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  
  /* Remove all spacing from the reviews widget */
  .jdgm-rev-widg {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  
  /* Reduce title size to save space */
  .jdgm-rev-widg__title {
    margin-top: 0 !important;
    margin-bottom: 0.25rem !important;
    padding-top: 0 !important;
  }
  
  /* Force all section spacing to be minimal */
  .shopify-section {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  
  /* Target the specific sections to create overlap */
  #LT--slider_whats_inside {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
  }
  
  /* Hide any extra elements that might be creating space */
  .jdgm-rev-widg__header {
    padding-bottom: 0 !important;
    margin-bottom: 0.25rem !important;
  }
  
  /* Reduce spacing in the reviews list */
  .jdgm-rev__header {
    margin-bottom: 0.25rem !important;
  }
  
  /* Reduce spacing in the pagination */
  .jdgm-paginate {
    margin-top: 0.5rem !important;
    padding-top: 0 !important;
  }
  
  /* Target any potential wrappers */
  .shopify-section--apps > div {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
}
