/* Custom CSS for Judge.me reviews */

/* Override the existing styles for the reviews title to make it an h2 */
.jdgm-rev-widg__title,
h2.jdgm-rev-widg__title {
  display: block !important;
  visibility: visible !important;
  font-family: var(--heading-font-family) !important;
  font-weight: var(--heading-font-weight) !important;
  font-size: var(--text-h2) !important;
  line-height: 1.1 !important;
  margin-bottom: 1.5rem !important;
  color: rgb(var(--text-color)) !important;
  text-transform: none !important;
}

/* Make sure the title is properly displayed */
.jdgm-widget-actions-wrapper {
  margin-top: 1.5rem !important;
}

/* Ensure proper spacing around the reviews widget */
.jdgm-rev-widg {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
  padding: 0 !important;
}

span.jdgm-prev-badge__text {
  font-family: 'FormaDJRText';
}
.jdgm-rev-widg__summary-inner {
  font-family: 'FormaDJRText';
}

.jdgm-rev-widg__sort-wrapper {
  font-family: 'FormaDJRText';
}