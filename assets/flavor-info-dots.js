document.addEventListener('DOMContentLoaded', function() {
  // Function to check if Variety Pack is selected
  function checkVarietyPackSelected() {
    const flavorOptions = document.querySelectorAll('.variant-picker__option-values input[type="radio"]');
    let isVarietyPackSelected = false;

    flavorOptions.forEach(option => {
      if (option.checked && option.nextElementSibling && option.nextElementSibling.textContent.trim().includes('Variety Pack')) {
        isVarietyPackSelected = true;
      }
    });

    // Add or remove class based on selection
    const productInfo = document.querySelector('.product-info');
    if (productInfo) {
      if (isVarietyPackSelected) {
        productInfo.classList.add('variant-selected-flavor-variety-pack');
      } else {
        productInfo.classList.remove('variant-selected-flavor-variety-pack');
      }
    }
  }

  // Run on page load
  checkVarietyPackSelected();

  // Listen for changes to the variant selection
  document.addEventListener('change', function(event) {
    if (event.target.matches('.variant-picker__option-values input[type="radio"]')) {
      checkVarietyPackSelected();
    }
  });

  // Also listen for variant changes from other sources
  document.addEventListener('variant:change', checkVarietyPackSelected);
});
