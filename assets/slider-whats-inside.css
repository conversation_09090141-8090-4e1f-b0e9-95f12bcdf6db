h2.lt-testimonial-title {
  font-family: var(--heading-font-family) !important;
  font-weight: var(--heading-font-weight) !important;
  font-style: var(--heading-font-style) !important;
  letter-spacing: var(--heading-letter-spacing) !important;
  text-transform: var(--heading-text-transform) !important;
  font-size: var(--text-h2) !important;
  text-align: center !important;
}

.testimonial-title-slider {
  font-family: var(--heading-font-family) !important;
  font-weight: var(--heading-font-weight) !important;
  font-style: var(--heading-font-style) !important;
  letter-spacing: var(--heading-letter-spacing) !important;
  text-transform: var(--heading-text-transform) !important;
  font-size: var(--text-h4) !important;
  text-align: center !important;
}

/* Ensure the slider section doesn't exceed the max page width */
#LT--slider_whats_inside .wrap-width-contain {
  max-width: var(--container-max-width);
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
}

/* Make sure the swiper container respects the max width */
#LT--slider_whats_inside .luna-swiper-testimonials {
  max-width: 100%;
  overflow: hidden;
}

/* Make the slider cards display properly */
#LT--slider_whats_inside .lt-testimonial-block-slider {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: auto;
  width: 100%;
  text-align: center !important;
  border-radius: 20px !important; /* Ensure rounded corners */
  overflow: hidden !important; /* Ensure content doesn't overflow the rounded corners */
}

/* Control the image aspect ratio while maintaining full visibility */
#LT--slider_whats_inside .image-item-wrapper-slider {
  flex: 0 0 auto;
  width: 100%;
  position: relative;
  /* aspect-ratio is now set inline based on section.settings.media_size */
  margin-bottom: 25px; /* Add more space below the image */
  border-radius: 20px !important; /* Ensure rounded corners */
  overflow: hidden !important; /* Ensure the image doesn't overflow the rounded corners */
}

#LT--slider_whats_inside .image-item-wrapper-slider img,
#LT--slider_whats_inside .slider-image {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  border-radius: 20px !important; /* Ensure rounded corners */
}

/* Style the text content area */
#LT--slider_whats_inside .lt-testimonial-info-slider {
  padding: 35px 15px 15px !important;
  display: flex;
  flex-direction: column;
  margin-top: 25px !important;
  text-align: center !important;
}

/* Style the testimonial quote that now appears above the title */
#LT--slider_whats_inside .testimonial-quote-slider {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
  text-align: center;
  width: 100%;
}

/* Style the testimonial title that now appears below the quote */
h3.testimonial-title-slider {
  margin-top: 10px !important;
  text-align: center;
  width: 100%;
}

/* Ensure the swiper container allows full height */
#LT--slider_whats_inside .swiper-slide {
  height: auto !important;
}

#LT--slider_whats_inside .luna-swiper-testimonials .swiper-wrapper {
  align-items: stretch;
}

/* Position the slider arrows */
#LT--slider_whats_inside .luna-swiper-testimonials {
  position: relative;
}

#LT--slider_whats_inside .slider-arrow-slider_whats_inside,
#LT--slider_whats_inside .luna-bottom-next,
#LT--slider_whats_inside .luna-bottom-prev {
  display: none;
}

#LT--slider_whats_inside .swiper-button-next,
#LT--slider_whats_inside .swiper-button-prev,
#LT--slider_whats_inside .arrowButtons {
  position: absolute !important;
  top: 30% !important;
  transform: translateY(-50%) !important;
  width: 50px !important;
  height: 50px !important;
  margin: 0 !important;
  z-index: 10 !important;
  background-color: transparent !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  bottom: auto !important;
}

#LT--slider_whats_inside .swiper-button-next {
  right: 10px !important;
  left: auto !important;
}

#LT--slider_whats_inside .swiper-button-prev {
  left: 10px !important;
  right: auto !important;
}

#LT--slider_whats_inside .swiper-button-next svg,
#LT--slider_whats_inside .swiper-button-prev svg {
  width: 40px !important;
  height: 40px !important;
  display: block !important;
}

/* Override any existing styles */
#LT--slider_whats_inside .swiper-button-next:after,
#LT--slider_whats_inside .swiper-button-prev:after {
  display: none !important;
}

/* Target the arrows4 navigation style */
#LT--slider_whats_inside .arrowButtons {
  bottom: auto !important;
  top: 30% !important;
  transform: translateY(-50%) !important;
}

#LT--slider_whats_inside .arrowButtons svg {
  width: 40px !important;
  height: 40px !important;
}

@media (max-width: 749px) {
  #LT--slider_whats_inside .swiper-button-next,
  #LT--slider_whats_inside .swiper-button-prev {
    display: none;
  }
}

