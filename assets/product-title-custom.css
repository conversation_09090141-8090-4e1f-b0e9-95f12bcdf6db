/* Custom CSS to make product title slightly smaller on desktop */

/* Add a test style to verify the CSS is loading */
.product-info__title {
  color: #2A2A2A !important; /* This should be visible on all product titles */
}

@media screen and (min-width: 700px) {
  /* Target the product title specifically on the product page using multiple selectors for better specificity */
  .product-info__title.h2,
  h2.product-info__title,
  .product-info h2.product-info__title,
  main .product-info__title.h2,
  [data-block-type="title"] .product-info__title {
    font-size: calc(var(--text-h2) * 1) !important; /* Make it 75% of the original size */
    line-height: 1.1;
  }
}

.product-info__text {
  font-family: 'FormaDJRText';
}