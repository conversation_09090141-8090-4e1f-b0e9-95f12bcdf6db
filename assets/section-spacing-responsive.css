/* Custom CSS for responsive section spacing */

/* Reduce space between slider and reviews on mobile */
@media screen and (max-width: 749px) {
  /* Target the JudgeMe reviews widget container */
  .jdgm-rev-widg {
    margin-top: 0 !important; /* Completely remove top margin */
  }

  /* Target the apps section that contains the JudgeMe reviews */
  #shopify-section-17372431293a76cefd {
    padding-top: 0 !important;
    margin-top: -30px !important; /* Add negative margin to pull it up */
  }

  /* Reduce bottom padding of the slider section on mobile */
  #shopify-section-slider_whats_inside {
    padding-bottom: 0 !important; /* Completely remove bottom padding */
  }

  /* Reduce spacing in the JudgeMe widget itself */
  .jdgm-rev-widg__title {
    margin-bottom: 0.5rem !important; /* Reduce title bottom margin */
  }

  .jdgm-rev-widg__summary {
    margin-bottom: 0.5rem !important; /* Reduce summary bottom margin */
  }

  /* Adjust spacing for the entire reviews section */
  .shopify-section--apps {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  /* Target the section spacing variables */
  [role="main"] .shopify-section {
    --section-stack-spacing-block: 0px !important;
  }
}
