var Pi=Object.create;var Re=Object.defineProperty;var Fi=Object.getOwnPropertyDescriptor;var Li=Object.getOwnPropertyNames;var Ci=Object.getPrototypeOf,Ri=Object.prototype.hasOwnProperty;var Mi=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),ki=(e,t)=>{for(var r in t)Re(e,r,{get:t[r],enumerable:!0})},zi=(e,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Li(t))!Ri.call(e,n)&&n!==r&&Re(e,n,{get:()=>t[n],enumerable:!(i=Fi(t,n))||i.enumerable});return e};var ji=(e,t,r)=>(r=e!=null?Pi(Ci(e)):{},zi(t||!e||!e.__esModule?Re(r,"default",{value:e,enumerable:!0}):r,e));var Mr=Mi(()=>{(function(){"use strict";var e=function(h,c){var p=function(N){for(var E=0,_=N.length;E<_;E++)b(N[E])},b=function(N){var E=N.target,_=N.attributeName,U=N.oldValue;E.attributeChangedCallback(_,U,E.getAttribute(_))};return function(w,N){var E=w.constructor.observedAttributes;return E&&h(N).then(function(){new c(p).observe(w,{attributes:!0,attributeOldValue:!0,attributeFilter:E});for(var _=0,U=E.length;_<U;_++)w.hasAttribute(E[_])&&b({target:w,attributeName:E[_],oldValue:null})}),w}};function t(h,c){if(h){if(typeof h=="string")return r(h,c);var p=Object.prototype.toString.call(h).slice(8,-1);if(p==="Object"&&h.constructor&&(p=h.constructor.name),p==="Map"||p==="Set")return Array.from(h);if(p==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(p))return r(h,c)}}function r(h,c){(c==null||c>h.length)&&(c=h.length);for(var p=0,b=new Array(c);p<c;p++)b[p]=h[p];return b}function i(h,c){var p=typeof Symbol<"u"&&h[Symbol.iterator]||h["@@iterator"];if(!p){if(Array.isArray(h)||(p=t(h))||c&&h&&typeof h.length=="number"){p&&(h=p);var b=0,w=function(){};return{s:w,n:function(){return b>=h.length?{done:!0}:{done:!1,value:h[b++]}},e:function(U){throw U},f:w}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var N=!0,E=!1,_;return{s:function(){p=p.call(h)},n:function(){var U=p.next();return N=U.done,U},e:function(U){E=!0,_=U},f:function(){try{!N&&p.return!=null&&p.return()}finally{if(E)throw _}}}}var n=!0,s=!1,o="querySelectorAll",u=function(c){var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:document,b=arguments.length>2&&arguments[2]!==void 0?arguments[2]:MutationObserver,w=arguments.length>3&&arguments[3]!==void 0?arguments[3]:["*"],N=function U(mt,vt,tt,x,$,q){var it=i(mt),Pt;try{for(it.s();!(Pt=it.n()).done;){var Q=Pt.value;(q||o in Q)&&($?tt.has(Q)||(tt.add(Q),x.delete(Q),c(Q,$)):x.has(Q)||(x.add(Q),tt.delete(Q),c(Q,$)),q||U(Q[o](vt),vt,tt,x,$,n))}}catch(Ce){it.e(Ce)}finally{it.f()}},E=new b(function(U){if(w.length){var mt=w.join(","),vt=new Set,tt=new Set,x=i(U),$;try{for(x.s();!($=x.n()).done;){var q=$.value,it=q.addedNodes,Pt=q.removedNodes;N(Pt,mt,vt,tt,s,s),N(it,mt,vt,tt,n,s)}}catch(Q){x.e(Q)}finally{x.f()}}}),_=E.observe;return(E.observe=function(U){return _.call(E,U,{subtree:n,childList:n})})(p),E},d="querySelectorAll",f=self,y=f.document,g=f.Element,v=f.MutationObserver,C=f.Set,I=f.WeakMap,k=function(c){return d in c},z=[].filter,M=function(h){var c=new I,p=function(x){for(var $=0,q=x.length;$<q;$++)c.delete(x[$])},b=function(){for(var x=mt.takeRecords(),$=0,q=x.length;$<q;$++)E(z.call(x[$].removedNodes,k),!1),E(z.call(x[$].addedNodes,k),!0)},w=function(x){return x.matches||x.webkitMatchesSelector||x.msMatchesSelector},N=function(x,$){var q;if($)for(var it,Pt=w(x),Q=0,Ce=_.length;Q<Ce;Q++)Pt.call(x,it=_[Q])&&(c.has(x)||c.set(x,new C),q=c.get(x),q.has(it)||(q.add(it),h.handle(x,$,it)));else c.has(x)&&(q=c.get(x),c.delete(x),q.forEach(function(Di){h.handle(x,$,Di)}))},E=function(x){for(var $=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,q=0,it=x.length;q<it;q++)N(x[q],$)},_=h.query,U=h.root||y,mt=u(N,U,v,_),vt=g.prototype.attachShadow;return vt&&(g.prototype.attachShadow=function(tt){var x=vt.call(this,tt);return mt.observe(x),x}),_.length&&E(U[d](_)),{drop:p,flush:b,observer:mt,parse:E}},P=self,F=P.document,j=P.Map,W=P.MutationObserver,H=P.Object,Y=P.Set,K=P.WeakMap,L=P.Element,et=P.HTMLElement,O=P.Node,S=P.Error,a=P.TypeError,l=P.Reflect,m=H.defineProperty,A=H.keys,T=H.getOwnPropertyNames,D=H.setPrototypeOf,R=!self.customElements,G=function(c){for(var p=A(c),b=[],w=new Y,N=p.length,E=0;E<N;E++){b[E]=c[p[E]];try{delete c[p[E]]}catch{w.add(E)}}return function(){for(var _=0;_<N;_++)w.has(_)||(c[p[_]]=b[_])}};if(R){var B=function(){var c=this.constructor;if(!ct.has(c))throw new a("Illegal constructor");var p=ct.get(c);if(Jt)return nr(Jt,p);var b=st.call(F,p);return nr(D(b,c.prototype),p)},st=F.createElement,ct=new j,rt=new j,ht=new j,pt=new j,Dt=[],Ct=function(c,p,b){var w=ht.get(b);if(p&&!w.isPrototypeOf(c)){var N=G(c);Jt=D(c,w);try{new w.constructor}finally{Jt=null,N()}}var E="".concat(p?"":"dis","connectedCallback");E in w&&c[E]()},gi=M({query:Dt,handle:Ct}),yi=gi.parse,Jt=null,Ne=function(c){if(!rt.has(c)){var p,b=new Promise(function(w){p=w});rt.set(c,{$:b,_:p})}return rt.get(c).$},nr=e(Ne,W);self.customElements={define:function(c,p){if(pt.has(c))throw new S('the name "'.concat(c,'" has already been used with this registry'));ct.set(p,c),ht.set(c,p.prototype),pt.set(c,p),Dt.push(c),Ne(c).then(function(){yi(F.querySelectorAll(c))}),rt.get(c)._(p)},get:function(c){return pt.get(c)},whenDefined:Ne},m(B.prototype=et.prototype,"constructor",{value:B}),self.HTMLElement=B,F.createElement=function(h,c){var p=c&&c.is,b=p?pt.get(p):pt.get(h);return b?new b:st.call(F,h)},"isConnected"in O.prototype||m(O.prototype,"isConnected",{configurable:!0,get:function(){return!(this.ownerDocument.compareDocumentPosition(this)&this.DOCUMENT_POSITION_DISCONNECTED)}})}else if(R=!self.customElements.get("extends-br"),R)try{var or=function h(){return self.Reflect.construct(HTMLBRElement,[],h)};or.prototype=HTMLLIElement.prototype;var sr="extends-br";self.customElements.define("extends-br",or,{extends:"br"}),R=F.createElement("br",{is:sr}).outerHTML.indexOf(sr)<0;var ar=self.customElements,bi=ar.get,wi=ar.whenDefined;self.customElements.whenDefined=function(h){var c=this;return wi.call(this,h).then(function(p){return p||bi.call(c,h)})}}catch{}if(R){var lr=function(c){var p=De.get(c);hr(p.querySelectorAll(this),c.isConnected)},at=self.customElements,cr=F.createElement,Ei=at.define,xi=at.get,Si=at.upgrade,Oi=l||{construct:function(c){return c.call(this)}},Ti=Oi.construct,De=new K,Pe=new Y,te=new j,ee=new j,ur=new j,re=new j,fr=[],ie=[],dr=function(c){return re.get(c)||xi.call(at,c)},Ai=function(c,p,b){var w=ur.get(b);if(p&&!w.isPrototypeOf(c)){var N=G(c);ne=D(c,w);try{new w.constructor}finally{ne=null,N()}}var E="".concat(p?"":"dis","connectedCallback");E in w&&c[E]()},Ii=M({query:ie,handle:Ai}),hr=Ii.parse,_i=M({query:fr,handle:function(c,p){De.has(c)&&(p?Pe.add(c):Pe.delete(c),ie.length&&lr.call(ie,c))}}),Ni=_i.parse,pr=L.prototype.attachShadow;pr&&(L.prototype.attachShadow=function(h){var c=pr.call(this,h);return De.set(this,c),c});var Fe=function(c){if(!ee.has(c)){var p,b=new Promise(function(w){p=w});ee.set(c,{$:b,_:p})}return ee.get(c).$},Le=e(Fe,W),ne=null;T(self).filter(function(h){return/^HTML.*Element$/.test(h)}).forEach(function(h){var c=self[h];function p(){var b=this.constructor;if(!te.has(b))throw new a("Illegal constructor");var w=te.get(b),N=w.is,E=w.tag;if(N){if(ne)return Le(ne,N);var _=cr.call(F,E);return _.setAttribute("is",N),Le(D(_,b.prototype),N)}else return Ti.call(this,c,[],b)}m(p.prototype=c.prototype,"constructor",{value:p}),m(self,h,{value:p})}),F.createElement=function(h,c){var p=c&&c.is;if(p){var b=re.get(p);if(b&&te.get(b).tag===h)return new b}var w=cr.call(F,h);return p&&w.setAttribute("is",p),w},at.get=dr,at.whenDefined=Fe,at.upgrade=function(h){var c=h.getAttribute("is");if(c){var p=re.get(c);if(p){Le(D(h,p.prototype),c);return}}Si.call(at,h)},at.define=function(h,c,p){if(dr(h))throw new S("'".concat(h,"' has already been defined as a custom element"));var b,w=p&&p.extends;te.set(c,w?{is:h,tag:w}:{is:"",tag:h}),w?(b="".concat(w,'[is="').concat(h,'"]'),ur.set(b,c.prototype),re.set(h,c),ie.push(b)):(Ei.apply(at,arguments),fr.push(b=h)),Fe(h).then(function(){w?(hr(F.querySelectorAll(b)),Pe.forEach(lr,[b])):Ni(F.querySelectorAll(b))}),ee.get(h)._(c)}}})()});var oe=null,vr,gr,yr,br=65,Me,Rt,mr=new Set,wr=1111;$i();function $i(){if(!document.createElement("link").relList.supports("prefetch"))return;let t="instantVaryAccept"in document.body.dataset||"Shopify"in window,r=navigator.userAgent.indexOf("Chrome/");if(r>-1&&(oe=parseInt(navigator.userAgent.substring(r+7))),t&&oe&&oe<110)return;let i="instantMousedownShortcut"in document.body.dataset;vr="instantAllowQueryString"in document.body.dataset,gr="instantAllowExternalLinks"in document.body.dataset,yr="instantWhitelist"in document.body.dataset;let n={capture:!0,passive:!0},s=!1,o=!1,u=!1;if("instantIntensity"in document.body.dataset){let d=document.body.dataset.instantIntensity;if(d.startsWith("mousedown"))s=!0,d=="mousedown-only"&&(o=!0);else if(d.startsWith("viewport")){let f=navigator.connection&&navigator.connection.saveData,y=navigator.connection&&navigator.connection.effectiveType&&navigator.connection.effectiveType.includes("2g");!f&&!y&&(d=="viewport"?document.documentElement.clientWidth*document.documentElement.clientHeight<45e4&&(u=!0):d=="viewport-all"&&(u=!0))}else{let f=parseInt(d);isNaN(f)||(br=f)}}if(o||document.addEventListener("touchstart",Wi,n),s?i||document.addEventListener("mousedown",Bi,n):document.addEventListener("mouseover",Vi,n),i&&document.addEventListener("mousedown",Ki,n),u){let d=window.requestIdleCallback;d||(d=f=>{f()}),d(function(){let y=new IntersectionObserver(g=>{g.forEach(v=>{if(v.isIntersecting){let C=v.target;y.unobserve(C),ae(C.href)}})});document.querySelectorAll("a").forEach(g=>{se(g)&&y.observe(g)})},{timeout:1500})}}function Wi(e){Me=performance.now();let t=e.target.closest("a");se(t)&&ae(t.href,"high")}function Vi(e){if(performance.now()-Me<wr||!("closest"in e.target))return;let t=e.target.closest("a");se(t)&&(t.addEventListener("mouseout",Hi,{passive:!0}),Rt=setTimeout(()=>{ae(t.href,"high"),Rt=void 0},br))}function Bi(e){let t=e.target.closest("a");se(t)&&ae(t.href,"high")}function Hi(e){e.relatedTarget&&e.target.closest("a")==e.relatedTarget.closest("a")||Rt&&(clearTimeout(Rt),Rt=void 0)}function Ki(e){if(performance.now()-Me<wr)return;let t=e.target.closest("a");if(e.which>1||e.metaKey||e.ctrlKey||!t)return;t.addEventListener("click",function(i){i.detail!=1337&&i.preventDefault()},{capture:!0,passive:!1,once:!0});let r=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1,detail:1337});t.dispatchEvent(r)}function se(e){if(!(!e||!e.href)&&!(yr&&!("instant"in e.dataset))&&!(e.origin!=location.origin&&(!(gr||"instant"in e.dataset)||!oe))&&["http:","https:"].includes(e.protocol)&&!(e.protocol=="http:"&&location.protocol=="https:")&&!(!vr&&e.search&&!("instant"in e.dataset))&&!(e.hash&&e.pathname+e.search==location.pathname+location.search)&&!("noInstant"in e.dataset))return!0}function ae(e,t="auto"){if(mr.has(e))return;let r=document.createElement("link");r.rel="prefetch",r.href=e,r.fetchPriority=t,r.as="document",document.head.appendChild(r),mr.add(e)}var Rr={};ki(Rr,{createFocusTrap:()=>vn});var xr=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],le=xr.join(","),Sr=typeof Element>"u",Ot=Sr?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,ce=!Sr&&Element.prototype.getRootNode?function(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}:function(e){return e?.ownerDocument},ue=function e(t,r){var i;r===void 0&&(r=!0);var n=t==null||(i=t.getAttribute)===null||i===void 0?void 0:i.call(t,"inert"),s=n===""||n==="true",o=s||r&&t&&e(t.parentNode);return o},Ui=function(t){var r,i=t==null||(r=t.getAttribute)===null||r===void 0?void 0:r.call(t,"contenteditable");return i===""||i==="true"},Or=function(t,r,i){if(ue(t))return[];var n=Array.prototype.slice.apply(t.querySelectorAll(le));return r&&Ot.call(t,le)&&n.unshift(t),n=n.filter(i),n},Tr=function e(t,r,i){for(var n=[],s=Array.from(t);s.length;){var o=s.shift();if(!ue(o,!1))if(o.tagName==="SLOT"){var u=o.assignedElements(),d=u.length?u:o.children,f=e(d,!0,i);i.flatten?n.push.apply(n,f):n.push({scopeParent:o,candidates:f})}else{var y=Ot.call(o,le);y&&i.filter(o)&&(r||!t.includes(o))&&n.push(o);var g=o.shadowRoot||typeof i.getShadowRoot=="function"&&i.getShadowRoot(o),v=!ue(g,!1)&&(!i.shadowRootFilter||i.shadowRootFilter(o));if(g&&v){var C=e(g===!0?o.children:g.children,!0,i);i.flatten?n.push.apply(n,C):n.push({scopeParent:o,candidates:C})}else s.unshift.apply(s,o.children)}}return n},Ar=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},gt=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||Ui(t))&&!Ar(t)?0:t.tabIndex},qi=function(t,r){var i=gt(t);return i<0&&r&&!Ar(t)?0:i},Gi=function(t,r){return t.tabIndex===r.tabIndex?t.documentOrder-r.documentOrder:t.tabIndex-r.tabIndex},Ir=function(t){return t.tagName==="INPUT"},Zi=function(t){return Ir(t)&&t.type==="hidden"},Xi=function(t){var r=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(i){return i.tagName==="SUMMARY"});return r},Yi=function(t,r){for(var i=0;i<t.length;i++)if(t[i].checked&&t[i].form===r)return t[i]},Qi=function(t){if(!t.name)return!0;var r=t.form||ce(t),i=function(u){return r.querySelectorAll('input[type="radio"][name="'+u+'"]')},n;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")n=i(window.CSS.escape(t.name));else try{n=i(t.name)}catch(o){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",o.message),!1}var s=Yi(n,t.form);return!s||s===t},Ji=function(t){return Ir(t)&&t.type==="radio"},tn=function(t){return Ji(t)&&!Qi(t)},en=function(t){var r,i=t&&ce(t),n=(r=i)===null||r===void 0?void 0:r.host,s=!1;if(i&&i!==t){var o,u,d;for(s=!!((o=n)!==null&&o!==void 0&&(u=o.ownerDocument)!==null&&u!==void 0&&u.contains(n)||t!=null&&(d=t.ownerDocument)!==null&&d!==void 0&&d.contains(t));!s&&n;){var f,y,g;i=ce(n),n=(f=i)===null||f===void 0?void 0:f.host,s=!!((y=n)!==null&&y!==void 0&&(g=y.ownerDocument)!==null&&g!==void 0&&g.contains(n))}}return s},Er=function(t){var r=t.getBoundingClientRect(),i=r.width,n=r.height;return i===0&&n===0},rn=function(t,r){var i=r.displayCheck,n=r.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var s=Ot.call(t,"details>summary:first-of-type"),o=s?t.parentElement:t;if(Ot.call(o,"details:not([open]) *"))return!0;if(!i||i==="full"||i==="legacy-full"){if(typeof n=="function"){for(var u=t;t;){var d=t.parentElement,f=ce(t);if(d&&!d.shadowRoot&&n(d)===!0)return Er(t);t.assignedSlot?t=t.assignedSlot:!d&&f!==t.ownerDocument?t=f.host:t=d}t=u}if(en(t))return!t.getClientRects().length;if(i!=="legacy-full")return!0}else if(i==="non-zero-area")return Er(t);return!1},nn=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var r=t.parentElement;r;){if(r.tagName==="FIELDSET"&&r.disabled){for(var i=0;i<r.children.length;i++){var n=r.children.item(i);if(n.tagName==="LEGEND")return Ot.call(r,"fieldset[disabled] *")?!0:!n.contains(t)}return!0}r=r.parentElement}return!1},fe=function(t,r){return!(r.disabled||ue(r)||Zi(r)||rn(r,t)||Xi(r)||nn(r))},ke=function(t,r){return!(tn(r)||gt(r)<0||!fe(t,r))},on=function(t){var r=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(r)||r>=0)},sn=function e(t){var r=[],i=[];return t.forEach(function(n,s){var o=!!n.scopeParent,u=o?n.scopeParent:n,d=qi(u,o),f=o?e(n.candidates):u;d===0?o?r.push.apply(r,f):r.push(u):i.push({documentOrder:s,tabIndex:d,item:n,isScope:o,content:f})}),i.sort(Gi).reduce(function(n,s){return s.isScope?n.push.apply(n,s.content):n.push(s.content),n},[]).concat(r)},_r=function(t,r){r=r||{};var i;return r.getShadowRoot?i=Tr([t],r.includeContainer,{filter:ke.bind(null,r),flatten:!1,getShadowRoot:r.getShadowRoot,shadowRootFilter:on}):i=Or(t,r.includeContainer,ke.bind(null,r)),sn(i)},Nr=function(t,r){r=r||{};var i;return r.getShadowRoot?i=Tr([t],r.includeContainer,{filter:fe.bind(null,r),flatten:!0,getShadowRoot:r.getShadowRoot}):i=Or(t,r.includeContainer,fe.bind(null,r)),i},Tt=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return Ot.call(t,le)===!1?!1:ke(r,t)},an=xr.concat("iframe").join(","),de=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return Ot.call(t,an)===!1?!1:fe(r,t)};function ln(e,t,r){return(t=un(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Dr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,i)}return r}function Pr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dr(Object(r),!0).forEach(function(i){ln(e,i,r[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dr(Object(r)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(r,i))})}return e}function cn(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var i=r.call(e,t||"default");if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function un(e){var t=cn(e,"string");return typeof t=="symbol"?t:t+""}var Fr={activateTrap:function(t,r){if(t.length>0){var i=t[t.length-1];i!==r&&i.pause()}var n=t.indexOf(r);n===-1||t.splice(n,1),t.push(r)},deactivateTrap:function(t,r){var i=t.indexOf(r);i!==-1&&t.splice(i,1),t.length>0&&t[t.length-1].unpause()}},fn=function(t){return t.tagName&&t.tagName.toLowerCase()==="input"&&typeof t.select=="function"},dn=function(t){return t?.key==="Escape"||t?.key==="Esc"||t?.keyCode===27},kt=function(t){return t?.key==="Tab"||t?.keyCode===9},hn=function(t){return kt(t)&&!t.shiftKey},pn=function(t){return kt(t)&&t.shiftKey},Lr=function(t){return setTimeout(t,0)},Cr=function(t,r){var i=-1;return t.every(function(n,s){return r(n)?(i=s,!1):!0}),i},Mt=function(t){for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return typeof t=="function"?t.apply(void 0,i):t},he=function(t){return t.target.shadowRoot&&typeof t.composedPath=="function"?t.composedPath()[0]:t.target},mn=[],vn=function(t,r){var i=r?.document||document,n=r?.trapStack||mn,s=Pr({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:hn,isKeyBackward:pn},r),o={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},u,d=function(a,l,m){return a&&a[l]!==void 0?a[l]:s[m||l]},f=function(a,l){var m=typeof l?.composedPath=="function"?l.composedPath():void 0;return o.containerGroups.findIndex(function(A){var T=A.container,D=A.tabbableNodes;return T.contains(a)||m?.includes(T)||D.find(function(R){return R===a})})},y=function(a){var l=s[a];if(typeof l=="function"){for(var m=arguments.length,A=new Array(m>1?m-1:0),T=1;T<m;T++)A[T-1]=arguments[T];l=l.apply(void 0,A)}if(l===!0&&(l=void 0),!l){if(l===void 0||l===!1)return l;throw new Error("`".concat(a,"` was specified but was not a node, or did not return a node"))}var D=l;if(typeof l=="string"&&(D=i.querySelector(l),!D))throw new Error("`".concat(a,"` as selector refers to no known node"));return D},g=function(){var a=y("initialFocus");if(a===!1)return!1;if(a===void 0||!de(a,s.tabbableOptions))if(f(i.activeElement)>=0)a=i.activeElement;else{var l=o.tabbableGroups[0],m=l&&l.firstTabbableNode;a=m||y("fallbackFocus")}if(!a)throw new Error("Your focus-trap needs to have at least one focusable element");return a},v=function(){if(o.containerGroups=o.containers.map(function(a){var l=_r(a,s.tabbableOptions),m=Nr(a,s.tabbableOptions),A=l.length>0?l[0]:void 0,T=l.length>0?l[l.length-1]:void 0,D=m.find(function(B){return Tt(B)}),R=m.slice().reverse().find(function(B){return Tt(B)}),G=!!l.find(function(B){return gt(B)>0});return{container:a,tabbableNodes:l,focusableNodes:m,posTabIndexesFound:G,firstTabbableNode:A,lastTabbableNode:T,firstDomTabbableNode:D,lastDomTabbableNode:R,nextTabbableNode:function(st){var ct=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,rt=l.indexOf(st);return rt<0?ct?m.slice(m.indexOf(st)+1).find(function(ht){return Tt(ht)}):m.slice(0,m.indexOf(st)).reverse().find(function(ht){return Tt(ht)}):l[rt+(ct?1:-1)]}}}),o.tabbableGroups=o.containerGroups.filter(function(a){return a.tabbableNodes.length>0}),o.tabbableGroups.length<=0&&!y("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(o.containerGroups.find(function(a){return a.posTabIndexesFound})&&o.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},C=function(a){var l=a.activeElement;if(l)return l.shadowRoot&&l.shadowRoot.activeElement!==null?C(l.shadowRoot):l},I=function(a){if(a!==!1&&a!==C(document)){if(!a||!a.focus){I(g());return}a.focus({preventScroll:!!s.preventScroll}),o.mostRecentlyFocusedNode=a,fn(a)&&a.select()}},k=function(a){var l=y("setReturnFocus",a);return l||(l===!1?!1:a)},z=function(a){var l=a.target,m=a.event,A=a.isBackward,T=A===void 0?!1:A;l=l||he(m),v();var D=null;if(o.tabbableGroups.length>0){var R=f(l,m),G=R>=0?o.containerGroups[R]:void 0;if(R<0)T?D=o.tabbableGroups[o.tabbableGroups.length-1].lastTabbableNode:D=o.tabbableGroups[0].firstTabbableNode;else if(T){var B=Cr(o.tabbableGroups,function(Dt){var Ct=Dt.firstTabbableNode;return l===Ct});if(B<0&&(G.container===l||de(l,s.tabbableOptions)&&!Tt(l,s.tabbableOptions)&&!G.nextTabbableNode(l,!1))&&(B=R),B>=0){var st=B===0?o.tabbableGroups.length-1:B-1,ct=o.tabbableGroups[st];D=gt(l)>=0?ct.lastTabbableNode:ct.lastDomTabbableNode}else kt(m)||(D=G.nextTabbableNode(l,!1))}else{var rt=Cr(o.tabbableGroups,function(Dt){var Ct=Dt.lastTabbableNode;return l===Ct});if(rt<0&&(G.container===l||de(l,s.tabbableOptions)&&!Tt(l,s.tabbableOptions)&&!G.nextTabbableNode(l))&&(rt=R),rt>=0){var ht=rt===o.tabbableGroups.length-1?0:rt+1,pt=o.tabbableGroups[ht];D=gt(l)>=0?pt.firstTabbableNode:pt.firstDomTabbableNode}else kt(m)||(D=G.nextTabbableNode(l))}}else D=y("fallbackFocus");return D},M=function(a){var l=he(a);if(!(f(l,a)>=0)){if(Mt(s.clickOutsideDeactivates,a)){u.deactivate({returnFocus:s.returnFocusOnDeactivate});return}Mt(s.allowOutsideClick,a)||a.preventDefault()}},P=function(a){var l=he(a),m=f(l,a)>=0;if(m||l instanceof Document)m&&(o.mostRecentlyFocusedNode=l);else{a.stopImmediatePropagation();var A,T=!0;if(o.mostRecentlyFocusedNode)if(gt(o.mostRecentlyFocusedNode)>0){var D=f(o.mostRecentlyFocusedNode),R=o.containerGroups[D].tabbableNodes;if(R.length>0){var G=R.findIndex(function(B){return B===o.mostRecentlyFocusedNode});G>=0&&(s.isKeyForward(o.recentNavEvent)?G+1<R.length&&(A=R[G+1],T=!1):G-1>=0&&(A=R[G-1],T=!1))}}else o.containerGroups.some(function(B){return B.tabbableNodes.some(function(st){return gt(st)>0})})||(T=!1);else T=!1;T&&(A=z({target:o.mostRecentlyFocusedNode,isBackward:s.isKeyBackward(o.recentNavEvent)})),I(A||o.mostRecentlyFocusedNode||g())}o.recentNavEvent=void 0},F=function(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;o.recentNavEvent=a;var m=z({event:a,isBackward:l});m&&(kt(a)&&a.preventDefault(),I(m))},j=function(a){(s.isKeyForward(a)||s.isKeyBackward(a))&&F(a,s.isKeyBackward(a))},W=function(a){dn(a)&&Mt(s.escapeDeactivates,a)!==!1&&(a.preventDefault(),u.deactivate())},H=function(a){var l=he(a);f(l,a)>=0||Mt(s.clickOutsideDeactivates,a)||Mt(s.allowOutsideClick,a)||(a.preventDefault(),a.stopImmediatePropagation())},Y=function(){if(o.active)return Fr.activateTrap(n,u),o.delayInitialFocusTimer=s.delayInitialFocus?Lr(function(){I(g())}):I(g()),i.addEventListener("focusin",P,!0),i.addEventListener("mousedown",M,{capture:!0,passive:!1}),i.addEventListener("touchstart",M,{capture:!0,passive:!1}),i.addEventListener("click",H,{capture:!0,passive:!1}),i.addEventListener("keydown",j,{capture:!0,passive:!1}),i.addEventListener("keydown",W),u},K=function(){if(o.active)return i.removeEventListener("focusin",P,!0),i.removeEventListener("mousedown",M,!0),i.removeEventListener("touchstart",M,!0),i.removeEventListener("click",H,!0),i.removeEventListener("keydown",j,!0),i.removeEventListener("keydown",W),u},L=function(a){var l=a.some(function(m){var A=Array.from(m.removedNodes);return A.some(function(T){return T===o.mostRecentlyFocusedNode})});l&&I(g())},et=typeof window<"u"&&"MutationObserver"in window?new MutationObserver(L):void 0,O=function(){et&&(et.disconnect(),o.active&&!o.paused&&o.containers.map(function(a){et.observe(a,{subtree:!0,childList:!0})}))};return u={get active(){return o.active},get paused(){return o.paused},activate:function(a){if(o.active)return this;var l=d(a,"onActivate"),m=d(a,"onPostActivate"),A=d(a,"checkCanFocusTrap");A||v(),o.active=!0,o.paused=!1,o.nodeFocusedBeforeActivation=i.activeElement,l?.();var T=function(){A&&v(),Y(),O(),m?.()};return A?(A(o.containers.concat()).then(T,T),this):(T(),this)},deactivate:function(a){if(!o.active)return this;var l=Pr({onDeactivate:s.onDeactivate,onPostDeactivate:s.onPostDeactivate,checkCanReturnFocus:s.checkCanReturnFocus},a);clearTimeout(o.delayInitialFocusTimer),o.delayInitialFocusTimer=void 0,K(),o.active=!1,o.paused=!1,O(),Fr.deactivateTrap(n,u);var m=d(l,"onDeactivate"),A=d(l,"onPostDeactivate"),T=d(l,"checkCanReturnFocus"),D=d(l,"returnFocus","returnFocusOnDeactivate");m?.();var R=function(){Lr(function(){D&&I(k(o.nodeFocusedBeforeActivation)),A?.()})};return D&&T?(T(k(o.nodeFocusedBeforeActivation)).then(R,R),this):(R(),this)},pause:function(a){if(o.paused||!o.active)return this;var l=d(a,"onPause"),m=d(a,"onPostPause");return o.paused=!0,l?.(),K(),O(),m?.(),this},unpause:function(a){if(!o.paused||!o.active)return this;var l=d(a,"onUnpause"),m=d(a,"onPostUnpause");return o.paused=!1,l?.(),v(),Y(),O(),m?.(),this},updateContainerElements:function(a){var l=[].concat(a).filter(Boolean);return o.containers=l.map(function(m){return typeof m=="string"?i.querySelector(m):m}),o.active&&v(),O(),this}},u.updateContainerElements(t),u};var ql=ji(Mr());function ze(e,t){e.indexOf(t)===-1&&e.push(t)}function je(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}var zt=(e,t,r)=>Math.min(Math.max(r,e),t);var V={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"};var J=e=>typeof e=="number";var lt=e=>Array.isArray(e)&&!J(e[0]);var kr=(e,t,r)=>{let i=t-e;return((r-e)%i+i)%i+e};function jt(e,t){return lt(e)?e[kr(0,e.length,t)]:e}var At=(e,t,r)=>-r*e+r*t+e;var $t=()=>{},X=e=>e;var ot=(e,t,r)=>t-e===0?1:(r-e)/(t-e);function Ft(e,t){let r=e[e.length-1];for(let i=1;i<=t;i++){let n=ot(0,t,i);e.push(At(r,1,n))}}function It(e){let t=[0];return Ft(t,e-1),t}function Wt(e,t=It(e.length),r=X){let i=e.length,n=i-t.length;return n>0&&Ft(t,n),s=>{let o=0;for(;o<i-2&&!(s<t[o+1]);o++);let u=zt(0,1,ot(t[o],t[o+1],s));return u=jt(r,o)(u),At(e[o],e[o+1],u)}}var Vt=e=>Array.isArray(e)&&J(e[0]);var yt=e=>typeof e=="object"&&!!e.createAnimation;var Z=e=>typeof e=="function";var ut=e=>typeof e=="string";var bt={ms:e=>e*1e3,s:e=>e/1e3};function $e(e,t){return t?e*(1e3/t):0}var zr=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e,gn=1e-7,yn=12;function bn(e,t,r,i,n){let s,o,u=0;do o=t+(r-t)/2,s=zr(o,i,n)-e,s>0?r=o:t=o;while(Math.abs(s)>gn&&++u<yn);return o}function _t(e,t,r,i){if(e===t&&r===i)return X;let n=s=>bn(s,0,1,e,r);return s=>s===0||s===1?s:zr(n(s),t,i)}var We=(e,t="end")=>r=>{r=t==="end"?Math.min(r,.999):Math.max(r,.001);let i=r*e,n=t==="end"?Math.floor(i):Math.ceil(i);return zt(0,1,n/e)};var wn={ease:_t(.25,.1,.25,1),"ease-in":_t(.42,0,1,1),"ease-in-out":_t(.42,0,.58,1),"ease-out":_t(0,0,.58,1)},En=/\((.*?)\)/;function Lt(e){if(Z(e))return e;if(Vt(e))return _t(...e);let t=wn[e];if(t)return t;if(e.startsWith("steps")){let r=En.exec(e);if(r){let i=r[1].split(",");return We(parseFloat(i[0]),i[1].trim())}}return X}var ft=class{constructor(t,r=[0,1],{easing:i,duration:n=V.duration,delay:s=V.delay,endDelay:o=V.endDelay,repeat:u=V.repeat,offset:d,direction:f="normal",autoplay:y=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=X,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise((v,C)=>{this.resolve=v,this.reject=C}),i=i||V.easing,yt(i)){let v=i.createAnimation(r);i=v.easing,r=v.keyframes||r,n=v.duration||n}this.repeat=u,this.easing=lt(i)?X:Lt(i),this.updateDuration(n);let g=Wt(r,d,lt(i)?i.map(Lt):X);this.tick=v=>{var C;s=s;let I=0;this.pauseTime!==void 0?I=this.pauseTime:I=(v-this.startTime)*this.rate,this.t=I,I/=1e3,I=Math.max(I-s,0),this.playState==="finished"&&this.pauseTime===void 0&&(I=this.totalDuration);let k=I/this.duration,z=Math.floor(k),M=k%1;!M&&k>=1&&(M=1),M===1&&z--;let P=z%2;(f==="reverse"||f==="alternate"&&P||f==="alternate-reverse"&&!P)&&(M=1-M);let F=I>=this.totalDuration?1:Math.min(M,1),j=g(this.easing(F));t(j),this.pauseTime===void 0&&(this.playState==="finished"||I>=this.totalDuration+o)?(this.playState="finished",(C=this.resolve)===null||C===void 0||C.call(this,j)):this.playState!=="idle"&&(this.frameRequestId=requestAnimationFrame(this.tick))},y&&this.play()}play(){let t=performance.now();this.playState="running",this.pauseTime!==void 0?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",this.frameRequestId!==void 0&&cancelAnimationFrame(this.frameRequestId),(t=this.reject)===null||t===void 0||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){this.pauseTime!==void 0||this.rate===0?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}};var Bt=function(){};var Ht=class{setAnimation(t){this.animation=t,t?.finished.then(()=>this.clearAnimation()).catch(()=>{})}clearAnimation(){this.animation=this.generator=void 0}};var Ve=new WeakMap;function pe(e){return Ve.has(e)||Ve.set(e,{transforms:[],values:new Map}),Ve.get(e)}function jr(e,t){return e.has(t)||e.set(t,new Ht),e.get(t)}var xn=["","X","Y","Z"],Sn=["translate","scale","rotate","skew"],Kt={x:"translateX",y:"translateY",z:"translateZ"},$r={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:e=>e+"deg"},On={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:e=>e+"px"},rotate:$r,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:X},skew:$r},wt=new Map,ve=e=>`--motion-${e}`,me=["x","y","z"];Sn.forEach(e=>{xn.forEach(t=>{me.push(e+t),wt.set(ve(e+t),On[e])})});var Tn=(e,t)=>me.indexOf(e)-me.indexOf(t),An=new Set(me),ge=e=>An.has(e),Wr=(e,t)=>{Kt[t]&&(t=Kt[t]);let{transforms:r}=pe(e);ze(r,t),e.style.transform=In(r)},In=e=>e.sort(Tn).reduce(_n,"").trim(),_n=(e,t)=>`${e} ${t}(var(${ve(t)}))`;var Ut=e=>e.startsWith("--"),Vr=new Set;function Br(e){if(!Vr.has(e)){Vr.add(e);try{let{syntax:t,initialValue:r}=wt.has(e)?wt.get(e):{};CSS.registerProperty({name:e,inherits:!1,syntax:t,initialValue:r})}catch{}}}var Be=(e,t)=>document.createElement("div").animate(e,t),Hr={cssRegisterProperty:()=>typeof CSS<"u"&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{Be({opacity:[1]})}catch{return!1}return!0},finished:()=>!!Be({opacity:[0,1]},{duration:.001}).finished,linearEasing:()=>{try{Be({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0}},He={},Et={};for(let e in Hr)Et[e]=()=>(He[e]===void 0&&(He[e]=Hr[e]()),He[e]);var Nn=.015,Dn=(e,t)=>{let r="",i=Math.round(t/Nn);for(let n=0;n<i;n++)r+=e(ot(0,i-1,n))+", ";return r.substring(0,r.length-2)},Ke=(e,t)=>Z(e)?Et.linearEasing()?`linear(${Dn(e,t)})`:V.easing:Vt(e)?Pn(e):e,Pn=([e,t,r,i])=>`cubic-bezier(${e}, ${t}, ${r}, ${i})`;function Kr(e,t){for(let r=0;r<e.length;r++)e[r]===null&&(e[r]=r?e[r-1]:t());return e}var ye=e=>Array.isArray(e)?e:[e];function qt(e){return Kt[e]&&(e=Kt[e]),ge(e)?ve(e):e}var Gt={get:(e,t)=>{t=qt(t);let r=Ut(t)?e.style.getPropertyValue(t):getComputedStyle(e)[t];if(!r&&r!==0){let i=wt.get(t);i&&(r=i.initialValue)}return r},set:(e,t,r)=>{t=qt(t),Ut(t)?e.style.setProperty(t,r):e.style[t]=r}};function be(e,t=!0){if(!(!e||e.playState==="finished"))try{e.stop?e.stop():(t&&e.commitStyles(),e.cancel())}catch{}}function Ur(e,t){var r;let i=t?.toDefaultUnit||X,n=e[e.length-1];if(ut(n)){let s=((r=n.match(/(-?[\d.]+)([a-z%]*)/))===null||r===void 0?void 0:r[2])||"";s&&(i=o=>o+s)}return i}function Fn(){return window.__MOTION_DEV_TOOLS_RECORD}function we(e,t,r,i={},n){let s=Fn(),o=i.record!==!1&&s,u,{duration:d=V.duration,delay:f=V.delay,endDelay:y=V.endDelay,repeat:g=V.repeat,easing:v=V.easing,persist:C=!1,direction:I,offset:k,allowWebkitAcceleration:z=!1,autoplay:M=!0}=i,P=pe(e),F=ge(t),j=Et.waapi();F&&Wr(e,t);let W=qt(t),H=jr(P.values,W),Y=wt.get(W);return be(H.animation,!(yt(v)&&H.generator)&&i.record!==!1),()=>{let K=()=>{var O,S;return(S=(O=Gt.get(e,W))!==null&&O!==void 0?O:Y?.initialValue)!==null&&S!==void 0?S:0},L=Kr(ye(r),K),et=Ur(L,Y);if(yt(v)){let O=v.createAnimation(L,t!=="opacity",K,W,H);v=O.easing,L=O.keyframes||L,d=O.duration||d}if(Ut(W)&&(Et.cssRegisterProperty()?Br(W):j=!1),F&&!Et.linearEasing()&&(Z(v)||lt(v)&&v.some(Z))&&(j=!1),j){Y&&(L=L.map(a=>J(a)?Y.toDefaultUnit(a):a)),L.length===1&&(!Et.partialKeyframes()||o)&&L.unshift(K());let O={delay:bt.ms(f),duration:bt.ms(d),endDelay:bt.ms(y),easing:lt(v)?void 0:Ke(v,d),direction:I,iterations:g+1,fill:"both"};u=e.animate({[W]:L,offset:k,easing:lt(v)?v.map(a=>Ke(a,d)):void 0},O),u.finished||(u.finished=new Promise((a,l)=>{u.onfinish=a,u.oncancel=l}));let S=L[L.length-1];u.finished.then(()=>{C||(Gt.set(e,W,S),u.cancel())}).catch($t),z||(u.playbackRate=1.000001)}else if(n&&F)L=L.map(O=>typeof O=="string"?parseFloat(O):O),L.length===1&&L.unshift(parseFloat(K())),u=new n(O=>{Gt.set(e,W,et?et(O):O)},L,Object.assign(Object.assign({},i),{duration:d,easing:v}));else{let O=L[L.length-1];Gt.set(e,W,Y&&J(O)?Y.toDefaultUnit(O):O)}return o&&s(e,t,L,{duration:d,delay:f,easing:v,repeat:g,offset:k},"motion-one"),H.setAnimation(u),u&&!M&&u.pause(),u}}var Ee=(e,t)=>e[t]?Object.assign(Object.assign({},e),e[t]):Object.assign({},e);function xt(e,t){var r;return typeof e=="string"?t?((r=t[e])!==null&&r!==void 0||(t[e]=document.querySelectorAll(e)),e=t[e]):e=document.querySelectorAll(e):e instanceof Element&&(e=[e]),Array.from(e||[])}var Ln=e=>e(),Nt=(e,t,r=V.duration)=>new Proxy({animations:e.map(Ln).filter(Boolean),duration:r,options:t},Rn),Cn=e=>e.animations[0],Rn={get:(e,t)=>{let r=Cn(e);switch(t){case"duration":return e.duration;case"currentTime":return bt.s(r?.[t]||0);case"playbackRate":case"playState":return r?.[t];case"finished":return e.finished||(e.finished=Promise.all(e.animations.map(Mn)).catch($t)),e.finished;case"stop":return()=>{e.animations.forEach(i=>be(i))};case"forEachNative":return i=>{e.animations.forEach(n=>i(n,e))};default:return typeof r?.[t]>"u"?void 0:()=>e.animations.forEach(i=>i[t]())}},set:(e,t,r)=>{switch(t){case"currentTime":r=bt.ms(r);case"playbackRate":for(let i=0;i<e.animations.length;i++)e.animations[i][t]=r;return!0}return!1}},Mn=e=>e.finished;function qr(e=.1,{start:t=0,from:r=0,easing:i}={}){return(n,s)=>{let o=J(r)?r:kn(r,s),u=Math.abs(o-n),d=e*u;if(i){let f=s*e;d=Lt(i)(d/f)*f}return t+d}}function kn(e,t){if(e==="first")return 0;{let r=t-1;return e==="last"?r:r/2}}function xe(e,t,r){return Z(e)?e(t,r):e}function Gr(e){return function(r,i,n={}){r=xt(r);let s=r.length;Bt(!!s,"No valid element provided."),Bt(!!i,"No keyframes defined.");let o=[];for(let u=0;u<s;u++){let d=r[u];for(let f in i){let y=Ee(n,f);y.delay=xe(y.delay,u,s);let g=we(d,f,i[f],y,e);o.push(g)}}return Nt(o,n,n.duration)}}var Ue=Gr(ft);function Se(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r}function qe(e,t,r,i){var n;return J(t)?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):t==="<"?r:(n=i.get(t))!==null&&n!==void 0?n:e}function zn(e,t,r){for(let i=0;i<e.length;i++){let n=e[i];n.at>t&&n.at<r&&(je(e,n),i--)}}function Zr(e,t,r,i,n,s){zn(e,n,s);for(let o=0;o<t.length;o++)e.push({value:t[o],at:At(n,s,i[o]),easing:jt(r,o)})}function Xr(e,t){return e.at===t.at?e.value===null?1:-1:e.at-t.at}function Yr(e,t={}){var r;let i=jn(e,t),n=i.map(s=>we(...s,ft)).filter(Boolean);return Nt(n,t,(r=i[0])===null||r===void 0?void 0:r[3].duration)}function jn(e,t={}){var{defaultOptions:r={}}=t,i=Se(t,["defaultOptions"]);let n=[],s=new Map,o={},u=new Map,d=0,f=0,y=0;for(let g=0;g<e.length;g++){let v=e[g];if(ut(v)){u.set(v,f);continue}else if(!Array.isArray(v)){u.set(v.name,qe(f,v.at,d,u));continue}let[C,I,k={}]=v;k.at!==void 0&&(f=qe(f,k.at,d,u));let z=0,M=xt(C,o),P=M.length;for(let F=0;F<P;F++){let j=M[F],W=$n(j,s);for(let H in I){let Y=Wn(H,W),K=ye(I[H]),L=Ee(k,H),{duration:et=r.duration||V.duration,easing:O=r.easing||V.easing}=L;if(yt(O)){Bt(H==="opacity"||K.length>1,"spring must be provided 2 keyframes within timeline()");let T=O.createAnimation(K,H!=="opacity",()=>0,H);O=T.easing,K=T.keyframes||K,et=T.duration||et}let S=xe(k.delay,F,P)||0,a=f+S,l=a+et,{offset:m=It(K.length)}=L;m.length===1&&m[0]===0&&(m[1]=1);let A=m.length-K.length;A>0&&Ft(m,A),K.length===1&&K.unshift(null),Zr(Y,K,O,m,a,l),z=Math.max(S+et,z),y=Math.max(l,y)}}d=f,f+=z}return s.forEach((g,v)=>{for(let C in g){let I=g[C];I.sort(Xr);let k=[],z=[],M=[];for(let P=0;P<I.length;P++){let{at:F,value:j,easing:W}=I[P];k.push(j),z.push(ot(0,y,F)),M.push(W||V.easing)}z[0]!==0&&(z.unshift(0),k.unshift(k[0]),M.unshift("linear")),z[z.length-1]!==1&&(z.push(1),k.push(null)),n.push([v,C,k,Object.assign(Object.assign(Object.assign({},r),{duration:y,easing:M,offset:z}),i)])}}),n}function $n(e,t){return!t.has(e)&&t.set(e,{}),t.get(e)}function Wn(e,t){return t[e]||(t[e]=[]),t[e]}var Vn={any:0,all:1};function Qr(e,t,{root:r,margin:i,amount:n="any"}={}){if(typeof IntersectionObserver>"u")return()=>{};let s=xt(e),o=new WeakMap,u=f=>{f.forEach(y=>{let g=o.get(y.target);if(y.isIntersecting!==!!g)if(y.isIntersecting){let v=t(y);Z(v)?o.set(y.target,v):d.unobserve(y.target)}else g&&(g(y),o.delete(y.target))})},d=new IntersectionObserver(u,{root:r,rootMargin:i,threshold:typeof n=="number"?n:Vn[n]});return s.forEach(f=>d.observe(f)),()=>d.disconnect()}var Oe=new WeakMap,St;function Bn(e,t){if(t){let{inlineSize:r,blockSize:i}=t[0];return{width:r,height:i}}else return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}function Hn({target:e,contentRect:t,borderBoxSize:r}){var i;(i=Oe.get(e))===null||i===void 0||i.forEach(n=>{n({target:e,contentSize:t,get size(){return Bn(e,r)}})})}function Kn(e){e.forEach(Hn)}function Un(){typeof ResizeObserver>"u"||(St=new ResizeObserver(Kn))}function Jr(e,t){St||Un();let r=xt(e);return r.forEach(i=>{let n=Oe.get(i);n||(n=new Set,Oe.set(i,n)),n.add(t),St?.observe(i)}),()=>{r.forEach(i=>{let n=Oe.get(i);n?.delete(t),n?.size||St?.unobserve(i)})}}var Te=new Set,Zt;function qn(){Zt=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};Te.forEach(r=>r(t))},window.addEventListener("resize",Zt)}function ti(e){return Te.add(e),Zt||qn(),()=>{Te.delete(e),!Te.size&&Zt&&(Zt=void 0)}}function ei(e,t){return Z(e)?ti(e):Jr(e,t)}var Gn=50,ri=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),ni=()=>({time:0,x:ri(),y:ri()}),Zn={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ii(e,t,r,i){let n=r[t],{length:s,position:o}=Zn[t],u=n.current,d=r.time;n.current=e[`scroll${o}`],n.scrollLength=e[`scroll${s}`]-e[`client${s}`],n.offset.length=0,n.offset[0]=0,n.offset[1]=n.scrollLength,n.progress=ot(0,n.scrollLength,n.current);let f=i-d;n.velocity=f>Gn?0:$e(n.current-u,f)}function oi(e,t,r){ii(e,"x",t,r),ii(e,"y",t,r),t.time=r}function si(e,t){let r={x:0,y:0},i=e;for(;i&&i!==t;)if(i instanceof HTMLElement)r.x+=i.offsetLeft,r.y+=i.offsetTop,i=i.offsetParent;else if(i instanceof SVGGraphicsElement&&"getBBox"in i){let{top:n,left:s}=i.getBBox();for(r.x+=s,r.y+=n;i&&i.tagName!=="svg";)i=i.parentNode}return r}var Ae={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]};var Ie={start:0,center:.5,end:1};function Ge(e,t,r=0){let i=0;if(Ie[e]!==void 0&&(e=Ie[e]),ut(e)){let n=parseFloat(e);e.endsWith("px")?i=n:e.endsWith("%")?e=n/100:e.endsWith("vw")?i=n/100*document.documentElement.clientWidth:e.endsWith("vh")?i=n/100*document.documentElement.clientHeight:e=n}return J(e)&&(i=t*e),r+i}var Xn=[0,0];function ai(e,t,r,i){let n=Array.isArray(e)?e:Xn,s=0,o=0;return J(e)?n=[e,e]:ut(e)&&(e=e.trim(),e.includes(" ")?n=e.split(" "):n=[e,Ie[e]?e:"0"]),s=Ge(n[0],r,i),o=Ge(n[1],t),s-o}var Yn={x:0,y:0};function li(e,t,r){let{offset:i=Ae.All}=r,{target:n=e,axis:s="y"}=r,o=s==="y"?"height":"width",u=n!==e?si(n,e):Yn,d=n===e?{width:e.scrollWidth,height:e.scrollHeight}:{width:n.clientWidth,height:n.clientHeight},f={width:e.clientWidth,height:e.clientHeight};t[s].offset.length=0;let y=!t[s].interpolate,g=i.length;for(let v=0;v<g;v++){let C=ai(i[v],f[o],d[o],u[s]);!y&&C!==t[s].interpolatorOffsets[v]&&(y=!0),t[s].offset[v]=C}y&&(t[s].interpolate=Wt(It(g),t[s].offset),t[s].interpolatorOffsets=[...t[s].offset]),t[s].progress=t[s].interpolate(t[s].current)}function Qn(e,t=e,r){if(r.x.targetOffset=0,r.y.targetOffset=0,t!==e){let i=t;for(;i&&i!=e;)r.x.targetOffset+=i.offsetLeft,r.y.targetOffset+=i.offsetTop,i=i.offsetParent}r.x.targetLength=t===e?t.scrollWidth:t.clientWidth,r.y.targetLength=t===e?t.scrollHeight:t.clientHeight,r.x.containerLength=e.clientWidth,r.y.containerLength=e.clientHeight}function ci(e,t,r,i={}){let n=i.axis||"y";return{measure:()=>Qn(e,i.target,r),update:s=>{oi(e,r,s),(i.offset||i.target)&&li(e,r,i)},notify:Z(t)?()=>t(r):Jn(t,r[n])}}function Jn(e,t){return e.pause(),e.forEachNative((r,{easing:i})=>{var n,s;if(r.updateDuration)i||(r.easing=X),r.updateDuration(1);else{let o={duration:1e3};i||(o.easing="linear"),(s=(n=r.effect)===null||n===void 0?void 0:n.updateTiming)===null||s===void 0||s.call(n,o)}}),()=>{e.currentTime=t.progress}}var Xt=new WeakMap,ui=new WeakMap,Ze=new WeakMap,fi=e=>e===document.documentElement?window:e;function di(e,t={}){var{container:r=document.documentElement}=t,i=Se(t,["container"]);let n=Ze.get(r);n||(n=new Set,Ze.set(r,n));let s=ni(),o=ci(r,e,s,i);if(n.add(o),!Xt.has(r)){let f=()=>{let g=performance.now();for(let v of n)v.measure();for(let v of n)v.update(g);for(let v of n)v.notify()};Xt.set(r,f);let y=fi(r);window.addEventListener("resize",f,{passive:!0}),r!==document.documentElement&&ui.set(r,ei(r,f)),y.addEventListener("scroll",f,{passive:!0})}let u=Xt.get(r),d=requestAnimationFrame(u);return()=>{var f;typeof e!="function"&&e.stop(),cancelAnimationFrame(d);let y=Ze.get(r);if(!y||(y.delete(o),y.size))return;let g=Xt.get(r);Xt.delete(r),g&&(fi(r).removeEventListener("scroll",g),(f=ui.get(r))===null||f===void 0||f(),window.removeEventListener("resize",g))}}function to(e,t={}){return Nt([()=>{let r=new ft(e,[0,1],t);return r.finished.catch(()=>{}),r}],t,t.duration)}function hi(e,t,r){return(Z(e)?to:Ue)(e,t,r)}function dt(e){this.listenerMap=[{},{}],e&&this.root(e),this.handle=dt.prototype.handle.bind(this),this._removedListeners=[]}dt.prototype.root=function(e){let t=this.listenerMap,r;if(this.rootElement){for(r in t[1])t[1].hasOwnProperty(r)&&this.rootElement.removeEventListener(r,this.handle,!0);for(r in t[0])t[0].hasOwnProperty(r)&&this.rootElement.removeEventListener(r,this.handle,!1)}if(!e||!e.addEventListener)return this.rootElement&&delete this.rootElement,this;this.rootElement=e;for(r in t[1])t[1].hasOwnProperty(r)&&this.rootElement.addEventListener(r,this.handle,!0);for(r in t[0])t[0].hasOwnProperty(r)&&this.rootElement.addEventListener(r,this.handle,!1);return this};dt.prototype.captureForType=function(e){return["blur","error","focus","load","resize","scroll"].indexOf(e)!==-1};dt.prototype.on=function(e,t,r,i){let n,s,o,u;if(!e)throw new TypeError("Invalid event type: "+e);if(typeof t=="function"&&(i=r,r=t,t=null),i===void 0&&(i=this.captureForType(e)),typeof r!="function")throw new TypeError("Handler must be a type of Function");return n=this.rootElement,s=this.listenerMap[i?1:0],s[e]||(n&&n.addEventListener(e,this.handle,i),s[e]=[]),t?/^[a-z]+$/i.test(t)?(u=t,o=eo):/^#[a-z0-9\-_]+$/i.test(t)?(u=t.slice(1),o=io):(u=t,o=Element.prototype.matches):(u=null,o=ro.bind(this)),s[e].push({selector:t,handler:r,matcher:o,matcherParam:u}),this};dt.prototype.off=function(e,t,r,i){let n,s,o,u,d;if(typeof t=="function"&&(i=r,r=t,t=null),i===void 0)return this.off(e,t,r,!0),this.off(e,t,r,!1),this;if(o=this.listenerMap[i?1:0],!e){for(d in o)o.hasOwnProperty(d)&&this.off(d,t,r);return this}if(u=o[e],!u||!u.length)return this;for(n=u.length-1;n>=0;n--)s=u[n],(!t||t===s.selector)&&(!r||r===s.handler)&&(this._removedListeners.push(s),u.splice(n,1));return u.length||(delete o[e],this.rootElement&&this.rootElement.removeEventListener(e,this.handle,i)),this};dt.prototype.handle=function(e){let t,r,i=e.type,n,s,o,u,d=[],f,y="ftLabsDelegateIgnore";if(e[y]===!0)return;switch(f=e.target,f.nodeType===3&&(f=f.parentNode),f.correspondingUseElement&&(f=f.correspondingUseElement),n=this.rootElement,s=e.eventPhase||(e.target!==e.currentTarget?3:2),s){case 1:d=this.listenerMap[1][i];break;case 2:this.listenerMap[0]&&this.listenerMap[0][i]&&(d=d.concat(this.listenerMap[0][i])),this.listenerMap[1]&&this.listenerMap[1][i]&&(d=d.concat(this.listenerMap[1][i]));break;case 3:d=this.listenerMap[0][i];break}let g=[];for(r=d.length;f&&r;){for(t=0;t<r&&(o=d[t],!!o);t++)f.tagName&&["button","input","select","textarea"].indexOf(f.tagName.toLowerCase())>-1&&f.hasAttribute("disabled")?g=[]:o.matcher.call(f,o.matcherParam,f)&&g.push([e,f,o]);if(f===n||(r=d.length,f=f.parentElement||f.parentNode,f instanceof HTMLDocument))break}let v;for(t=0;t<g.length;t++)if(!(this._removedListeners.indexOf(g[t][2])>-1)&&(u=this.fire.apply(this,g[t]),u===!1)){g[t][0][y]=!0,g[t][0].preventDefault(),v=!1;break}return v};dt.prototype.fire=function(e,t,r){return r.handler.call(t,e,t)};function eo(e,t){return e.toLowerCase()===t.tagName.toLowerCase()}function ro(e,t){return this.rootElement===window?t===document||t===document.documentElement||t===window:this.rootElement===t}function io(e,t){return e===t.id}dt.prototype.destroy=function(){this.off(),this.root()};var no=dt;function Yt(e,t,r){let i=document.createElement(t);return e&&(i.className=e),r&&r.appendChild(i),i}function oo(e,t,r){let i=`translate3d(${e}px,${t||0}px,0)`;return r!==void 0&&(i+=` scale3d(${r},${r},1)`),i}function Xe(e,t,r){e.style.width=typeof t=="number"?`${t}px`:t,e.style.height=typeof r=="number"?`${r}px`:r}var nt={IDLE:"idle",LOADING:"loading",LOADED:"loaded",ERROR:"error"};function so(e){return"button"in e&&e.button===1||e.ctrlKey||e.metaKey||e.altKey||e.shiftKey}function Qt(e,t,r=document){let i=[];if(e instanceof Element)i=[e];else if(e instanceof NodeList||Array.isArray(e))i=Array.from(e);else{let n=typeof e=="string"?e:t;n&&(i=Array.from(r.querySelectorAll(n)))}return i}function ao(e){return typeof e=="function"&&e.prototype&&e.prototype.goTo}function pi(){return!!(navigator.vendor&&navigator.vendor.match(/apple/i))}var Ye=class{constructor(t,r){this.type=t,this.defaultPrevented=!1,r&&Object.assign(this,r)}preventDefault(){this.defaultPrevented=!0}},Qe=class{constructor(){this._listeners={},this._filters={},this.pswp=void 0,this.options=void 0}addFilter(t,r,i=100){var n,s,o;this._filters[t]||(this._filters[t]=[]),(n=this._filters[t])===null||n===void 0||n.push({fn:r,priority:i}),(s=this._filters[t])===null||s===void 0||s.sort((u,d)=>u.priority-d.priority),(o=this.pswp)===null||o===void 0||o.addFilter(t,r,i)}removeFilter(t,r){this._filters[t]&&(this._filters[t]=this._filters[t].filter(i=>i.fn!==r)),this.pswp&&this.pswp.removeFilter(t,r)}applyFilters(t,...r){var i;return(i=this._filters[t])===null||i===void 0||i.forEach(n=>{r[0]=n.fn.apply(this,r)}),r[0]}on(t,r){var i,n;this._listeners[t]||(this._listeners[t]=[]),(i=this._listeners[t])===null||i===void 0||i.push(r),(n=this.pswp)===null||n===void 0||n.on(t,r)}off(t,r){var i;this._listeners[t]&&(this._listeners[t]=this._listeners[t].filter(n=>r!==n)),(i=this.pswp)===null||i===void 0||i.off(t,r)}dispatch(t,r){var i;if(this.pswp)return this.pswp.dispatch(t,r);let n=new Ye(t,r);return(i=this._listeners[t])===null||i===void 0||i.forEach(s=>{s.call(this,n)}),n}},Je=class{constructor(t,r){if(this.element=Yt("pswp__img pswp__img--placeholder",t?"img":"div",r),t){let i=this.element;i.decoding="async",i.alt="",i.src=t,i.setAttribute("role","presentation")}this.element.setAttribute("aria-hidden","true")}setDisplayedSize(t,r){this.element&&(this.element.tagName==="IMG"?(Xe(this.element,250,"auto"),this.element.style.transformOrigin="0 0",this.element.style.transform=oo(0,0,t/250)):Xe(this.element,t,r))}destroy(){var t;(t=this.element)!==null&&t!==void 0&&t.parentNode&&this.element.remove(),this.element=null}},tr=class{constructor(t,r,i){this.instance=r,this.data=t,this.index=i,this.element=void 0,this.placeholder=void 0,this.slide=void 0,this.displayedImageWidth=0,this.displayedImageHeight=0,this.width=Number(this.data.w)||Number(this.data.width)||0,this.height=Number(this.data.h)||Number(this.data.height)||0,this.isAttached=!1,this.hasSlide=!1,this.isDecoding=!1,this.state=nt.IDLE,this.data.type?this.type=this.data.type:this.data.src?this.type="image":this.type="html",this.instance.dispatch("contentInit",{content:this})}removePlaceholder(){this.placeholder&&!this.keepPlaceholder()&&setTimeout(()=>{this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0)},1e3)}load(t,r){if(this.slide&&this.usePlaceholder())if(this.placeholder){let i=this.placeholder.element;i&&!i.parentElement&&this.slide.container.prepend(i)}else{let i=this.instance.applyFilters("placeholderSrc",this.data.msrc&&this.slide.isFirstSlide?this.data.msrc:!1,this);this.placeholder=new Je(i,this.slide.container)}this.element&&!r||this.instance.dispatch("contentLoad",{content:this,isLazy:t}).defaultPrevented||(this.isImageContent()?(this.element=Yt("pswp__img","img"),this.displayedImageWidth&&this.loadImage(t)):(this.element=Yt("pswp__content","div"),this.element.innerHTML=this.data.html||""),r&&this.slide&&this.slide.updateContentSize(!0))}loadImage(t){var r,i;if(!this.isImageContent()||!this.element||this.instance.dispatch("contentLoadImage",{content:this,isLazy:t}).defaultPrevented)return;let n=this.element;this.updateSrcsetSizes(),this.data.srcset&&(n.srcset=this.data.srcset),n.src=(r=this.data.src)!==null&&r!==void 0?r:"",n.alt=(i=this.data.alt)!==null&&i!==void 0?i:"",this.state=nt.LOADING,n.complete?this.onLoaded():(n.onload=()=>{this.onLoaded()},n.onerror=()=>{this.onError()})}setSlide(t){this.slide=t,this.hasSlide=!0,this.instance=t.pswp}onLoaded(){this.state=nt.LOADED,this.slide&&this.element&&(this.instance.dispatch("loadComplete",{slide:this.slide,content:this}),this.slide.isActive&&this.slide.heavyAppended&&!this.element.parentNode&&(this.append(),this.slide.updateContentSize(!0)),(this.state===nt.LOADED||this.state===nt.ERROR)&&this.removePlaceholder())}onError(){this.state=nt.ERROR,this.slide&&(this.displayError(),this.instance.dispatch("loadComplete",{slide:this.slide,isError:!0,content:this}),this.instance.dispatch("loadError",{slide:this.slide,content:this}))}isLoading(){return this.instance.applyFilters("isContentLoading",this.state===nt.LOADING,this)}isError(){return this.state===nt.ERROR}isImageContent(){return this.type==="image"}setDisplayedSize(t,r){if(this.element&&(this.placeholder&&this.placeholder.setDisplayedSize(t,r),!this.instance.dispatch("contentResize",{content:this,width:t,height:r}).defaultPrevented&&(Xe(this.element,t,r),this.isImageContent()&&!this.isError()))){let i=!this.displayedImageWidth&&t;this.displayedImageWidth=t,this.displayedImageHeight=r,i?this.loadImage(!1):this.updateSrcsetSizes(),this.slide&&this.instance.dispatch("imageSizeChange",{slide:this.slide,width:t,height:r,content:this})}}isZoomable(){return this.instance.applyFilters("isContentZoomable",this.isImageContent()&&this.state!==nt.ERROR,this)}updateSrcsetSizes(){if(!this.isImageContent()||!this.element||!this.data.srcset)return;let t=this.element,r=this.instance.applyFilters("srcsetSizesWidth",this.displayedImageWidth,this);(!t.dataset.largestUsedSize||r>parseInt(t.dataset.largestUsedSize,10))&&(t.sizes=r+"px",t.dataset.largestUsedSize=String(r))}usePlaceholder(){return this.instance.applyFilters("useContentPlaceholder",this.isImageContent(),this)}lazyLoad(){this.instance.dispatch("contentLazyLoad",{content:this}).defaultPrevented||this.load(!0)}keepPlaceholder(){return this.instance.applyFilters("isKeepingPlaceholder",this.isLoading(),this)}destroy(){this.hasSlide=!1,this.slide=void 0,!this.instance.dispatch("contentDestroy",{content:this}).defaultPrevented&&(this.remove(),this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0),this.isImageContent()&&this.element&&(this.element.onload=null,this.element.onerror=null,this.element=void 0))}displayError(){if(this.slide){var t,r;let i=Yt("pswp__error-msg","div");i.innerText=(t=(r=this.instance.options)===null||r===void 0?void 0:r.errorMsg)!==null&&t!==void 0?t:"",i=this.instance.applyFilters("contentErrorElement",i,this),this.element=Yt("pswp__content pswp__error-msg-container","div"),this.element.appendChild(i),this.slide.container.innerText="",this.slide.container.appendChild(this.element),this.slide.updateContentSize(!0),this.removePlaceholder()}}append(){if(this.isAttached||!this.element)return;if(this.isAttached=!0,this.state===nt.ERROR){this.displayError();return}if(this.instance.dispatch("contentAppend",{content:this}).defaultPrevented)return;let t="decode"in this.element;this.isImageContent()?t&&this.slide&&(!this.slide.isActive||pi())?(this.isDecoding=!0,this.element.decode().catch(()=>{}).finally(()=>{this.isDecoding=!1,this.appendImage()})):this.appendImage():this.slide&&!this.element.parentNode&&this.slide.container.appendChild(this.element)}activate(){this.instance.dispatch("contentActivate",{content:this}).defaultPrevented||!this.slide||(this.isImageContent()&&this.isDecoding&&!pi()?this.appendImage():this.isError()&&this.load(!1,!0),this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","false"))}deactivate(){this.instance.dispatch("contentDeactivate",{content:this}),this.slide&&this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","true")}remove(){this.isAttached=!1,!this.instance.dispatch("contentRemove",{content:this}).defaultPrevented&&(this.element&&this.element.parentNode&&this.element.remove(),this.placeholder&&this.placeholder.element&&this.placeholder.element.remove())}appendImage(){this.isAttached&&(this.instance.dispatch("contentAppendImage",{content:this}).defaultPrevented||(this.slide&&this.element&&!this.element.parentNode&&this.slide.container.appendChild(this.element),(this.state===nt.LOADED||this.state===nt.ERROR)&&this.removePlaceholder()))}};function lo(e,t){if(e.getViewportSizeFn){let r=e.getViewportSizeFn(e,t);if(r)return r}return{x:document.documentElement.clientWidth,y:window.innerHeight}}function _e(e,t,r,i,n){let s=0;if(t.paddingFn)s=t.paddingFn(r,i,n)[e];else if(t.padding)s=t.padding[e];else{let o="padding"+e[0].toUpperCase()+e.slice(1);t[o]&&(s=t[o])}return Number(s)||0}function co(e,t,r,i){return{x:t.x-_e("left",e,t,r,i)-_e("right",e,t,r,i),y:t.y-_e("top",e,t,r,i)-_e("bottom",e,t,r,i)}}var mi=4e3,er=class{constructor(t,r,i,n){this.pswp=n,this.options=t,this.itemData=r,this.index=i,this.panAreaSize=null,this.elementSize=null,this.fit=1,this.fill=1,this.vFill=1,this.initial=1,this.secondary=1,this.max=1,this.min=1}update(t,r,i){let n={x:t,y:r};this.elementSize=n,this.panAreaSize=i;let s=i.x/n.x,o=i.y/n.y;this.fit=Math.min(1,s<o?s:o),this.fill=Math.min(1,s>o?s:o),this.vFill=Math.min(1,o),this.initial=this._getInitial(),this.secondary=this._getSecondary(),this.max=Math.max(this.initial,this.secondary,this._getMax()),this.min=Math.min(this.fit,this.initial,this.secondary),this.pswp&&this.pswp.dispatch("zoomLevelsUpdate",{zoomLevels:this,slideData:this.itemData})}_parseZoomLevelOption(t){let r=t+"ZoomLevel",i=this.options[r];if(i)return typeof i=="function"?i(this):i==="fill"?this.fill:i==="fit"?this.fit:Number(i)}_getSecondary(){let t=this._parseZoomLevelOption("secondary");return t||(t=Math.min(1,this.fit*3),this.elementSize&&t*this.elementSize.x>mi&&(t=mi/this.elementSize.x),t)}_getInitial(){return this._parseZoomLevelOption("initial")||this.fit}_getMax(){return this._parseZoomLevelOption("max")||Math.max(1,this.fit*4)}};function vi(e,t,r){let i=t.createContentFromData(e,r),n,{options:s}=t;if(s){n=new er(s,e,-1);let o;t.pswp?o=t.pswp.viewportSize:o=lo(s,t);let u=co(s,o,e,r);n.update(i.width,i.height,u)}return i.lazyLoad(),n&&i.setDisplayedSize(Math.ceil(i.width*n.initial),Math.ceil(i.height*n.initial)),i}function uo(e,t){let r=t.getItemData(e);if(!t.dispatch("lazyLoadSlide",{index:e,itemData:r}).defaultPrevented)return vi(r,t,e)}var rr=class extends Qe{getNumItems(){var t;let r=0,i=(t=this.options)===null||t===void 0?void 0:t.dataSource;i&&"length"in i?r=i.length:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),i.items&&(r=i.items.length));let n=this.dispatch("numItems",{dataSource:i,numItems:r});return this.applyFilters("numItems",n.numItems,i)}createContentFromData(t,r){return new tr(t,this,r)}getItemData(t){var r;let i=(r=this.options)===null||r===void 0?void 0:r.dataSource,n={};Array.isArray(i)?n=i[t]:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),n=i.items[t]);let s=n;s instanceof Element&&(s=this._domElementToItemData(s));let o=this.dispatch("itemData",{itemData:s||{},index:t});return this.applyFilters("itemData",o.itemData,t)}_getGalleryDOMElements(t){var r,i;return(r=this.options)!==null&&r!==void 0&&r.children||(i=this.options)!==null&&i!==void 0&&i.childSelector?Qt(this.options.children,this.options.childSelector,t)||[]:[t]}_domElementToItemData(t){let r={element:t},i=t.tagName==="A"?t:t.querySelector("a");if(i){r.src=i.dataset.pswpSrc||i.href,i.dataset.pswpSrcset&&(r.srcset=i.dataset.pswpSrcset),r.width=i.dataset.pswpWidth?parseInt(i.dataset.pswpWidth,10):0,r.height=i.dataset.pswpHeight?parseInt(i.dataset.pswpHeight,10):0,r.w=r.width,r.h=r.height,i.dataset.pswpType&&(r.type=i.dataset.pswpType);let s=t.querySelector("img");if(s){var n;r.msrc=s.currentSrc||s.src,r.alt=(n=s.getAttribute("alt"))!==null&&n!==void 0?n:""}(i.dataset.pswpCropped||i.dataset.cropped)&&(r.thumbCropped=!0)}return this.applyFilters("domItemData",r,t,i)}lazyLoadData(t,r){return vi(t,this,r)}},ir=class extends rr{constructor(t){super(),this.options=t||{},this._uid=0,this.shouldOpen=!1,this._preloadedContent=void 0,this.onThumbnailsClick=this.onThumbnailsClick.bind(this)}init(){Qt(this.options.gallery,this.options.gallerySelector).forEach(t=>{t.addEventListener("click",this.onThumbnailsClick,!1)})}onThumbnailsClick(t){if(so(t)||window.pswp)return;let r={x:t.clientX,y:t.clientY};!r.x&&!r.y&&(r=null);let i=this.getClickedIndex(t);i=this.applyFilters("clickedIndex",i,t,this);let n={gallery:t.currentTarget};i>=0&&(t.preventDefault(),this.loadAndOpen(i,n,r))}getClickedIndex(t){if(this.options.getClickedIndexFn)return this.options.getClickedIndexFn.call(this,t);let r=t.target,n=Qt(this.options.children,this.options.childSelector,t.currentTarget).findIndex(s=>s===r||s.contains(r));return n!==-1?n:this.options.children||this.options.childSelector?-1:0}loadAndOpen(t,r,i){if(window.pswp||!this.options)return!1;if(!r&&this.options.gallery&&this.options.children){let n=Qt(this.options.gallery);n[0]&&(r={gallery:n[0]})}return this.options.index=t,this.options.initialPointerPos=i,this.shouldOpen=!0,this.preload(t,r),!0}preload(t,r){let{options:i}=this;r&&(i.dataSource=r);let n=[],s=typeof i.pswpModule;if(ao(i.pswpModule))n.push(Promise.resolve(i.pswpModule));else{if(s==="string")throw new Error("pswpModule as string is no longer supported");if(s==="function")n.push(i.pswpModule());else throw new Error("pswpModule is not valid")}typeof i.openPromise=="function"&&n.push(i.openPromise()),i.preloadFirstSlide!==!1&&t>=0&&(this._preloadedContent=uo(t,this));let o=++this._uid;Promise.all(n).then(u=>{if(this.shouldOpen){let d=u[0];this._openPhotoswipe(d,o)}})}_openPhotoswipe(t,r){if(r!==this._uid&&this.shouldOpen||(this.shouldOpen=!1,window.pswp))return;let i=typeof t=="object"?new t.default(this.options):new t(this.options);this.pswp=i,window.pswp=i,Object.keys(this._listeners).forEach(n=>{var s;(s=this._listeners[n])===null||s===void 0||s.forEach(o=>{i.on(n,o)})}),Object.keys(this._filters).forEach(n=>{var s;(s=this._filters[n])===null||s===void 0||s.forEach(o=>{i.addFilter(n,o.fn,o.priority)})}),this._preloadedContent&&(i.contentLoader.addToCache(this._preloadedContent),this._preloadedContent=void 0),i.on("destroy",()=>{this.pswp=void 0,delete window.pswp}),i.init()}destroy(){var t;(t=this.pswp)===null||t===void 0||t.destroy(),this.shouldOpen=!1,this._listeners={},Qt(this.options.gallery,this.options.gallerySelector).forEach(r=>{r.removeEventListener("click",this.onThumbnailsClick,!1)})}};(function(){Node.prototype.replaceChildren===void 0&&(Node.prototype.replaceChildren=e=>{for(;this.lastChild;)this.removeChild(this.lastChild);e!==void 0&&this.append(e)})})();(function(){let e=!1;if(document.createElement("i").addEventListener("click",()=>{},{get signal(){e=!0}}),e||!window.AbortController)return;let t=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(r,i,n){if(n&&n.signal){if(n.signal.aborted)return;n.signal.addEventListener("abort",()=>this.removeEventListener(r,i,{...n}))}return t.call(this,r,i,n)}})();export{ql as CustomElementsPolyfill,no as Delegate,Rr as FocusTrap,ir as PhotoSwipeLightbox,Ae as ScrollOffset,hi as animate,Qr as inView,di as scroll,qr as stagger,Yr as timeline};
/*! Bundled license information:

@ungap/custom-elements/index.js:
  (*! (c) Andrea Giammarchi @webreflection ISC *)
  (*! (c) Andrea Giammarchi - ISC *)

instant.page/instantpage.js:
  (*! instant.page v5.2.0 - (C) 2019-2023 Alexandre Dieulot - https://instant.page/license *)

tabbable/dist/index.esm.js:
  (*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)

focus-trap/dist/focus-trap.esm.js:
  (*!
  * focus-trap 7.6.0
  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
  *)

photoswipe/dist/photoswipe-lightbox.esm.js:
  (*!
    * PhotoSwipe Lightbox 5.4.4 - https://photoswipe.com
    * (c) 2024 Dmytro Semenov
    *)
*/
