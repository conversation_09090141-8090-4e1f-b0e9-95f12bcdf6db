/* Slider Navigation Arrows Positioning
 * This CSS positions the navigation arrows on the left and right sides of slider cards
 */

/* ===== Header Visibility Fixes with Cart Drawer ===== */

/* Ensure the header stays visible when cart drawer is open */
body.drawer-open store-header.header {
  visibility: visible !important;
  z-index: 998 !important; /* Below cart drawer but above other content */
}

/* Ensure the header doesn't get hidden by the cart drawer overlay */
.cart-drawer::part(overlay) {
  z-index: 998 !important; /* Below the cart drawer content but above other content */
}

/* Ensure the header doesn't disappear in a weird way */
#shopify-section-header store-header.header {
  transition: transform 0.3s ease-out !important;
  will-change: transform;
}

/* Prevent any unwanted animations on the header when cart drawer opens */
body.drawer-open #shopify-section-header {
  visibility: visible !important;
}

/* Ensure the header remains in place when cart drawer is open */
body.drawer-open #shopify-section-header store-header.header {
  transform: translateY(0) !important;
}

/* Hide the default arrow container that appears below the slider */
[class^="slider-arrow-"] {
  display: none !important;
}

/* Show the actual navigation arrows only for slider.liquid section */
.section-slider .luna-swiper-testimonials .swiper-button-next,
.section-slider .luna-swiper-testimonials .swiper-button-prev,
.section-slider .swiper-button-next,
.section-slider .swiper-button-prev {
  display: flex !important;
  position: absolute !important;
  top: 35% !important;
  transform: translateY(-50%) !important;
  width: 40px !important;
  height: 40px !important;
  margin: 0 !important;
  z-index: 10 !important;
  background-color: #2A2A2A !important; /* Dark background as per reference */
  border-radius: 50% !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
}

/* Add arrow content to navigation buttons */
.section-slider .luna-swiper-testimonials .swiper-button-next:after,
.section-slider .luna-swiper-testimonials .swiper-button-prev:after,
.section-slider .swiper-button-next:after,
.section-slider .swiper-button-prev:after {
  content: '' !important;
  display: block !important;
  width: 8px !important;
  height: 8px !important;
  border-style: solid !important;
  border-color: #E7E8E5 !important;
  border-width: 0 2px 2px 0 !important;
  transform: rotate(-45deg) !important;
  position: relative !important;
  left: -1px !important; /* Adjust horizontal position */
  top: 0px !important; /* Ensure vertical centering */
}

.section-slider .luna-swiper-testimonials .swiper-button-prev:after,
.section-slider .swiper-button-prev:after {
  transform: rotate(135deg) !important;
  left: 1px !important; /* Adjust horizontal position for prev button */
}

/* Position the navigation buttons */

/* Position the next button on the right */
.section-slider .luna-swiper-testimonials .swiper-button-next,
.section-slider .swiper-button-next {
  right: 5px !important;
  left: auto !important;
}

/* Position the prev button on the left */
.section-slider .luna-swiper-testimonials .swiper-button-prev,
.section-slider .swiper-button-prev {
  left: 5px !important;
  right: auto !important;
}

/* Hide SVG arrows since we're using CSS arrows */
.section-slider .luna-swiper-testimonials .swiper-button-next svg,
.section-slider .luna-swiper-testimonials .swiper-button-prev svg,
.section-slider .swiper-button-next svg,
.section-slider .swiper-button-prev svg {
  display: none !important;
}

/* Style the custom arrows to match the main arrows */
.section-slider .luna-swiper-testimonials .custom-slider-next,
.section-slider .luna-swiper-testimonials .custom-slider-prev,
.section-slider .custom-slider-next,
.section-slider .custom-slider-prev {
  display: flex !important;
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 40px !important;
  height: 40px !important;
  z-index: 10 !important;
  background-color: #2A2A2A !important;
  border-radius: 50% !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
}

/* Add arrow content to custom navigation buttons */
.luna-swiper-testimonials .custom-slider-next:after,
.luna-swiper-testimonials .custom-slider-prev:after,
.custom-slider-next:after,
.custom-slider-prev:after {
  content: '' !important;
  display: block !important;
  width: 8px !important;
  height: 8px !important;
  border-style: solid !important;
  border-color: #E7E8E5 !important;
  border-width: 0 2px 2px 0 !important;
  transform: rotate(-45deg) !important;
  position: relative !important;
  left: -1px !important; /* Adjust horizontal position */
  top: 0px !important; /* Ensure vertical centering */
}

.luna-swiper-testimonials .custom-slider-prev:after,
.custom-slider-prev:after {
  transform: rotate(135deg) !important;
  left: 1px !important; /* Adjust horizontal position for prev button */
}

/* Hide SVG arrows in custom buttons */
.section-slider .luna-swiper-testimonials .custom-slider-next svg,
.section-slider .luna-swiper-testimonials .custom-slider-prev svg,
.section-slider .custom-slider-next svg,
.section-slider .custom-slider-prev svg {
  display: none !important;
}

.section-slider .luna-swiper-testimonials .custom-slider-next,
.section-slider .custom-slider-next {
  right: 10px !important;
}

.section-slider .luna-swiper-testimonials .custom-slider-prev,
.section-slider .custom-slider-prev {
  left: 10px !important;
}

/* Make inactive slides smaller */
.luna-swiper-testimonials .swiper-slide {
  transform: scale(0.85) !important;
  transition: transform 0.3s ease !important;
  opacity: 0.7 !important;
}

/* Make active slide full size */
.luna-swiper-testimonials .swiper-slide-active {
  transform: scale(1) !important;
  opacity: 1 !important;
  z-index: 1 !important;
}

/* Hide text content on all slides by default */
.luna-swiper-testimonials .swiper-slide .lt-testimonial-info-slider {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.3s ease, visibility 0.3s ease !important;
}

/* Show text content only on active slide */
.luna-swiper-testimonials .swiper-slide-active .lt-testimonial-info-slider {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Responsive adjustments */
@media (max-width: 749px) {
  /* Hide navigation arrows on mobile */
  .section-slider .luna-swiper-testimonials .swiper-button-next,
  .section-slider .luna-swiper-testimonials .swiper-button-prev,
  .section-slider .luna-swiper-testimonials .custom-slider-next,
  .section-slider .luna-swiper-testimonials .custom-slider-prev,
  .section-slider .swiper-button-next,
  .section-slider .swiper-button-prev,
  .section-slider .custom-slider-next,
  .section-slider .custom-slider-prev {
    display: none !important; /* Hide arrows on mobile */
  }

  /* Make inactive slides more visible on mobile */
  .section-slider .luna-swiper-testimonials {
    padding: 0 !important; /* Remove padding to maximize space */
    overflow: visible !important; /* Allow overflow to show adjacent slides */
  }

  /* Adjust the swiper container to show adjacent slides */
  .section-slider .luna-swiper-testimonials .swiper-wrapper {
    overflow: visible !important;
  }

  /* Make the active slide larger on mobile */
  .section-slider .luna-swiper-testimonials .swiper-slide-active {
    transform: scale(1) !important; /* Full size for active slide */
    opacity: 1 !important;
  }

  /* Make inactive slides more visible */
  .section-slider .luna-swiper-testimonials .swiper-slide-prev,
  .section-slider .luna-swiper-testimonials .swiper-slide-next {
    opacity: 0.7 !important; /* Increased from 0.4 to 0.7 for better visibility */
    transform: scale(0.85) !important; /* Increased from 0.75 to 0.85 to make them larger */
  }

  /* Make images larger on mobile */
  .section-slider .luna-swiper-testimonials .image-item-wrapper-slider {
    height: auto !important;
    max-height: 70vh !important; /* Limit height to 70% of viewport height */
  }

  /* Ensure images fill their container */
  .section-slider .luna-swiper-testimonials .slider-image {
    object-fit: cover !important;
  }
}
