/* Judge.me Review Form Styling - Clean Design */

/* Main form container */
.jdgm-form {
  font-family: var(--heading-font-family) !important;
  background: #E7E8E5 !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin: 1.5rem 0 !important;
  border: 2px solid #2A2A2A !important;
  max-width: 100% !important;
  width: 100% !important;
}

/* Desktop form layout improvements */
@media screen and (min-width: 750px) {
  .jdgm-form {
    max-width: 1000px !important;
    padding: 2.5rem !important;
  }

  /* Create a grid layout for form fields on desktop */
  .jdgm-form .jdgm-form__body {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 1.5rem !important;
  }

  /* Make certain fields span full width */
  .jdgm-form .jdgm-form__field:has(textarea),
  .jdgm-form .jdgm-form__field[data-field="review_title"],
  .jdgm-form .jdgm-form__field[data-field="review_content"],
  .jdgm-form .jdgm-form__field:last-child {
    grid-column: 1 / -1 !important;
  }
}

/* Form title styling */
.jdgm-form__title,
.jdgm-form h3 {
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 1.75rem !important;
  color: #2A2A2A !important;
  margin-bottom: 1.5rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Form labels */
.jdgm-form label,
.jdgm-form .jdgm-form__label {
  font-family: var(--heading-font-family) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  color: #2A2A2A !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  text-transform: uppercase !important;
  letter-spacing: 0.3px !important;
}

/* Input fields */
.jdgm-form input[type="text"],
.jdgm-form input[type="email"],
.jdgm-form textarea,
.jdgm-form select {
  width: 100% !important;
  padding: 0.875rem 1rem !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  background: #FFFFFF !important;
  color: #2A2A2A !important;
  font-family: var(--text-font-family) !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
  margin-bottom: 1rem !important;
  box-sizing: border-box !important;
}

/* Input focus states */
.jdgm-form input[type="text"]:focus,
.jdgm-form input[type="email"]:focus,
.jdgm-form textarea:focus,
.jdgm-form select:focus {
  outline: none !important;
  border-color: #47DE47 !important;
  box-shadow: 0 0 0 3px rgba(71, 222, 71, 0.1) !important;
}

/* Textarea specific styling */
.jdgm-form textarea {
  min-height: 120px !important;
  resize: vertical !important;
  font-family: var(--text-font-family) !important;
}

/* Placeholder styling */
.jdgm-form input::placeholder,
.jdgm-form textarea::placeholder {
  color: #999999 !important;
  font-style: italic !important;
}

/* Star rating container - ONLY for forms */
.jdgm-form .jdgm-star-rating,
.jdgm-write-rev-form .jdgm-star-rating,
.jdgm-rev-form .jdgm-star-rating {
  margin: 1rem 0 !important;
}

/* Individual stars - ONLY for interactive form stars */
.jdgm-form .jdgm-star,
.jdgm-write-rev-form .jdgm-star,
.jdgm-rev-form .jdgm-star,
.jdgm-star-rating .jdgm-star {
  font-size: 2rem !important;
  color: #DDDDDD !important;
  cursor: pointer !important;
  transition: color 0.2s ease !important;
  margin-right: 0.25rem !important;
}

.jdgm-form .jdgm-star:hover,
.jdgm-form .jdgm-star.jdgm-star--hov,
.jdgm-form .jdgm-star.jdgm-star--sel,
.jdgm-write-rev-form .jdgm-star:hover,
.jdgm-write-rev-form .jdgm-star.jdgm-star--hov,
.jdgm-write-rev-form .jdgm-star.jdgm-star--sel,
.jdgm-rev-form .jdgm-star:hover,
.jdgm-rev-form .jdgm-star.jdgm-star--hov,
.jdgm-rev-form .jdgm-star.jdgm-star--sel,
.jdgm-star-rating .jdgm-star:hover,
.jdgm-star-rating .jdgm-star.jdgm-star--hov,
.jdgm-star-rating .jdgm-star.jdgm-star--sel {
  color: #47DE47 !important;
}

/* Form buttons */
.jdgm-form button,
.jdgm-form input[type="submit"],
.jdgm-form .jdgm-btn {
  background: #47DE47 !important;
  color: #2A2A2A !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  padding: 0.875rem 2rem !important;
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-top: 1rem !important;
  width: auto !important;
  display: inline-block !important;
}

/* Button hover states */
.jdgm-form button:hover,
.jdgm-form input[type="submit"]:hover,
.jdgm-form .jdgm-btn:hover {
  background: #2A2A2A !important;
  color: #47DE47 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(42, 42, 42, 0.2) !important;
}

/* File upload styling */
.jdgm-form input[type="file"] {
  border: 2px dashed #2A2A2A !important;
  border-radius: 8px !important;
  padding: 1.5rem !important;
  background: #F8F8F8 !important;
  text-align: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.jdgm-form input[type="file"]:hover {
  border-color: #47DE47 !important;
  background: #F0F8F0 !important;
}

/* Form sections spacing */
.jdgm-form .jdgm-form__group,
.jdgm-form .jdgm-form__field {
  margin-bottom: 1.5rem !important;
}

/* Error messages */
.jdgm-form .jdgm-form__error {
  color: #E74C3C !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
  font-family: var(--text-font-family) !important;
}

/* Success messages */
.jdgm-form .jdgm-form__success {
  color: #47DE47 !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
  font-family: var(--text-font-family) !important;
}

/* Character count */
.jdgm-form .jdgm-form__char-count {
  font-size: 0.75rem !important;
  color: #666666 !important;
  text-align: right !important;
  margin-top: 0.25rem !important;
  font-family: var(--text-font-family) !important;
}

/* Mobile responsive adjustments */
@media screen and (max-width: 749px) {
  .jdgm-form {
    padding: 1.5rem !important;
    margin: 1rem 0 !important;
    border-radius: 8px !important;
  }
  
  .jdgm-form__title,
  .jdgm-form h3 {
    font-size: 1.5rem !important;
    margin-bottom: 1rem !important;
  }
  
  .jdgm-star {
    font-size: 1.75rem !important;
    margin-right: 0.125rem !important;
  }
  
  .jdgm-form button,
  .jdgm-form input[type="submit"],
  .jdgm-form .jdgm-btn {
    width: 100% !important;
    padding: 1rem !important;
    font-size: 0.875rem !important;
  }
}

/* Additional form elements */
.jdgm-form .jdgm-checkbox,
.jdgm-form .jdgm-radio {
  margin: 0.5rem 0 !important;
}

.jdgm-form .jdgm-checkbox input,
.jdgm-form .jdgm-radio input {
  margin-right: 0.5rem !important;
  width: auto !important;
}

/* Form validation styling */
.jdgm-form .jdgm-form__field--invalid input,
.jdgm-form .jdgm-form__field--invalid textarea {
  border-color: #E74C3C !important;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

.jdgm-form .jdgm-form__field--valid input,
.jdgm-form .jdgm-form__field--valid textarea {
  border-color: #47DE47 !important;
  box-shadow: 0 0 0 3px rgba(71, 222, 71, 0.1) !important;
}

/* Additional Judge.me specific selectors */
.jdgm-write-rev-form,
.jdgm-rev-form {
  font-family: var(--heading-font-family) !important;
  background: #E7E8E5 !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin: 1.5rem 0 !important;
  border: 2px solid #2A2A2A !important;
  max-width: 100% !important;
  width: 100% !important;
}

/* Desktop layout for review forms */
@media screen and (min-width: 750px) {
  .jdgm-write-rev-form,
  .jdgm-rev-form {
    max-width: 1000px !important;
    padding: 2.5rem !important;
  }

  /* Grid layout for form fields */
  .jdgm-write-rev-form .jdgm-form__body,
  .jdgm-rev-form .jdgm-form__body {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 1.5rem !important;
  }

  /* Full width fields */
  .jdgm-write-rev-form .jdgm-form__field:has(textarea),
  .jdgm-rev-form .jdgm-form__field:has(textarea),
  .jdgm-write-rev-form .jdgm-form__field[data-field="review_title"],
  .jdgm-rev-form .jdgm-form__field[data-field="review_title"],
  .jdgm-write-rev-form .jdgm-form__field[data-field="review_content"],
  .jdgm-rev-form .jdgm-form__field[data-field="review_content"] {
    grid-column: 1 / -1 !important;
  }
}

/* Write review link button */
.jdgm-write-rev-link {
  background: #47DE47 !important;
  color: #2A2A2A !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  padding: 0.875rem 2rem !important;
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  text-decoration: none !important;
  display: inline-block !important;
  transition: all 0.2s ease !important;
  margin: 1rem 0 !important;
}

.jdgm-write-rev-link:hover {
  background: #2A2A2A !important;
  color: #47DE47 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(42, 42, 42, 0.2) !important;
}

/* Form field containers */
.jdgm-rev-form__field,
.jdgm-write-rev-form__field {
  margin-bottom: 1.5rem !important;
}

/* Star rating specific styling - ONLY for forms */
.jdgm-form .jdgm-star-rating-wrapper,
.jdgm-write-rev-form .jdgm-star-rating-wrapper,
.jdgm-rev-form .jdgm-star-rating-wrapper {
  margin: 1.5rem 0 !important;
}

.jdgm-form .jdgm-star-rating-wrapper .jdgm-star,
.jdgm-write-rev-form .jdgm-star-rating-wrapper .jdgm-star,
.jdgm-rev-form .jdgm-star-rating-wrapper .jdgm-star {
  font-size: 2.5rem !important;
  color: #DDDDDD !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-right: 0.25rem !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1) !important;
}

.jdgm-form .jdgm-star-rating-wrapper .jdgm-star:hover,
.jdgm-form .jdgm-star-rating-wrapper .jdgm-star.jdgm-star--hov,
.jdgm-form .jdgm-star-rating-wrapper .jdgm-star.jdgm-star--sel,
.jdgm-write-rev-form .jdgm-star-rating-wrapper .jdgm-star:hover,
.jdgm-write-rev-form .jdgm-star-rating-wrapper .jdgm-star.jdgm-star--hov,
.jdgm-write-rev-form .jdgm-star-rating-wrapper .jdgm-star.jdgm-star--sel,
.jdgm-rev-form .jdgm-star-rating-wrapper .jdgm-star:hover,
.jdgm-rev-form .jdgm-star-rating-wrapper .jdgm-star.jdgm-star--hov,
.jdgm-rev-form .jdgm-star-rating-wrapper .jdgm-star.jdgm-star--sel {
  color: #47DE47 !important;
  transform: scale(1.1) !important;
}

/* Form inputs with Judge.me specific classes */
.jdgm-rev-form input[type="text"],
.jdgm-rev-form input[type="email"],
.jdgm-rev-form textarea,
.jdgm-write-rev-form input[type="text"],
.jdgm-write-rev-form input[type="email"],
.jdgm-write-rev-form textarea {
  width: 100% !important;
  padding: 0.875rem 1rem !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  background: #FFFFFF !important;
  color: #2A2A2A !important;
  font-family: var(--text-font-family) !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
  margin-bottom: 1rem !important;
  box-sizing: border-box !important;
}

/* Focus states for Judge.me inputs */
.jdgm-rev-form input:focus,
.jdgm-rev-form textarea:focus,
.jdgm-write-rev-form input:focus,
.jdgm-write-rev-form textarea:focus {
  outline: none !important;
  border-color: #47DE47 !important;
  box-shadow: 0 0 0 3px rgba(71, 222, 71, 0.1) !important;
}

/* Submit buttons */
.jdgm-rev-form__submit,
.jdgm-write-rev-form__submit,
.jdgm-btn--primary {
  background: #47DE47 !important;
  color: #2A2A2A !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  padding: 0.875rem 2rem !important;
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-top: 1rem !important;
  width: auto !important;
  display: inline-block !important;
}

.jdgm-rev-form__submit:hover,
.jdgm-write-rev-form__submit:hover,
.jdgm-btn--primary:hover {
  background: #2A2A2A !important;
  color: #47DE47 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(42, 42, 42, 0.2) !important;
}

/* File upload area */
.jdgm-file-upload,
.jdgm-file-upload-area {
  border: 2px dashed #2A2A2A !important;
  border-radius: 8px !important;
  padding: 2rem !important;
  background: #F8F8F8 !important;
  text-align: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin: 1rem 0 !important;
}

.jdgm-file-upload:hover,
.jdgm-file-upload-area:hover {
  border-color: #47DE47 !important;
  background: #F0F8F0 !important;
}

.jdgm-file-upload-text {
  font-family: var(--text-font-family) !important;
  color: #666666 !important;
  font-size: 0.875rem !important;
}

/* Form labels with Judge.me classes */
.jdgm-rev-form label,
.jdgm-write-rev-form label,
.jdgm-form-label {
  font-family: var(--heading-font-family) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  color: #2A2A2A !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  text-transform: uppercase !important;
  letter-spacing: 0.3px !important;
}

/* Additional form styling for better coverage */
.jdgm-form-wrapper,
.jdgm-review-form-wrapper {
  background: transparent !important;
  padding: 0 !important;
}

/* Form title variations */
.jdgm-form-title,
.jdgm-write-rev-title {
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 1.75rem !important;
  color: #2A2A2A !important;
  margin-bottom: 1.5rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Rating display */
.jdgm-rating-display {
  margin: 1rem 0 !important;
}

/* Form validation messages */
.jdgm-error-message,
.jdgm-validation-error {
  color: #E74C3C !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
  font-family: var(--text-font-family) !important;
  background: rgba(231, 76, 60, 0.1) !important;
  padding: 0.5rem !important;
  border-radius: 4px !important;
  border-left: 4px solid #E74C3C !important;
}

.jdgm-success-message {
  color: #47DE47 !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
  font-family: var(--text-font-family) !important;
  background: rgba(71, 222, 71, 0.1) !important;
  padding: 0.5rem !important;
  border-radius: 4px !important;
  border-left: 4px solid #47DE47 !important;
}

/* Loading states */
.jdgm-form-loading,
.jdgm-btn--loading {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

/* Form close button */
.jdgm-form-close,
.jdgm-close-btn {
  background: transparent !important;
  border: none !important;
  font-size: 1.5rem !important;
  color: #2A2A2A !important;
  cursor: pointer !important;
  padding: 0.5rem !important;
  position: absolute !important;
  top: 1rem !important;
  right: 1rem !important;
  transition: color 0.2s ease !important;
}

.jdgm-form-close:hover,
.jdgm-close-btn:hover {
  color: #E74C3C !important;
}

/* Form overlay */
.jdgm-form-overlay {
  background: rgba(42, 42, 42, 0.8) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  backdrop-filter: blur(4px) !important;
}

/* Required field indicators */
.jdgm-required,
.jdgm-form-required {
  color: #E74C3C !important;
  margin-left: 0.25rem !important;
}

/* Character counter */
.jdgm-char-counter {
  font-size: 0.75rem !important;
  color: #666666 !important;
  text-align: right !important;
  margin-top: 0.25rem !important;
  font-family: var(--text-font-family) !important;
}

/* Form progress indicator */
.jdgm-form-progress {
  background: #DDDDDD !important;
  height: 4px !important;
  border-radius: 2px !important;
  margin-bottom: 1.5rem !important;
  overflow: hidden !important;
}

.jdgm-form-progress-bar {
  background: #47DE47 !important;
  height: 100% !important;
  transition: width 0.3s ease !important;
}

/* Additional desktop form improvements */
@media screen and (min-width: 750px) {
  /* Ensure form fields are properly spaced in grid */
  .jdgm-form .jdgm-form__field,
  .jdgm-write-rev-form .jdgm-form__field,
  .jdgm-rev-form .jdgm-form__field {
    margin-bottom: 0 !important;
  }

  /* Style the rating section to span full width */
  .jdgm-form .jdgm-form__field--rating,
  .jdgm-write-rev-form .jdgm-form__field--rating,
  .jdgm-rev-form .jdgm-form__field--rating {
    grid-column: 1 / -1 !important;
    text-align: center !important;
    padding: 1rem 0 !important;
  }

  /* Style submit button area */
  .jdgm-form .jdgm-form__actions,
  .jdgm-write-rev-form .jdgm-form__actions,
  .jdgm-rev-form .jdgm-form__actions {
    grid-column: 1 / -1 !important;
    text-align: center !important;
    margin-top: 1rem !important;
  }
}

/* Additional mobile adjustments */
@media screen and (max-width: 749px) {
  .jdgm-write-rev-form,
  .jdgm-rev-form {
    padding: 1.5rem !important;
    margin: 1rem 0 !important;
    border-radius: 8px !important;
  }

  .jdgm-form .jdgm-star-rating-wrapper .jdgm-star,
  .jdgm-write-rev-form .jdgm-star-rating-wrapper .jdgm-star,
  .jdgm-rev-form .jdgm-star-rating-wrapper .jdgm-star,
  .jdgm-form .jdgm-star,
  .jdgm-write-rev-form .jdgm-star,
  .jdgm-rev-form .jdgm-star,
  .jdgm-star-rating .jdgm-star {
    font-size: 2rem !important;
    margin-right: 0.125rem !important;
  }

  .jdgm-form-close,
  .jdgm-close-btn {
    top: 0.75rem !important;
    right: 0.75rem !important;
  }
}

/* Catch-all for any missed Judge.me form elements */
[class*="jdgm-form"] input[type="text"],
[class*="jdgm-form"] input[type="email"],
[class*="jdgm-form"] textarea,
[class*="jdgm"] input[type="text"],
[class*="jdgm"] input[type="email"],
[class*="jdgm"] textarea {
  font-family: var(--text-font-family) !important;
  border-radius: 8px !important;
  border: 2px solid #2A2A2A !important;
  padding: 0.875rem 1rem !important;
  background: #FFFFFF !important;
  color: #2A2A2A !important;
  transition: all 0.2s ease !important;
}

[class*="jdgm-form"] input:focus,
[class*="jdgm-form"] textarea:focus,
[class*="jdgm"] input:focus,
[class*="jdgm"] textarea:focus {
  border-color: #47DE47 !important;
  box-shadow: 0 0 0 3px rgba(71, 222, 71, 0.1) !important;
  outline: none !important;
}

/* Ensure buttons are styled consistently */
[class*="jdgm"] button,
[class*="jdgm"] input[type="submit"],
[class*="jdgm-btn"] {
  font-family: var(--heading-font-family) !important;
  background: #47DE47 !important;
  color: #2A2A2A !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  padding: 0.875rem 2rem !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

[class*="jdgm"] button:hover,
[class*="jdgm"] input[type="submit"]:hover,
[class*="jdgm-btn"]:hover {
  background: #2A2A2A !important;
  color: #47DE47 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(42, 42, 42, 0.2) !important;
}

/* Preserve original styling for display stars in reviews */
.jdgm-rev .jdgm-star,
.jdgm-rev__rating .jdgm-star,
.jdgm-rev-widg__summary .jdgm-star,
.jdgm-histogram .jdgm-star {
  font-size: inherit !important;
  color: inherit !important;
  cursor: default !important;
  transition: none !important;
  margin-right: inherit !important;
  text-shadow: none !important;
  transform: none !important;
}

.jdgm-rev .jdgm-star:hover,
.jdgm-rev__rating .jdgm-star:hover,
.jdgm-rev-widg__summary .jdgm-star:hover,
.jdgm-histogram .jdgm-star:hover {
  color: inherit !important;
  transform: none !important;
  cursor: default !important;
}


