// <PERSON>ript to change Judge.me reviews title to h2
document.addEventListener('DOMContentLoaded', function() {
  // Function to change the title element
  function changeReviewsTitleToH2() {
    // Look for the Judge.me reviews title
    const reviewsTitle = document.querySelector('.jdgm-rev-widg__title');
    
    if (reviewsTitle) {
      // Get the current text content
      const titleText = reviewsTitle.textContent;
      
      // Create a new h2 element
      const h2Element = document.createElement('h2');
      h2Element.className = 'jdgm-rev-widg__title';
      h2Element.textContent = titleText;
      
      // Replace the original element with the h2
      reviewsTitle.parentNode.replaceChild(h2Element, reviewsTitle);
    }
  }
  
  // Initial check
  changeReviewsTitleToH2();
  
  // Also check after a delay to handle dynamic loading
  setTimeout(changeReviewsTitleToH2, 1000);
  setTimeout(changeReviewsTitleToH2, 2000);
  
  // Use MutationObserver to detect when the reviews widget is loaded
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.addedNodes && mutation.addedNodes.length > 0) {
        for (let i = 0; i < mutation.addedNodes.length; i++) {
          const node = mutation.addedNodes[i];
          if (node.classList && node.classList.contains('jdgm-rev-widg')) {
            changeReviewsTitleToH2();
            break;
          }
        }
      }
    });
  });
  
  // Start observing the document body for changes
  observer.observe(document.body, { childList: true, subtree: true });
});
