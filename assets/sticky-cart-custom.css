/* Custom styles for the sticky add to cart button */
.product-quick-add {
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  padding: 0 !important;
  background-color: #2A2A2A !important;
  border-radius: 0 !important;
  border: none !important;
  display: flex !important;
  justify-content: center !important; /* Changed from space-between to center */
  align-items: center !important;
  z-index: 100 !important;
  transform: translateY(100%) !important;
  transition: transform 0.3s ease-in-out !important;
}

.product-quick-add.is-visible {
  transform: translateY(0) !important;
  box-shadow: 0 -4px 10px rgba(0,0,0,0.2) !important; /* Added shadow at the top of the sticky add-to-cart */
}

.product-quick-add__container {
  display: flex;
  width: 100%;
  max-width: calc(var(--container-max-width) + 60px); /* Add 60px (30px padding on each side) to container max width */
  margin: 0 auto;
  padding: 10px 20px;
  justify-content: space-between;
  align-items: center;
}

.product-quick-add__left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-quick-add__center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-quick-add__right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-quick-add__image {
  width: 50px;
  height: 50px;
  border-radius: 5px;
  object-fit: cover;
}

.product-quick-add__title {
  color: #FFFFFF;
  font-weight: 600;
  font-size: 16px;
  margin: 0;
}

.product-quick-add__price {
  color: #FFFFFF;
  font-size: 14px;
  margin: 0;
}

.product-quick-add__button {
  background-color: #47DE47 !important;
  color: #2A2A2A !important;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  white-space: nowrap;
}

.product-quick-add__purchase-options {
  display: flex;
  gap: 10px;
}

.product-quick-add__option {
  background-color: transparent;
  color: #FFFFFF;
  border: 1px solid #FFFFFF;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.product-quick-add__option.selected {
  background-color: #FFFFFF;
  color: #2A2A2A;
}

@media screen and (max-width: 768px) {
  .product-quick-add__container {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .product-quick-add__left,
  .product-quick-add__center,
  .product-quick-add__right {
    width: 100%;
    justify-content: center;
  }

  .product-quick-add__purchase-options {
    width: 100%;
    justify-content: space-between;
  }

  .product-quick-add__button {
    width: 100%;
  }
}
