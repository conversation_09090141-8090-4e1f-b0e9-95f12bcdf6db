/* Additional CSS to reduce spacing in JudgeMe reviews on mobile */

@media screen and (max-width: 749px) {
  /* Target the JudgeMe reviews container directly */
  .jdgm-widget-actions-wrapper {
    margin-top: 0.5rem !important;
  }
  
  /* Target the review widget header */
  .jdgm-rev-widg__header {
    margin-bottom: 0.5rem !important;
    padding-bottom: 0 !important;
  }
  
  /* Target the review widget title */
  .jdgm-rev-widg__title {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  
  /* Target the review widget summary */
  .jdgm-rev-widg__summary {
    padding: 0 !important;
  }
  
  /* Target the review widget body */
  .jdgm-rev-widg__body {
    margin-top: 0.5rem !important;
  }
  
  /* Target the reviews section */
  .jdgm-reviews__wrapper {
    margin-top: 0.5rem !important;
  }
  
  /* Target the pagination */
  .jdgm-paginate {
    margin-top: 0.5rem !important;
  }
  
  /* Target the entire JudgeMe container */
  #judgeme_product_reviews {
    margin-top: -20px !important;
    padding-top: 0 !important;
  }
  
  /* Force the slider section to have minimal bottom padding */
  #shopify-section-slider_whats_inside {
    margin-bottom: -20px !important;
  }
  
  /* Force the apps section to have negative top margin */
  #shopify-section-17372431293a76cefd {
    margin-top: -40px !important;
  }
}
