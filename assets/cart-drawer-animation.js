// Custom script to make the cart drawer slide in from right to left when opened and slide back to the right when closed
document.addEventListener('DOMContentLoaded', function() {
  // Create a style element for our custom animations
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    /* Hide the default drawer animations */
    cart-drawer::part(content) {
      animation: none !important;
      clip-path: none !important;
      transform: translateX(100%) !important;
      transition: transform 0.4s cubic-bezier(0.86, 0, 0.07, 1) !important;
      z-index: 1000 !important; /* Ensure content is above overlay and header */
    }

    /* Our custom animation for sliding in */
    cart-drawer[open]::part(content) {
      transform: translateX(0) !important;
    }

    /* Our custom animation for sliding out */
    cart-drawer.animate-out::part(content) {
      transform: translateX(0) !important;
    }

    cart-drawer.animate-out.animating::part(content) {
      transform: translateX(100%) !important;
    }

    /* Fade out the overlay */
    cart-drawer.animate-out::part(overlay) {
      opacity: 0 !important;
      transition: opacity 0.4s ease-out !important;
      backdrop-filter: blur(0px) !important;
      -webkit-backdrop-filter: blur(0px) !important;
    }

    /* Ensure the overlay doesn't hide the header */
    cart-drawer::part(overlay) {
      z-index: 998 !important; /* Below drawer content but above other content */
    }

    /* Fade out the content */
    cart-drawer.animate-out::part(content) > * {
      opacity: 0 !important;
      transition: opacity 0.2s ease-out !important;
    }

    /* Ensure the header stays visible */
    body.drawer-open #shopify-section-header,
    body.drawer-open store-header.header {
      visibility: visible !important;
      z-index: 998 !important;
    }
  `;
  document.head.appendChild(styleElement);

  // Function to prevent layout shift when drawer opens/closes
  function preventLayoutShift() {
    // Get the scrollbar width
    const getScrollbarWidth = () => {
      // Create a temporary div to measure scrollbar width
      const outer = document.createElement('div');
      outer.style.visibility = 'hidden';
      outer.style.overflow = 'scroll';
      document.body.appendChild(outer);

      const inner = document.createElement('div');
      outer.appendChild(inner);

      const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
      outer.parentNode.removeChild(outer);

      return scrollbarWidth;
    };

    // Store the scrollbar width
    const scrollbarWidth = getScrollbarWidth();

    // Get all fixed elements that need adjustment
    const getFixedElements = () => {
      // Exclude the header from the elements that get adjusted
      return Array.from(document.querySelectorAll('.sticky-element, [class*="sticky"]:not(.header), [style*="position: fixed"]:not(.header), [style*="position:fixed"]:not(.header)'));
    };

    // Function to add padding to body and adjust fixed elements when drawer opens
    const lockBody = () => {
      // Only add padding if the page is scrollable
      if (document.documentElement.scrollHeight > window.innerHeight) {
        // Add padding to body to compensate for scrollbar disappearance
        document.body.style.paddingRight = `${scrollbarWidth}px`;
        document.body.style.overflow = 'hidden';

        // Add class to body for CSS targeting
        document.body.classList.add('drawer-open');

        // Make sure the header stays visible
        const header = document.querySelector('store-header.header');
        if (header) {
          header.style.visibility = 'visible';
        }

        // Adjust fixed elements (excluding the header)
        getFixedElements().forEach(el => {
          // Only adjust if not already adjusted
          if (!el.hasAttribute('data-original-right')) {
            const style = window.getComputedStyle(el);
            const originalRight = style.right;

            // Store original right value
            el.setAttribute('data-original-right', originalRight);

            // Adjust right value to prevent shifting
            if (originalRight !== 'auto') {
              const rightValue = parseInt(originalRight, 10);
              el.style.right = `${rightValue + scrollbarWidth}px`;
            } else {
              el.style.right = `${scrollbarWidth}px`;
            }
          }
        });
      }
    };

    // Function to remove padding and restore fixed elements when drawer closes
    const unlockBody = () => {
      document.body.style.paddingRight = '';
      document.body.style.overflow = '';
      document.body.classList.remove('drawer-open');

      // No need to modify header visibility here as it should remain visible

      // Restore fixed elements
      getFixedElements().forEach(el => {
        if (el.hasAttribute('data-original-right')) {
          el.style.right = el.getAttribute('data-original-right');
          el.removeAttribute('data-original-right');
        }
      });
    };

    return { lockBody, unlockBody };
  }

  // Create the layout shift prevention functions
  const { lockBody, unlockBody } = preventLayoutShift();

  // Function to patch the cart drawer's show and hide methods
  function patchCartDrawer() {
    // Get the cart drawer element
    const cartDrawer = document.querySelector('cart-drawer');
    if (!cartDrawer) return;

    // Store the original methods
    const originalShow = cartDrawer.show;
    const originalHide = cartDrawer.hide;

    // Override the show method to ensure the slide-in animation works
    cartDrawer.show = function() {
      // Lock the body to prevent layout shift
      lockBody();

      // Call the original show method
      const showPromise = originalShow.call(this);

      // Return the promise from the original show method
      return showPromise;
    };

    // Override the hide method
    cartDrawer.hide = function() {
      // Add our animation classes
      this.classList.add('animate-out');

      // Start the animation after a small delay to ensure CSS is applied
      setTimeout(() => {
        this.classList.add('animating');

        // After animation completes, remove our classes and call the original hide
        setTimeout(() => {
          this.classList.remove('animate-out', 'animating');
          originalHide.call(this);

          // Unlock the body after the drawer is hidden
          unlockBody();
        }, 400); // Match animation duration
      }, 10);

      // Prevent the original hide from being called immediately
      return new Promise(resolve => {
        setTimeout(resolve, 450); // Slightly longer than animation to ensure completion
      });
    };

    console.log('Cart drawer animations patched successfully');
  }

  // Try to patch immediately if the cart drawer exists
  if (customElements.get('cart-drawer')) {
    patchCartDrawer();
  } else {
    // Otherwise wait for it to be defined
    customElements.whenDefined('cart-drawer').then(patchCartDrawer);
  }

  // Also set up a MutationObserver as a fallback to detect when the cart drawer is added to the DOM
  const observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length) {
        for (const node of mutation.addedNodes) {
          if (node.tagName && node.tagName.toLowerCase() === 'cart-drawer') {
            patchCartDrawer();
            observer.disconnect();
            break;
          }
        }
      }
    }
  });

  // Start observing the document body
  observer.observe(document.body, { childList: true, subtree: true });
});
