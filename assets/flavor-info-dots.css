.flavor-info-container {
  margin-top: 15px;
  margin-bottom: 15px;
  padding-top: 15px;
  display: none;
  width: 100%;
}

/* Only show when Variety Pack is selected */
.variant-selected-flavor-variety-pack .flavor-info-container {
  display: block;
}

.flavor-info-title {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-size: var(--text-h5);
  color: rgb(var(--text-color));
  text-transform: var(--heading-text-transform);
  letter-spacing: var(--heading-letter-spacing);
  line-height: 1.4;
  margin-bottom: 10px;
}

.flavor-info-items {
  display: flex;
  /* flex-direction: column; */
  gap: 30px;
  text-transform: uppercase;
}

.flavor-info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.flavor-dot-container {
  position: relative;
  width: 32px;
  height: 32px;
}

.flavor-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.citrus-dot {
  background: linear-gradient(to right, #47de47 50%, #f9d423 50%);
}

.watermelon-dot {
  background-color: #ff6b9c;
}

.raspberry-dot {
  background: linear-gradient(to right, #ff4b4b 50%, #47de47 50%);
}

.flavor-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #2A2A2A;
  color: #E7E8E5;
  border-radius: 10px;
  padding: 2px 5px;
  font-size: 9px;
  font-weight: bold;
  border: none;
  line-height: 1;
}

.flavor-text {
  font-size: var(--text-sm);
}

.flavor-name {
  font-weight: 500;
}

/* Media query for mobile */
@media screen and (max-width: 767px) {
  .flavor-info-container {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-top: 10px;
  }

  .flavor-info-items {
    gap: 6px;
  }
}
