/**
 * Enhanced Font Loading API implementation to prevent layout shifts
 * This script implements a robust font loading strategy with fallbacks
 */

// Immediately apply fonts-loading class to prevent FOUT
document.documentElement.classList.add('fonts-loading');

// Check if fonts were already loaded in a previous page view
if (sessionStorage.getItem('fonts-loaded')) {
  document.documentElement.classList.remove('fonts-loading');
  document.documentElement.classList.add('fonts-loaded');
}

// Define critical and non-critical font families
const criticalFonts = [
  // Most important fonts that should be loaded first
  { family: 'FormaDJRDisplay', weight: '400', style: 'normal' },
  { family: 'FormaDJRDisplay', weight: '700', style: 'normal' },
  { family: 'FormaDJRText', weight: '400', style: 'normal' },
  { family: 'Meltmino', weight: '400', style: 'normal' }
];

const nonCriticalFonts = [
  // Secondary fonts that can be loaded after critical content is displayed
  { family: 'FormaDJRDisplay', weight: '800', style: 'normal' },
  { family: 'FormaDJRBanner', weight: '400', style: 'normal' },
  { family: 'FormaDJRBanner', weight: '700', style: 'normal' },
  { family: 'FormaDJRBanner', weight: '800', style: 'normal' },
  { family: 'FormaDJRText', weight: '700', style: 'normal' },
  { family: 'FormaDJRText', weight: '800', style: 'normal' },
  { family: 'FormaDJRDeck', weight: '400', style: 'normal' },
  { family: 'FormaDJRDeck', weight: '700', style: 'normal' },
  { family: 'FormaDJRDeck', weight: '800', style: 'normal' },
  { family: 'FormaDJRMicro', weight: '400', style: 'normal' },
  { family: 'FormaDJRMicro', weight: '700', style: 'normal' },
  { family: 'FormaDJRMicro', weight: '800', style: 'normal' },
  { family: 'Meltmino', weight: '700', style: 'normal' }
];

// Function to load fonts with timeout
function loadFontsWithTimeout(fontFamilies, timeout) {
  return Promise.race([
    Promise.all(
      fontFamilies.map(font =>
        document.fonts.load(`${font.weight} ${font.style} 1em "${font.family}"`)
      )
    ),
    new Promise(resolve => setTimeout(resolve, timeout))
  ]);
}

// Main font loading function
function loadFonts() {
  if (!('fonts' in document)) {
    // Font Loading API not supported, remove loading class and add fallback class
    document.documentElement.classList.remove('fonts-loading');
    document.documentElement.classList.add('no-font-loading-api');
    return;
  }

  // Stage 1: Load critical fonts with a short timeout
  loadFontsWithTimeout(criticalFonts, 1000).then(() => {
    // Add class to indicate critical fonts are loaded
    document.documentElement.classList.add('critical-fonts-loaded');

    // Stage 2: Load non-critical fonts with a longer timeout
    loadFontsWithTimeout(nonCriticalFonts, 2000).then(() => {
      // All fonts have loaded (or timed out)
      document.documentElement.classList.remove('fonts-loading');
      document.documentElement.classList.add('fonts-loaded');

      // Store in session storage that fonts are loaded
      try {
        sessionStorage.setItem('fonts-loaded', 'true');
      } catch (e) {
        console.warn('Could not save font loading state to sessionStorage', e);
      }
    }).catch(handleFontError);
  }).catch(handleFontError);
}

// Error handler for font loading
function handleFontError(err) {
  console.warn('Error loading fonts:', err);
  document.documentElement.classList.remove('fonts-loading');
  document.documentElement.classList.add('fonts-error');
}

// Check if the document is already interactive or complete
if (document.readyState === 'interactive' || document.readyState === 'complete') {
  loadFonts();
} else {
  // Wait for DOMContentLoaded to load fonts
  document.addEventListener('DOMContentLoaded', loadFonts);
}
