/* Hero Image Optimization CSS
 * Optimizations specifically for the hero section images to improve loading performance
 */

/* Minimal hero image optimization - remove all complex CSS */
.mobile-hero-image {
  /* Basic sizing only */
  width: 100%;
  height: auto;

  /* Remove white background */
  background: transparent;
}

/* Optimize for WebP and modern formats */
.mobile-hero-image[src*=".webp"],
.images-scrolling-desktop__media-wrapper > img[src*=".webp"] {
  /* WebP images can use better compression settings */
  image-rendering: auto;
}

/* Loading state for hero images - simplified */
.mobile-hero-image[loading="eager"],
.images-scrolling-desktop__media-wrapper > img:first-child[loading="eager"] {
  /* Simple immediate rendering */
  display: block;
}

/* Simplified hero section container */
.images-scrolling-mobile__item:first-child,
.images-scrolling-desktop__media-wrapper > :first-child {
  /* Basic display only */
  display: block;
}

/* Preload hint for critical hero images */
.images-scrolling-mobile__item:first-child .mobile-hero-image,
.images-scrolling-desktop__media-wrapper > img:first-child {
  /* Ensure immediate decoding */
  decoding: sync;

  /* Prioritize this image */
  importance: high;
}

/* Prevent cumulative layout shift in hero section */
.images-scrolling-mobile__item:first-child .mobile-hero-image {
  /* Reserve space to prevent layout shifts */
  min-height: 350px;
}

@media screen and (min-width: 741px) {
  .images-scrolling-mobile__item:first-child .mobile-hero-image {
    min-height: 450px;
  }

  /* Ensure desktop hero images are properly sized */
  .images-scrolling-desktop__media-wrapper > img:first-child {
    min-height: 500px;
    max-height: 80vh;
    object-fit: contain;
  }
}

/* Optimize for Core Web Vitals */
.images-scrolling-desktop__media-wrapper > img:first-child {
  /* Ensure the first image in desktop view loads immediately */
  loading: eager !important;
  fetchpriority: high !important;
}

.mobile-hero-image:first-of-type {
  /* Ensure the first mobile hero image loads immediately */
  loading: eager !important;
  fetchpriority: high !important;
}

/* Minimal mobile hero image CSS */
@media screen and (max-width: 740px) {
  .mobile-hero-image {
    width: 100%;
    height: auto;
    background: transparent;
  }
}

@media screen and (min-width: 741px) {
  .images-scrolling-desktop__media-wrapper {
    /* Ensure the media wrapper takes full advantage of its space */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .images-scrolling-desktop__media-wrapper > img {
    /* Make desktop images larger and more prominent */
    width: 100% !important;
    height: auto !important;
    max-width: none !important;
    object-fit: contain !important;
  }
}
