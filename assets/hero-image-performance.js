/**
 * Hero Image Performance Optimization
 * Enhances hero image loading with advanced performance techniques
 */

(function() {
  'use strict';

  // Early return if not on a page with hero images
  if (!document.querySelector('.mobile-hero-image, .images-scrolling-desktop__media-wrapper img')) {
    return;
  }

  /**
   * Optimize hero image loading with aggressive first image optimization
   */
  function optimizeHeroImages() {
    const heroImages = document.querySelectorAll('.mobile-hero-image, .images-scrolling-desktop__media-wrapper img');

    if (!heroImages.length) return;

    // Immediately optimize the first hero image
    const firstHeroImage = document.querySelector('.images-scrolling-mobile__item:first-child .mobile-hero-image, .images-scrolling-desktop__media-wrapper img:first-child');

    if (firstHeroImage) {
      // Force immediate loading and decoding
      firstHeroImage.loading = 'eager';
      firstHeroImage.fetchPriority = 'high';
      firstHeroImage.decoding = 'sync';

      // Ensure immediate decoding
      if (firstHeroImage.decode) {
        firstHeroImage.decode().catch(() => {});
      }

      // Add critical performance hints
      firstHeroImage.style.contentVisibility = 'auto';
      firstHeroImage.style.containIntrinsicSize = '400px 300px';
      firstHeroImage.style.willChange = 'auto';
    }

    // Create intersection observer for other images
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;

          // Prioritize decoding for visible images
          if (img.decode) {
            img.decode().catch(() => {});
          }

          // Stop observing once loaded
          imageObserver.unobserve(img);
        }
      });
    }, {
      rootMargin: '100px 0px', // Start loading earlier
      threshold: 0.1
    });

    // Observe non-critical hero images
    heroImages.forEach(img => {
      if (img !== firstHeroImage) {
        imageObserver.observe(img);
      }
    });
  }

  /**
   * Preload next hero images for better UX
   */
  function preloadNextHeroImages() {
    const heroContainer = document.querySelector('.images-scrolling-mobile, .images-scrolling-desktop');
    if (!heroContainer) return;

    const allImages = heroContainer.querySelectorAll('img[data-src], img[src]');
    if (allImages.length <= 1) return;

    // Preload the second image for smoother transitions
    const secondImage = allImages[1];
    if (secondImage && secondImage.dataset.src) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = secondImage.dataset.src;
      link.fetchPriority = 'low';
      document.head.appendChild(link);
    }
  }

  /**
   * Optimize image rendering performance
   */
  function optimizeImageRendering() {
    const heroImages = document.querySelectorAll('.mobile-hero-image, .images-scrolling-desktop__media-wrapper img');
    
    heroImages.forEach(img => {
      // Add performance optimizations
      img.style.imageRendering = 'auto';
      img.style.imageRendering = '-webkit-optimize-contrast';
      
      // Ensure proper aspect ratio to prevent layout shifts
      if (!img.style.aspectRatio && img.naturalWidth && img.naturalHeight) {
        img.style.aspectRatio = `${img.naturalWidth} / ${img.naturalHeight}`;
      }
    });
  }

  /**
   * Initialize all optimizations
   */
  function init() {
    // Run optimizations when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        optimizeHeroImages();
        preloadNextHeroImages();
        optimizeImageRendering();
      });
    } else {
      optimizeHeroImages();
      preloadNextHeroImages();
      optimizeImageRendering();
    }
  }

  // Initialize the optimizations
  init();

})();
