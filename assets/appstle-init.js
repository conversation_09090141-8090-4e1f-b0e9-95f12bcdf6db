document.addEventListener('DOMContentLoaded', function() {
  // Initialize the Appstle subscription widget if available.
  if (typeof appstleInit === 'function') {
    appstleInit();
  }

  // Define border colors.
  const fullOpacityBorderColor = 'rgb(var(--text-color))';        // Full opacity border.
  const halfOpacityBorderColor = 'rgb(var(--text-color) / 0.2)';      // 50% opacity border.

  // When selected:
  // - All options get full opacity border,
  // - Then non-dropdown ones are overridden to half opacity.
  function setSelectedStyles() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.setProperty('border-color', fullOpacityBorderColor, 'important');
    });
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown)').forEach(option => {
      option.style.setProperty('border-color', halfOpacityBorderColor, 'important');
    });
  }

  // When deselected:
  // - All options get half opacity border,
  // - Then non-dropdown ones are overridden to full opacity.
  function setDeselectedStyles() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.setProperty('border-color', halfOpacityBorderColor, 'important');
    });
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option:not(.appstle_include_dropdown)').forEach(option => {
      option.style.setProperty('border-color', fullOpacityBorderColor, 'important');
    });
  }

  // Helper: clear background colors from all options.
  function clearBackgroundColors() {
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      option.style.removeProperty('background-color');
    });
  }

  // Event: When a subscription plan is selected.
  document.addEventListener('AppstleSubscription:SubscriptionWidget:SellingPlanSelected', function() {
    const checkedInput = document.querySelector('.appstle_sub_widget input[name=selling_plan]:checked');
    console.log('Selling plan selected:', checkedInput ? checkedInput.value : null);

    // Apply selected border styles.
    setSelectedStyles();
    // Clear previous background colors.
    clearBackgroundColors();

    // Set background color on the selected option's container.
    if (checkedInput) {
      const selectedWrapper = checkedInput.closest('.appstle_subscription_wrapper_option');
      if (selectedWrapper) {
        selectedWrapper.style.setProperty('background-color', '#E7E7E5', 'important');
      }
    }
  });

  // Event: When a subscription plan is deselected.
  document.addEventListener('AppstleSubscription:SubscriptionWidget:SellingPlanDeSelected', function() {
    const checkedInput = document.querySelector('.appstle_sub_widget input[name=selling_plan]:checked');
    console.log('Selling plan deselected:', checkedInput ? checkedInput.value : null);

    // Apply deselected border styles.
    setDeselectedStyles();

    // Instead of clearing all backgrounds, update them:
    document.querySelectorAll('.WIDGET_TYPE_7 .appstle_subscription_wrapper_option').forEach(option => {
      // For non-dropdown options, set background to #E7E7E5.
      if (option.matches(':not(.appstle_include_dropdown)')) {
        option.style.setProperty('background-color', '#E7E7E5', 'important');
      } else {
        // For other options, clear any background color.
        option.style.removeProperty('background-color');
      }
    });
  });

  // Function to fix subscription compare-at-price display
  function fixSubscriptionPricing() {
    const appstleWidget = document.querySelector('.appstle_sub_widget');
    if (!appstleWidget) return;

    // Look specifically for compare-at-price elements in subscription options that show $0
    const compareAtPriceElements = appstleWidget.querySelectorAll('.appstle_subscription_amount_compare_at_price');

    compareAtPriceElements.forEach(element => {
      if (element.textContent.trim() === '$0' && !element.hasAttribute('data-price-fixed')) {
        element.setAttribute('data-price-fixed', 'true');
        element.textContent = '$69';
      }
    });
  }

  // Simple check for widget and update pricing - no mutation observer
  const checkForWidget = setInterval(() => {
    const appstleWidget = document.querySelector('.appstle_sub_widget');
    if (appstleWidget) {
      fixSubscriptionPricing();
      forceSaveBadgeStyling();
      clearInterval(checkForWidget);
    }
  }, 100);

  // Also run save badge styling and pricing updates periodically to catch any dynamically added content
  setInterval(() => {
    forceSaveBadgeStyling();
    fixSubscriptionPricing();
  }, 500);
});