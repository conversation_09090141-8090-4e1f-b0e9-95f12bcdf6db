/* Slider Image Sizing
 * This CSS ensures consistent image sizing for all slider sections
 * based on the media_size setting
 */

/* Force all swiper slides to have the same dimensions */
.swiper-slide {
  height: auto !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 400px !important; /* Set a minimum height for all slides */
}

/* Force all testimonial blocks to have the same height */
.lt-testimonial-block-slider {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
}

/* Control the image aspect ratio while maintaining full visibility */
.image-item-wrapper-slider {
  flex: 0 0 auto !important;
  width: 100% !important;
  position: relative !important;
  margin-bottom: 25px !important; /* Add space below the image */
  border-radius: 20px !important; /* Ensure rounded corners */
  overflow: hidden !important; /* Ensure the image doesn't overflow the rounded corners */
  min-height: 200px !important; /* Minimum height to ensure visibility */
  padding-bottom: 100% !important; /* Default aspect ratio of 1:1 if not set inline */
}

/* Override padding-bottom when aspect ratio is set inline */
.image-item-wrapper-slider[style*="aspect-ratio"] {
  padding-bottom: 0 !important; /* Let the aspect-ratio property handle the height */
  height: auto !important; /* Allow the height to be determined by the aspect ratio */
}

.image-item-wrapper-slider img,
.slider-image {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  border-radius: 20px !important; /* Ensure rounded corners */
}

/* Ensure proper sizing for video elements as well */
.image-item-wrapper-slider video,
.slider-video {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  border-radius: 20px !important;
}

/* Force all testimonial info sections to have the same height */
.lt-testimonial-info-slider {
  flex: 1 0 auto !important; /* Changed from 1 1 auto to 1 0 auto to prevent shrinking */
  display: flex !important;
  flex-direction: column !important;
  min-height: 150px !important; /* Set a minimum height for consistent spacing */
  justify-content: flex-start !important;
  padding: 20px 15px !important; /* Add consistent padding */
  margin-top: 10px !important; /* Add space between image and text */
}

/* Style text elements for consistent spacing */
.testimonial-quote-slider,
.testimonial-title-slider,
.testimonial-author {
  display: block !important;
  width: 100% !important;
  margin-bottom: 5px !important; /* Reduced from 10px to 5px for closer spacing */
  min-height: 1.2em !important; /* Reduced from 1.5em to 1.2em for tighter spacing */
  line-height: 1.3 !important; /* Reduced from 1.4 to 1.3 for tighter spacing */
}

.testimonial-quote-slider {
  margin-bottom: 2px !important; /* Further reduced to 2px for even closer spacing to title */
  flex: 0 0 auto !important;
}

.testimonial-title-slider {
  margin-bottom: 10px !important; /* Reduced from 15px to 10px for better spacing */
  flex: 0 0 auto !important;
}

.testimonial-author {
  flex: 0 0 auto !important;
}

/* Desktop-specific adjustments */
@media (min-width: 750px) {
  .image-item-wrapper-slider {
    min-height: 250px !important;
    aspect-ratio: 3/4 !important; /* Changed from 1/1 to 3/4 to match mobile */
  }

  .swiper-slide-active .image-item-wrapper-slider {
    min-height: 280px !important;
  }

  /* Ensure images are visible and properly sized */
  .image-item-wrapper-slider img,
  .slider-image {
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
  }
}

/* Responsive adjustments for mobile */
@media (max-width: 749px) {
  .swiper-slide {
    min-height: 350px !important; /* Slightly smaller minimum height on mobile */
  }

  .lt-testimonial-info-slider {
    min-height: 120px !important; /* Smaller minimum height for text container on mobile */
  }

  .image-item-wrapper-slider {
    margin-bottom: 15px !important;
    aspect-ratio: 3/4 !important; /* Maintain 3/4 aspect ratio on mobile */
    width: 100% !important;
    max-height: 70vh !important; /* Limit maximum height */
  }

  /* Make active slide images maintain the same aspect ratio but be slightly larger */
  .section-slider .swiper-slide-active .image-item-wrapper-slider {
    aspect-ratio: 3/4 !important; /* Keep the same aspect ratio */
    transform: scale(1.15) !important; /* Make it slightly larger */
    z-index: 2 !important; /* Ensure it appears above other slides */
  }

  /* Ensure images maintain aspect ratio while filling container */
  .image-item-wrapper-slider img,
  .slider-image {
    object-position: center !important;
    object-fit: cover !important;
  }
}
