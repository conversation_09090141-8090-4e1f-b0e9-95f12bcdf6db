{%- assign newsletter_block = section.blocks | where: 'type', 'newsletter' | first -%}

{%- if section.blocks.size > 3 -%}
<style>
  @media screen and (min-width: 1000px) {
    #shopify-section-{{ section.id }} {
      {%- if newsletter_block != blank -%}
        --footer-block-list-gap: var(--spacing-10);
      {%- endif -%}

      --footer-block-list-justify-content: space-between;
    }
  }
</style>
{%- endif -%}

<div class="footer">
  <div class="container">
    <div class="footer__wrapper">
      {%- comment -%}
      ------------------------------------------------------------------------------------------------------------------
      BLOCK AREA
      ------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- if section.blocks.size > 0 -%}
        <div class="footer__block-list empty:hidden">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'text' -%}
                {%- if block.settings.title != blank or block.settings.content != blank or shop.features.follow_on_shop? and block.settings.enable_follow_on_shop -%}
                  <div class="footer__block footer__block--text" {{ block.shopify_attributes }}>
                    {%- if block.settings.title != blank -%}
                      <p class="bold">{{ block.settings.title | escape }}</p>
                    {%- endif -%}

                    {%- if block.settings.content != blank -%}
                      <div class="prose text-subdued">
                        {{- block.settings.content -}}
                      </div>
                    {%- endif -%}

                    {%- if shop.features.follow_on_shop? and block.settings.enable_follow_on_shop -%}
                      <div class="follow-on-shop">
                        {{- shop | login_button: action: 'follow' -}}
                      </div>
                    {%- endif -%}
                  </div>
                {%- endif -%}

              {%- when 'links' -%}
                {%- if block.settings.menu != blank -%}
                  <div class="footer__block footer__block--menu" {{ block.shopify_attributes }}>
                    {%- if block.settings.show_menu_title -%}
                      <p class="bold">{{ block.settings.menu_title | default: block.settings.menu.title }}</p>
                    {%- endif -%}

                    <ul class="v-stack gap-3" role="list">
                      {%- for link in block.settings.menu.links -%}
                        <li>
                          <a href="{{ link.url }}" class="inline-block link-faded break-all">{{ link.title }}</a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                {%- endif -%}

              {%- when 'newsletter' -%}
                <div class="footer__block footer__block--newsletter" {{ block.shopify_attributes }}>
                  {%- if block.settings.image != blank -%}
                    {%- capture sizes -%}{{ block.settings.image_width }}px{%- endcapture -%}
                    {%- capture widths -%}{{ block.settings.image_width }}, {{ block.settings.image_width | times: 2 | at_most: block.settings.image.width }}{%- endcapture -%}
                    {%- capture style -%}max-width: {{ block.settings.image_width }}px{%- endcapture -%}
                    {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', style: style, sizes: sizes, widths: widths -}}
                  {%- endif -%}

                  <div class="v-stack gap-6">
                    {%- if block.settings.title != blank -%}
                      <p class="{{ block.settings.heading_size }}">{{- block.settings.title | escape -}}</p>
                    {%- endif -%}

                    {%- if block.settings.content != blank -%}
                      <div class="prose text-subdued">
                        {{- block.settings.content -}}
                      </div>
                    {%- endif -%}

                    {%- form 'customer', id: 'footer-newsletter', class: 'footer__newsletter-form form' -%}
                      {%- if form.posted_successfully? -%}
                        {%- capture success_message -%}{{ 'general.newsletter.subscribed_successfully' | t }}{%- endcapture -%}
                        {%- render 'banner', content: success_message, status: 'success' -%}
                      {%- else -%}
                        {%- if form.errors -%}
                          {%- capture error_message -%}{{ form.errors.translated_fields['email'] }} {{ form.errors.messages['email'] }}{%- endcapture -%}
                          {%- render 'banner', content: error_message, status: 'error' -%}
                        {%- endif -%}

                        <input type="hidden" name="contact[tags]" value="newsletter">

                        {%- capture label -%}{{ 'general.newsletter.email' | t }}{%- endcapture -%}
                        {%- render 'input', name: 'contact[email]', label: label, type: 'email', required: true, autocomplete: 'email', enterkeyhint: 'send', self_submit: true -%}
                      {%- endif -%}
                    {%- endform -%}
                  </div>
                </div>
            {%- endcase -%}
          {%- endfor -%}

        </div>
      {%- endif -%}

      {%- comment -%}
      ------------------------------------------------------------------------------------------------------------------
      SECONDARY AREA
      ------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      <div class="footer__aside empty:hidden">
        {%- comment -%}
        ----------------------------------------------------------------------------------------------------------------
        SOCIAL MEDIA + LOCALIZATION
        ----------------------------------------------------------------------------------------------------------------
        {%- endcomment -%}

        {%- if section.settings.show_social_media -%}
          {%- capture social_media -%}{%- render 'social-media' -%}{%- endcapture -%}
        {%- endif -%}

        {%- if section.settings.show_country_selector and localization.available_countries.size > 1 -%}
          {%- assign country_selector = true -%}
        {%- endif -%}

        {%- if section.settings.show_locale_selector and localization.available_languages.size > 1 -%}
          {%- assign locale_selector = true -%}
        {%- endif -%}

        {%- if social_media != blank or country_selector or locale_selector -%}
          <div class="footer__aside-top">
            {{- social_media -}}

            {%- if country_selector or locale_selector -%}
              <div class="h-stack gap-6">
                {%- if social_media == blank -%}
                  {%- assign popover_horizontal_position = 'start' -%}
                {%- else -%}
                  {%- assign popover_horizontal_position = 'end' -%}
                {%- endif -%}

                {%- if country_selector -%}
                  {%- render 'localization-selector', type: 'country', show_country_name: section.settings.show_country_name, show_country_flag: section.settings.show_country_flag, popover_horizontal_position: popover_horizontal_position -%}
                {%- endif -%}

                {%- if locale_selector -%}
                  {%- render 'localization-selector', type: 'locale', popover_horizontal_position: popover_horizontal_position -%}
                {%- endif -%}
              </div>
            {%- endif -%}
          </div>
        {%- endif -%}

        {%- comment -%}
        ----------------------------------------------------------------------------------------------------------------
        COPYRIGHT + PAYMENT METHODS
        ----------------------------------------------------------------------------------------------------------------
        {%- endcomment -%}

        <div class="footer__aside-bottom">
          {%- if section.settings.show_payment_icons and shop.enabled_payment_types.size > 0 -%}
            <ul class="footer__payment-icons h-stack wrap gap-2">
              {%- for type in shop.enabled_payment_types -%}
                <li class="contents">{{ type | payment_type_svg_tag }}</li>
              {%- endfor -%}
            </ul>
          {%- endif -%}

          <p class="footer__copyright text-sm text-subdued">© {{ 'now' | date: '%Y' }}, {{ shop.name }}.  </p>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Footer",
  "class": "shopify-section--footer",
  "tag": "footer",
  "max_blocks": 5,
  "settings": [
    {
      "type": "checkbox",
      "id": "show_social_media",
      "label": "Show social media",
      "info": "To configure social media, go to your social media settings.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show payment icons",
      "default": true
    },
    {
      "type": "header",
      "content": "Country/region selector",
      "info": "To add a country/region, go to your [markets settings.](/admin/settings/markets)"
    },
    {
      "type": "checkbox",
      "id": "show_country_selector",
      "label": "Show country/region selector",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_flag",
      "label": "Show country flag",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_country_name",
      "label": "Show country name",
      "default": true
    },
    {
      "type": "header",
      "content": "Language selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
    },
    {
      "type": "checkbox",
      "id": "show_locale_selector",
      "label": "Show language selector",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "About our store"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Use this text area to tell your customers about your brand and vision. You can change it in the theme editor.</p>"
        },
        {
          "type": "checkbox",
          "id": "enable_follow_on_shop",
          "label": "Show Follow on Shop",
          "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)",
          "default": true
        }
      ]
    },
    {
      "type": "links",
      "name": "Links",
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "info": "This menu won't show dropdown items.",
          "default": "footer"
        },
        {
          "type": "checkbox",
          "id": "show_menu_title",
          "label": "Show menu title",
          "default": true
        },
        {
          "type": "inline_richtext",
          "id": "menu_title",
          "label": "Menu title",
          "info": "Replaces the default menu title"
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Customers who subscribe will have their email address added to the \"accepts marketing\" [customer list](/admin/customers?query=&accepts_marketing=1)."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "600 x 600px .png recommended"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 300,
          "step": 10,
          "unit": "px",
          "label": "Image width",
          "default": 150
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "Title size",
          "options": [
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            },
            {
              "value": "h6",
              "label": "H6"
            }
          ],
          "default": "h3"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Sign up to our newsletter"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        }
      ]
    }
  ]
}
{% endschema %}