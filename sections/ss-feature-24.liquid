{% comment %}
---------------------------------------------------------
Copyright © 2023 Section Store. All rights reserved.
Unauthorized copying, modification, distribution, or use
of this code or any portion of it, is strictly prohibited.
Violators will be prosecuted to the fullest extent of the law.
For inquiries or permissions, contact <EMAIL>
---------------------------------------------------------
{% endcomment %}

{%- liquid 
  assign padding_horizontal = section.settings.padding_horizontal
  assign padding_horizontal_mobile = section.settings.padding_horizontal_mobile
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign border_color = section.settings.border_color
  assign border_thickness = section.settings.border_thickness
  assign margin_top = section.settings.margin_top
  assign margin_bottom = section.settings.margin_bottom
  assign margin_horizontal_mobile = section.settings.margin_horizontal_mobile
  assign margin_horizontal = section.settings.margin_horizontal
  assign background_color = section.settings.background_color
  assign background_gradient = section.settings.background_gradient
  assign full_width = section.settings.full_width
  assign content_width = section.settings.content_width
  assign lazy = section.settings.lazy
  assign section_radius = section.settings.section_radius

  assign body_layout = section.settings.body_layout
  assign body_gap = section.settings.body_gap

  assign image_radius = section.settings.image_radius
  assign image_width = section.settings.image_width
  assign image_ratio = section.settings.image_ratio
  assign image_ratio_mobile = section.settings.image_ratio_mobile    
  assign images_mt_mobile = section.settings.images_mt_mobile

  assign content_align = section.settings.content_align
  assign content_align_mobile = section.settings.content_align_mobile

  assign button_align = ""
  if content_align == "center"
    assign button_align = "center"
  elsif content_align == "right"
    assign button_align = "end"
  else
    assign button_align = "start"
  endif

  assign button_align_mobile = ""
  if content_align_mobile == "center"
    assign button_align_mobile = "center"
  elsif content_align_mobile == "right"
    assign button_align_mobile = "end"
  else
    assign button_align_mobile = "start"
  endif

  assign heading = section.settings.heading
  assign heading_size = section.settings.heading_size
  assign heading_size_mobile = section.settings.heading_size_mobile
  assign heading_color = section.settings.heading_color
  assign heading_custom = section.settings.heading_custom
  assign heading_font = section.settings.heading_font
  assign heading_height = section.settings.heading_height

  assign sub_heading = section.settings.sub_heading
  assign sub_heading_size = section.settings.sub_heading_size
  assign sub_heading_size_mobile = section.settings.sub_heading_size_mobile
  assign sub_heading_color = section.settings.sub_heading_color
  assign sub_heading_custom = section.settings.sub_heading_custom
  assign sub_heading_font = section.settings.sub_heading_font
  assign sub_heading_height = section.settings.sub_heading_height
  assign sub_heading_mt = section.settings.sub_heading_mt
  assign sub_heading_mt_mobile = section.settings.sub_heading_mt_mobile

  assign icons_autoplay_delay = section.settings.icons_autoplay_delay
  assign use_autoplay = section.settings.use_autoplay

  assign icons_padding_vertical = section.settings.icons_padding_vertical
  assign icons_padding_vertical_mobile = section.settings.icons_padding_vertical_mobile
  assign icons_padding_horizontal = section.settings.icons_padding_horizontal
  assign icons_padding_horizontal_mobile = section.settings.icons_padding_horizontal_mobile
  assign icons_radius = section.settings.icons_radius
  assign icons_gap = section.settings.icons_gap
  assign icons_gap_mobile = section.settings.icons_gap_mobile
  assign icons_mt = section.settings.icons_mt
  assign icons_mt_mobile = section.settings.icons_mt_mobile
  assign icons_bg_color = section.settings.icons_bg_color

  assign circle_color = section.settings.circle_color
  assign circle_active_color = section.settings.circle_active_color

  assign icon_size = section.settings.icon_size
  assign icon_size_mobile = section.settings.icon_size_mobile
  assign icon_bg_color = section.settings.icon_bg_color
  assign icon_color = section.settings.icon_color

  assign tab_title_custom = section.settings.tab_title_custom
  assign tab_title_font = section.settings.tab_title_font
  assign tab_title_size = section.settings.tab_title_size
  assign tab_title_size_mobile = section.settings.tab_title_size_mobile
  assign tab_title_height = section.settings.tab_title_height
  assign tab_title_mt = section.settings.tab_title_mt
  assign tab_title_mt_mobile = section.settings.tab_title_mt_mobile
  assign tab_title_color = section.settings.tab_title_color  

  assign title_custom = section.settings.title_custom
  assign title_font = section.settings.title_font
  assign title_size = section.settings.title_size
  assign title_size_mobile = section.settings.title_size_mobile
  assign title_height = section.settings.title_height
  assign title_color = section.settings.title_color
  assign title_mt = section.settings.title_mt
  assign title_mt_mobile = section.settings.title_mt_mobile

  assign text_custom = section.settings.text_custom
  assign text_font = section.settings.text_font
  assign text_size = section.settings.text_size
  assign text_size_mobile = section.settings.text_size_mobile
  assign text_height = section.settings.text_height
  assign text_color = section.settings.text_color
  assign text_mt = section.settings.text_mt
  assign text_mt_mobile = section.settings.text_mt_mobile

  assign sub_text_custom = section.settings.sub_text_custom
  assign sub_text_font = section.settings.sub_text_font
  assign sub_text_size = section.settings.sub_text_size
  assign sub_text_size_mobile = section.settings.sub_text_size_mobile
  assign sub_text_height = section.settings.sub_text_height
  assign sub_text_color = section.settings.sub_text_color
  assign sub_text_mt = section.settings.sub_text_mt
  assign sub_text_mt_mobile = section.settings.sub_text_mt_mobile

  assign button = section.settings.button
  assign button_url = section.settings.button_url
  assign button_size = section.settings.button_size
  assign button_size_mobile = section.settings.button_size_mobile
  assign button_color = section.settings.button_color
  assign button_hover_color = section.settings.button_hover_color
  assign button_custom = section.settings.button_custom
  assign button_font = section.settings.button_font
  assign button_height = section.settings.button_height
  assign button_padding_vertical = section.settings.button_padding_vertical
  assign button_padding_vertical_mobile = section.settings.button_padding_vertical_mobile
  assign button_padding_horizontal = section.settings.button_padding_horizontal
  assign button_padding_horizontal_mobile = section.settings.button_padding_horizontal_mobile
  assign button_radius = section.settings.button_radius
  assign button_border_thickness = section.settings.button_border_thickness
  assign button_border_color = section.settings.button_border_color
  assign button_border_hover_color = section.settings.button_border_hover_color
  assign button_bg_color = section.settings.button_bg_color
  assign button_bg_hover_color = section.settings.button_bg_hover_color
  assign button_style = section.settings.button_style
  assign button_mt = section.settings.button_mt
  assign button_mt_mobile = section.settings.button_mt_mobile
-%}

{%- style -%}
  {{  heading_font | font_face: font_display: 'swap' }}
  {{  sub_heading_font | font_face: font_display: 'swap' }}
  {{  button_font | font_face: font_display: 'swap' }}
  {{  tab_title_font | font_face: font_display: 'swap' }}
  {{  title_font | font_face: font_display: 'swap' }}
  {{  text_font | font_face: font_display: 'swap' }}
  {{  sub_text_font | font_face: font_display: 'swap' }}

  .section-{{ section.id }} {
    border-top: solid {{ border_color }} {{ border_thickness }}px;
    border-bottom: solid {{ border_color }} {{ border_thickness }}px;
    margin-top: {{ margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ margin_bottom | times: 0.75 | round: 0 }}px;
    margin-left: {{ margin_horizontal_mobile }}rem;
    margin-right: {{ margin_horizontal_mobile }}rem;
    border-radius: {{ section_radius | times: 0.6 | round: 0 }}px;
    overflow: hidden;
  }
  
  .section-{{ section.id }}-settings {
    margin: 0 auto;
    padding-top: {{ padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: {{ padding_horizontal_mobile }}rem;
    padding-right: {{ padding_horizontal_mobile }}rem;
  }

  .feature-body-{{ section.id }} {
    display: block;
  }

  .feature-content-{{ section.id }} {
    display: flex;
    flex-direction: column;
    grid-area: content;
  }

  .feature-images-mobile-{{ section.id }} {
    margin-top: {{ images_mt_mobile }}px;
    width: 100%;
    display: block;
  }

  .feature-images-{{ section.id }} {
    display: none;
  }

  .feature-image-{{ section.id }} {
    display: none;  
    width: 100%;
    height: 100%;
    text-decoration: none;
    box-sizing: border-box !important;
    border-radius: {{ image_radius }}px;    
    overflow: hidden;
    transition: all 0.25s ease 0s;
  }

  .feature-image-{{ section.id }}.active {
    -webkit-animation: opacity{{ section.id | replace: '-', '' }} 0.5s linear;
    animation: opacity{{ section.id | replace: '-', '' }} 0.5s linear;    
    display: block;
    transition: all 0.25s ease 0s;
  }

  @keyframes opacity{{ section.id | replace: '-', '' }} {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  .feature-image-{{ section.id }} img,
  .feature-image-{{ section.id }} svg {
    display: block;     
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .feature-image-{{ section.id }} svg {
    background-color: #AFAFAF;
  }

  .feature-heading-{{ section.id }} {
    text-align: {{ content_align_mobile }};
    width: 100%;
  }

  .feature-heading-{{ section.id }} * {
    margin: 0px;
    font-size: {{ heading_size_mobile }}px;
    color: {{ heading_color }};
    line-height: {{ heading_height }}%;
    text-transform: unset;
  }

  .feature-subheading-{{ section.id }} {    
    margin-top: {{ sub_heading_mt_mobile }}px;
    text-align: {{ content_align_mobile }};
    width: 100%;
  }

  .feature-subheading-{{ section.id }} * {
    margin: 0px;
    font-size: {{ sub_heading_size_mobile }}px;
    color: {{ sub_heading_color }};
    line-height: {{ sub_heading_height }}%;
    text-transform: unset;
  }

  .feature-tabs-icons-{{ section.id }} {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: {{ icons_mt_mobile }}px;
    gap: {{ icons_gap_mobile }}px;
    padding: {{ icons_padding_vertical_mobile }}px {{ icons_padding_horizontal_mobile }}px;
    border-radius: {{ icons_radius }}px;
    background-color: {{ icons_bg_color }};
  }

  .feature-tabs-icon-{{ section.id }} {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: {{ icon_bg_color }};
    padding: {{ icon_size_mobile | times: 0.75 }}px;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    aspect-ratio: 1/1;
  }

  .feature-tabs-icon-{{ section.id }} img,
  .feature-tabs-icon-{{ section.id }} svg {
    display: block;
    height: {{ icon_size_mobile }}px;
    width: {{ icon_size_mobile }}px;
    object-fit: contain;
  }

  .feature-progress-circle-{{ section.id }} {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    margin: auto;
    z-index: 1;
    transform: rotate(-90deg);
  }

  .feature-background-{{ section.id }} {
    stroke: {{ circle_color }};
  }

  .feature-progress-{{ section.id }} {
    stroke: {{ circle_active_color }};
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
  }

  .feature-tabs-icon-{{ section.id }}.active .feature-progress-{{ section.id }} {
    -webkit-animation: stroke{{ section.id | replace: '-', '' }} {{ icons_autoplay_delay }}s linear;
    animation: stroke{{ section.id | replace: '-', '' }} {{ icons_autoplay_delay }}s linear;   
  }
  
  @keyframes stroke{{ section.id | replace: '-', '' }} {
    0% {
      stroke-dashoffset: 0;
    }
    100% {
      stroke-dashoffset: 283;
    }
  }

  .feature-tabs-icon-{{ section.id }}.static-active .feature-progress-{{ section.id }} {
    stroke-dasharray: 0;
    stroke-dashoffset: 0;
  }

  .feature-tabs-icon-{{ section.id }} svg path {
    fill: {{ icon_color }};
  }  

  .feature-tabs-text-body-{{ section.id }} {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0px {{ icons_padding_horizontal_mobile }}px;
    gap: {{ icons_gap_mobile }}px;
  }

  .feature-tabs-title-{{ section.id }} {
    text-align: center;
    margin: 0px;
    margin-top: {{ tab_title_mt_mobile }}px;
    font-size: {{ tab_title_size_mobile }}px;
    color: {{ tab_title_color }};
    line-height: {{ tab_title_height }}%;
    text-transform: unset;
    word-break: break-word;
    width: calc({{ icon_size_mobile }}px +  ( {{ icon_size_mobile | times: 0.75 }}px * 2));
  }

  .feature-text-body-{{ section.id }} {
    margin-top: {{ title_mt_mobile }}px;
    position: relative;
  }

  .feature-text-content-{{ section.id }} {
    display: flex;
    align-items: center;
    flex-direction: column; 
    transition: opacity 0.5s ease, transform 0.5s ease;
    pointer-events: none; 
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    transform: translateX(-20%);
    opacity: 0;
    pointer-events: none;
  }

  .feature-text-content-{{ section.id }}.active {
    transform: translateX(0%);
    opacity: 1;
    pointer-events: all;
  }

  .feature-title-{{ section.id }} {
    margin: 0px;
    width: 100%;
    font-size: {{ title_size_mobile }}px;
    color: {{ title_color }};
    line-height: {{ title_height }}%;
    text-align: {{ content_align_mobile }};
    text-transform: unset;
  }

  .feature-text-{{ section.id }} {
    margin-top: {{ text_mt_mobile }}px;
    width: 100%;
    text-align: {{ content_align_mobile }};
  }

  .feature-text-{{ section.id }} * {
    margin: 0px;    
    font-size: {{ text_size_mobile }}px;
    color: {{ text_color }};
    line-height: {{ text_height }}%;
    text-transform: unset;
  }

  .feature-subtext-{{ section.id }} {
    width: 100%;
    margin-top: {{ sub_text_mt_mobile }}px;
    text-align: {{ content_align_mobile }};
  }

  .feature-subtext-{{ section.id }} * {
    margin: 0px;
    
    font-size: {{ sub_text_size_mobile }}px;
    color: {{ sub_text_color }};
    line-height: {{ sub_text_height }}%;
    text-transform: unset;
  }
  
  .feature-button-wrapper-{{ section.id }} {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: {{ button_align_mobile }};
  }

  .feature-button-{{ section.id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: fit-content;
    gap: 10px;
    width: 100%;
    margin: 0;
    margin-top: {{ button_mt_mobile }}px;
    font-size: {{ button_size_mobile }}px;
    color: {{ button_color }};
    line-height: {{ button_height }}%;
    text-align: center;
    text-transform: unset;
    text-decoration: none;
    padding: {{ button_padding_vertical_mobile }}px {{ button_padding_horizontal_mobile }}px;
    border-radius: {{ button_radius }}px;
    transition: all 0.25s ease 0s;
    background-color: transparent;
    border: 0px;
    cursor: pointer;
  }

  .feature-button-inner-{{ section.id }} {
    margin: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .feature-button-{{ section.id }}:hover {
    color: {{ button_hover_color }};
    transition: all 0.25s ease 0s;
  }

  .feature-button-{{ section.id }} svg {
    width: 14px;
    height: 14px;
  }

  .feature-button-{{ section.id }} svg path {
    fill: {{ button_color }};
    transition: all 0.25s ease 0s;
  }

  .feature-button-{{ section.id }}:hover svg path {
    fill: {{ button_hover_color }};
    transition: all 0.25s ease 0s;
  }

  @media(min-width: 1024px) {

    .section-{{ section.id }} {
      margin-top: {{ margin_top }}px;
      margin-bottom: {{ margin_bottom }}px;
      margin-left: {{ margin_horizontal }}rem;
      margin-right: {{ margin_horizontal }}rem;
      border-radius: {{ section_radius }}px;
    }
    
    .section-{{ section.id }}-settings {
      padding: 0 5rem;
      padding-top: {{ padding_top }}px;
      padding-bottom: {{ padding_bottom }}px;
      padding-left: {{ padding_horizontal }}rem;
      padding-right: {{ padding_horizontal }}rem;
    }

    .feature-body-{{ section.id }} {
      display: grid;
      gap: {{ body_gap }}px;
      align-items: center;
    }

    .feature-heading-{{ section.id }} {
      text-align: {{ content_align }};
    }
  
    .feature-heading-{{ section.id }} * {
      font-size: {{ heading_size }}px;
    }
    
    .feature-subheading-{{ section.id }} {
      margin-top: {{ sub_heading_mt }}px;
      text-align: {{ content_align }};
    }
  
    .feature-subheading-{{ section.id }} * {
      font-size: {{ sub_heading_size }}px;
    } 

    .feature-images-mobile-{{ section.id }} {
      display: none;
    }

    .feature-images-{{ section.id }} {
      display: block;
    }

    .feature-tabs-icons-{{ section.id }} {
      margin-top: {{ icons_mt }}px;
      gap: {{ icons_gap }}px;
      padding: {{ icons_padding_vertical }}px {{ icons_padding_horizontal }}px;
      justify-content: space-between;
      width: 100%;
    }

    .feature-tabs-icon-{{ section.id }} {
      padding: {{ icon_size | times: 0.75 }}px; 
    }

    .feature-tabs-icon-{{ section.id }} svg,
    .feature-tabs-icon-{{ section.id }} img {
      height: {{ icon_size }}px;
      width: {{ icon_size }}px;
    }

    .feature-tabs-text-body-{{ section.id }} {
      padding: 0px {{ icons_padding_horizontal }}px;
      gap: {{ icons_gap }}px;
    }

    .feature-text-body-{{ section.id }} {
      margin-top: {{ title_mt }}px;
    }

    .feature-tabs-title-{{ section.id }} {
      font-size: {{ tab_title_size }}px;
      margin-top: {{ tab_title_mt }}px;
      width: calc({{ icon_size }}px +  ( {{ icon_size | times: 0.75 }}px * 2));
    }

    .feature-title-{{ section.id }} {
      font-size: {{ title_size }}px;
      text-align: {{ content_align }};
    }

    .feature-text-{{ section.id }} {
      text-align: {{ content_align }};
      margin-top: {{ text_mt }}px
    }

    .feature-text-{{ section.id }} * {      
      font-size: {{ text_size }}px;
    }
    
    .feature-subtext-{{ section.id }} {
      text-align: {{ content_align }};
      margin-top: {{ sub_text_mt }}px
    }

    .feature-subtext-{{ section.id }} * {
      font-size: {{ sub_text_size }}px;
    }

    .feature-button-wrapper-{{ section.id }} {
      justify-content: {{  button_align }};
    }

    .feature-button-{{ section.id }} {
      margin-top: {{ button_mt }}px;
      padding: {{ button_padding_vertical }}px {{ button_padding_horizontal }}px;
      font-size: {{ button_size }}px;
    }
  }
  
{%- endstyle -%}

{% unless full_width %}
  <style>
    .section-{{ section.id }}-settings {
      max-width: {{ content_width }}px;
    }
  </style>
{% endunless %}

{% if margin_horizontal_mobile > 0 %}
  <style>
    .section-{{ section.id }} {
      border-left: solid {{ border_color }} {{ border_thickness }}px;
      border-right: solid {{ border_color }} {{ border_thickness }}px;
    }
    
    @media(min-width: 1024px) {
      .section-{{ section.id }} {
        border-left: 0px;
        border-right: 0px;
      }
    }
  </style>
{% endif %}

{% if margin_horizontal > 0 %}
  <style>
    @media(min-width: 1024px) {
      .section-{{ section.id }} {
        border-left: solid {{ border_color }} {{ border_thickness }}px;
        border-right: solid {{ border_color }} {{ border_thickness }}px;
      }
    }
  </style>
{% endif %}

{% if heading_custom %}
  <style>
    .feature-heading-{{ section.id }} * {
      font-family: {{ heading_font.family }}, {{ heading_font.fallback_families }};
      font-weight: {{ heading_font.weight }};
      font-style: {{ heading_font.style }};
    }
  </style>
{% endif %}

{% if sub_heading_custom %}
  <style>
    .feature-subheading-{{ section.id }} * {
      font-family: {{ sub_heading_font.family }}, {{ sub_heading_font.fallback_families }};
      font-weight: {{ sub_heading_font.weight }};
      font-style: {{ sub_heading_font.style }};
    }
  </style>
{% endif %}

{% if tab_title_custom %}
  <style>
    .feature-tabs-title-{{ section.id }} {
      font-family: {{ tab_title_font.family }}, {{ tab_title_font.fallback_families }};
      font-weight: {{ tab_title_font.weight }};
      font-style: {{ tab_title_font.style }};
    }
  </style>
{% endif %}

{% if title_custom %}
  <style>
    .feature-title-{{ section.id }} {
      font-family: {{ title_font.family }}, {{ title_font.fallback_families }};
      font-weight: {{ title_font.weight }};
      font-style: {{ title_font.style }};
    }
  </style>
{% endif %}

{% if text_custom %}
  <style>
    .feature-text-{{ section.id }}  * {
      font-family: {{ text_font.family }}, {{ text_font.fallback_families }};
      font-weight: {{ text_font.weight }};
      font-style: {{ text_font.style }};
    }
  </style>
{% endif %}

{% if sub_text_custom %}
  <style>
    .feature-subtext-{{ section.id }}  * {
      font-family: {{ sub_text_font.family }}, {{ sub_text_font.fallback_families }};
      font-weight: {{ sub_text_font.weight }};
      font-style: {{ sub_text_font.style }};
    }
  </style>
{% endif %}

{% if button_custom %}
  <style>
    .feature-button-{{ section.id }} {
      font-family: {{ button_font.family }}, {{ button_font.fallback_families }};
      font-weight: {{ button_font.weight }};
      font-style: {{ button_font.style }};
    }
  </style>
{% endif %}

{% if body_layout == "image_text" %}
  <style>
    @media (min-width: 1024px) {
      .feature-body-{{ section.id }} {
        grid-template-columns: {{ image_width }}% 1fr;
        grid-template-areas: "image content";
      }
    }
  </style>
{% else %}  
  {% if body_layout == "text_image" %}
    <style>
      @media (min-width: 1024px) {
        .feature-body-{{ section.id }} {
          grid-template-columns: 1fr {{ image_width }}%;
          grid-template-areas: "content image";
        }
      }
    </style>
  {% endif %}  
{% endif %}

{% if image_ratio_mobile == "portrait" %}
  <style>
    .feature-image-{{ section.id }} img,
    .feature-image-{{ section.id }} svg {
      aspect-ratio: 9.6/13;
    }
  </style>
{% elsif image_ratio_mobile == "landscape" %}
  <style>
  .feature-image-{{ section.id }} img,
  .feature-image-{{ section.id }} svg {
    aspect-ratio: 12/8;
  }
  </style>
{% elsif image_ratio_mobile == "square" %}
  <style>
    .feature-image-{{ section.id }} img,
    .feature-image-{{ section.id }} svg {
      aspect-ratio: 12/12;
    }
  </style>
{% else %}
  <style>
    .feature-image-{{ section.id }} img,
    .feature-image-{{ section.id }} svg {
      aspect-ratio: auto;
    }
  </style>
{% endif %}

{% if image_ratio == "portrait" %}
  <style>
    @media(min-width: 1024px) {
      .feature-image-{{ section.id }} img,
      .feature-image-{{ section.id }} svg {
        aspect-ratio: 9.6/13;
      }
    }
    
  </style>
{% elsif image_ratio == "landscape" %}
  <style>
    @media(min-width: 1024px) {
      .feature-image-{{ section.id }} img,
      .feature-image-{{ section.id }} svg {
        aspect-ratio: 12/8;
      }
    }  
  </style>
{% elsif image_ratio == "square" %}
  <style>
    @media(min-width: 1024px) {
      .feature-image-{{ section.id }} img,
      .feature-image-{{ section.id }} svg {
        aspect-ratio: 12/12;
      }
    }    
  </style>
{% else %}
  <style>
     @media(min-width: 1024px) {
      .feature-image-{{ section.id }} img,
      .feature-image-{{ section.id }} svg {
        aspect-ratio: auto;
      }
    }
  </style>
{% endif %}

{% if button_style == "non_outline" or button_style == "non_outline_arrow" %}
  <style>
    .feature-button-{{ section.id }} {
      background-color: {{ button_bg_color }};
    }

    .feature-button-{{ section.id }}:hover {
      background-color: {{ button_bg_hover_color }};
      transition: all 0.25s ease 0s;
    }

    .feature-button-{{ section.id }}:hover svg path {
      transition: all 0.25s ease 0s;
    }
  </style>
{% elsif button_style == "outline" or button_style == "outline_arrow" %}
  <style>
    .feature-button-{{ section.id }} {
      background-color: {{ button_bg_color }};
      border: {{ button_border_thickness }}px solid {{ button_border_color }};
    }

    .feature-button-{{ section.id }}:hover {
      background-color: {{ button_bg_hover_color }};
      border: {{ button_border_thickness }}px solid {{ button_border_hover_color }};
      transition: all 0.25s ease 0s;
    }

    .feature-button-{{ section.id }}:hover svg path {
      transition: all 0.25s ease 0s;
    }
  </style>
{% elsif button_style == "link" %}
  <style>
    .feature-button-{{ section.id }} {
      padding: 0px !important;
    }
  </style>
{% endif %}

<div class="section-{{ section.id }} feature-{{ section.id }}" style="background-color:{{ background_color }}; background-image: {{ background_gradient }};">
    <div class="section-{{ section.id }}-settings">
      <div class="feature-body-{{ section.id }}">
        <div class="feature-content-{{ section.id }}">
          {% if heading != blank %}
            <div class="feature-heading-{{ section.id }}">
              {{ heading }}
            </div>
          {% endif %}
          {% if sub_heading != blank %}
            <div class="feature-subheading-{{ section.id }}">
              {{ sub_heading }}
            </div>
          {% endif %}  
          <div class="feature-images-mobile-{{ section.id }}">     
            {% for block in section.blocks %}
              <div class="feature-image-{{ section.id }} {% if forloop.first %}active{% endif %}" data-id="{{ block.id }}">
                {% if block.settings.image != blank %}
                  <img src="{{ block.settings.image | image_url }}" alt="{{ block.settings.image.alt }}" {% if lazy %}loading="lazy"{% endif %}>      
                {% else %}
                  {{ 'image' | placeholder_svg_tag }}
                {% endif %} 
              </div>            
            {% endfor %}       
          </div>    
          <div class="feature-tabs-icons-{{ section.id }}">
            {% for block in section.blocks %}
              {% if block.settings.custom_icon != blank or block.settings.icon != "none" %}
                <div class="feature-tabs-icon-{{ section.id }} {% if use_autoplay %}{% if forloop.first %}active{% endif %}{% else %}{% if forloop.first %}static-active{% endif %} {% endif %}" id="{{ block.id }}">
                  <svg class="feature-progress-circle-{{ section.id }}" viewBox="0 0 100 100" data-id="{{ block.id }}">
                    <circle class="feature-background-{{ section.id }}" cx="50" cy="50" r="45" stroke-width="5" fill="none" />
                    <circle class="feature-progress-{{ section.id }}" cx="50" cy="50" r="45" stroke-width="5" fill="none" />
                  </svg>
                  {% if block.settings.custom_icon != blank %}
                    <img src="{{ block.settings.custom_icon | image_url }}" alt="{{ block.settings.custom_icon.alt }}">
                  {% else %}
                    {% case block.settings.icon %}
                      {% when 'atc' %}
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" fill="currentColor"><path d="M 20 4 A 1.0001 1.0001 0 0 0 19.292969 4.2929688 L 17.585938 6 L 9.3867188 6 C 7.5296852 6 5.9058511 7.2896965 5.4882812 9.0996094 L 3.2050781 19 L 2 19 C 0.9069372 19 0 19.906937 0 21 L 0 25 C 0 26.093063 0.9069372 27 2 27 L 2.4785156 27 L 7.8261719 45.544922 C 8.0711719 46.388922 8.985 47 10 47 L 30.267578 47 C 29.817578 46.375 29.425563 45.707 29.101562 45 L 10 45 C 9.856 45 9.7320469 44.928281 9.7480469 44.988281 L 4.5625 27 L 45.4375 27 L 44.855469 29.03125 C 45.483469 29.31025 46.084391 29.635672 46.650391 30.013672 L 47.519531 27 L 48 27 C 49.093063 27 50 26.093063 50 25 L 50 21 C 50 19.906937 49.093063 19 48 19 L 46.794922 19 L 44.509766 9.0996094 C 44.0923 7.2901497 42.470315 6 40.613281 6 L 32.414062 6 L 30.707031 4.2929688 A 1.0001 1.0001 0 0 0 30 4 L 20 4 z M 20.414062 6 L 29.585938 6 L 31.292969 7.7070312 A 1.0001 1.0001 0 0 0 32 8 L 40.613281 8 C 41.550248 8 42.351965 8.638241 42.5625 9.5507812 L 44.742188 19 L 5.2578125 19 L 7.4375 9.5507812 C 7.6479301 8.6386942 8.4497521 8 9.3867188 8 L 18 8 A 1.0001 1.0001 0 0 0 18.707031 7.7070312 L 20.414062 6 z M 2 21 L 48 21 L 48 25 L 2 25 L 2 21 z M 17 30 C 16.448 30 16 30.449 16 31 L 16 40.923828 C 16 41.474828 16.448 41.923828 17 41.923828 C 17.552 41.923828 18 41.474828 18 40.923828 L 18 31 C 18 30.449 17.552 30 17 30 z M 24.998047 30 C 24.446047 30 23.998047 30.449 23.998047 31 L 24 40.923828 C 24 41.474828 24.448 41.923828 25 41.923828 C 25.552 41.923828 26 41.474828 26 40.923828 L 25.998047 31 C 25.998047 30.449 25.550047 30 24.998047 30 z M 40 30 C 34.5 30 30 34.5 30 40 C 30 45.5 34.5 50 40 50 C 45.5 50 50 45.5 50 40 C 50 34.5 45.5 30 40 30 z M 40 32 C 44.4 32 48 35.6 48 40 C 48 44.4 44.4 48 40 48 C 35.6 48 32 44.4 32 40 C 32 35.6 35.6 32 40 32 z M 40 34.099609 C 39.4 34.099609 39 34.499609 39 35.099609 L 39 39 L 35.099609 39 C 34.499609 39 34.099609 39.4 34.099609 40 C 34.099609 40.6 34.499609 41 35.099609 41 L 39 41 L 39 44.900391 C 39 45.500391 39.4 45.900391 40 45.900391 C 40.6 45.900391 41 45.500391 41 44.900391 L 41 41 L 44.900391 41 C 45.500391 41 45.900391 40.6 45.900391 40 C 45.900391 39.4 45.500391 39 44.900391 39 L 41 39 L 41 35.099609 C 41 34.499609 40.6 34.099609 40 34.099609 z"/></svg>
                      {% when 'airplane' %}
                      <svg xmlns="http://www.w3.org/2000/svg" width="50px" height="50px" viewBox="0 0 50 50"><path d="M 46.40625 0.1875 C 46.085938 0.210938 45.757813 0.277344 45.4375 0.34375 C 44.15625 0.617188 42.78125 1.210938 41.4375 1.90625 C 38.765625 3.289063 36.34375 5.039063 35.3125 6.125 L 35.28125 6.125 L 28.53125 12.84375 L 4.53125 9.59375 C 4.214844 9.558594 3.902344 9.675781 3.6875 9.90625 L 0.28125 13.40625 C 0.0507813 13.644531 -0.0507813 13.984375 0.0195313 14.308594 C 0.0898438 14.636719 0.316406 14.90625 0.625 15.03125 L 19.125 22.28125 L 9.59375 32 L 5 32 C 4.757813 32 4.523438 32.089844 4.34375 32.25 L 0.34375 35.625 C 0.046875 35.867188 -0.0859375 36.253906 0 36.625 C 0.0859375 37 0.378906 37.289063 0.75 37.375 L 10.28125 39.71875 L 12.625 49.25 C 12.710938 49.585938 12.96875 49.851563 13.296875 49.953125 C 13.628906 50.054688 13.988281 49.976563 14.25 49.75 L 17.65625 46.75 C 17.875 46.5625 18 46.289063 18 46 L 18 40.40625 L 27.8125 30.875 L 34.96875 49.375 C 35.09375 49.683594 35.363281 49.910156 35.691406 49.980469 C 36.015625 50.050781 36.355469 49.949219 36.59375 49.71875 L 40.09375 46.3125 C 40.324219 46.097656 40.441406 45.785156 40.40625 45.46875 L 37.15625 21.46875 L 43.90625 14.71875 C 44.945313 13.679688 46.707031 11.203125 48.09375 8.53125 C 48.789063 7.195313 49.382813 5.835938 49.65625 4.5625 C 49.929688 3.289063 49.933594 1.945313 49.03125 1 C 49.019531 0.988281 49.011719 0.980469 49 0.96875 C 48.527344 0.515625 47.957031 0.296875 47.34375 0.21875 C 47.039063 0.179688 46.726563 0.164063 46.40625 0.1875 Z M 46.5 2.21875 C 47.070313 2.179688 47.402344 2.296875 47.5625 2.4375 C 47.753906 2.652344 47.894531 3.191406 47.6875 4.15625 C 47.476563 5.136719 46.957031 6.386719 46.3125 7.625 C 45.023438 10.105469 43.164063 12.617188 42.5 13.28125 L 35.40625 20.40625 C 35.183594 20.613281 35.070313 20.914063 35.09375 21.21875 L 38.34375 45.25 L 36.3125 47.21875 L 29.125 28.75 C 29.007813 28.4375 28.738281 28.203125 28.414063 28.125 C 28.085938 28.050781 27.742188 28.144531 27.5 28.375 L 16.3125 39.28125 C 16.113281 39.46875 16.003906 39.726563 16 40 L 16 45.5625 L 14.15625 47.15625 L 12.0625 38.65625 C 11.972656 38.304688 11.695313 38.027344 11.34375 37.9375 L 3.125 35.90625 L 5.375 34 L 10 34 C 10.273438 33.996094 10.53125 33.886719 10.71875 33.6875 L 21.625 22.59375 C 21.855469 22.351563 21.949219 22.007813 21.875 21.679688 C 21.796875 21.355469 21.5625 21.085938 21.25 20.96875 L 2.78125 13.71875 L 4.75 11.65625 L 28.78125 14.90625 C 29.085938 14.929688 29.386719 14.816406 29.59375 14.59375 L 36.71875 7.5 C 37.296875 6.886719 39.851563 4.976563 42.34375 3.6875 C 43.589844 3.042969 44.855469 2.523438 45.84375 2.3125 C 46.085938 2.261719 46.308594 2.230469 46.5 2.21875 Z"></path></svg>
                      {% when 'gift' %}
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" fill="currentColor"><path d="M 12.3125 1 C 10.382813 1 8.945313 1.644531 8.09375 2.65625 C 7.242188 3.667969 7 4.925781 7 6 C 7 8.554688 8.382813 10.628906 10.28125 11.96875 C 10.867188 12.382813 11.488281 12.710938 12.15625 13 L 1 13 L 1 24 L 3 24 L 3 49 L 47 49 L 47 24 L 49 24 L 49 13 L 37.84375 13 C 38.511719 12.710938 39.132813 12.382813 39.71875 11.96875 C 41.617188 10.628906 43 8.554688 43 6 C 43 4.925781 42.757813 3.667969 41.90625 2.65625 C 41.054688 1.644531 39.617188 1 37.6875 1 C 35.691406 1 34.078125 1.84375 32.875 3 C 31.671875 4.15625 30.808594 5.613281 30 7 C 29.191406 8.386719 28.429688 9.71875 27.625 10.625 C 26.820313 11.53125 26.085938 12 25 12 C 23.914063 12 23.179688 11.53125 22.375 10.625 C 21.570313 9.71875 20.808594 8.386719 20 7 C 19.191406 5.613281 18.328125 4.15625 17.125 3 C 15.921875 1.84375 14.308594 1 12.3125 1 Z M 12.3125 3 C 13.777344 3 14.777344 3.53125 15.71875 4.4375 C 16.660156 5.34375 17.488281 6.636719 18.28125 8 C 19.074219 9.363281 19.851563 10.78125 20.875 11.9375 C 20.894531 11.960938 20.917969 11.976563 20.9375 12 L 17 12 C 14.984375 12 12.902344 11.398438 11.40625 10.34375 C 9.910156 9.289063 9 7.863281 9 6 C 9 5.246094 9.179688 4.5 9.625 3.96875 C 10.070313 3.4375 10.800781 3 12.3125 3 Z M 37.6875 3 C 39.199219 3 39.929688 3.4375 40.375 3.96875 C 40.820313 4.5 41 5.246094 41 6 C 41 7.863281 40.089844 9.289063 38.59375 10.34375 C 37.097656 11.398438 35.015625 12 33 12 L 29.0625 12 C 29.082031 11.976563 29.105469 11.960938 29.125 11.9375 C 30.148438 10.78125 30.925781 9.363281 31.71875 8 C 32.511719 6.636719 33.339844 5.34375 34.28125 4.4375 C 35.222656 3.53125 36.222656 3 37.6875 3 Z M 3 15 L 19 15 L 19 22 L 3 22 Z M 21 15 L 29 15 L 29 22.8125 C 29 22.84375 29 22.875 29 22.90625 C 29 22.9375 29 22.96875 29 23 C 28.992188 23.074219 28.992188 23.144531 29 23.21875 L 29 47 L 21 47 L 21 23.1875 C 21.007813 23.125 21.007813 23.0625 21 23 C 21.003906 22.957031 21.003906 22.917969 21 22.875 C 21 22.84375 21 22.8125 21 22.78125 Z M 31 15 L 47 15 L 47 22 L 31 22 Z M 5 24 L 19 24 L 19 47 L 5 47 Z M 31 24 L 45 24 L 45 47 L 31 47 Z"/></svg>
                      {% when 'heart' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 15 7 C 7.832031 7 2 12.832031 2 20 C 2 34.761719 18.695313 42.046875 24.375 46.78125 L 25 47.3125 L 25.625 46.78125 C 31.304688 42.046875 48 34.761719 48 20 C 48 12.832031 42.167969 7 35 7 C 30.945313 7 27.382813 8.925781 25 11.84375 C 22.617188 8.925781 19.054688 7 15 7 Z M 15 9 C 18.835938 9 22.1875 10.96875 24.15625 13.9375 L 25 15.1875 L 25.84375 13.9375 C 27.8125 10.96875 31.164063 9 35 9 C 41.085938 9 46 13.914063 46 20 C 46 32.898438 31.59375 39.574219 25 44.78125 C 18.40625 39.574219 4 32.898438 4 20 C 4 13.914063 8.914063 9 15 9 Z"/></svg>
                      {% when 'bi-ecology' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 2 C 18.243757 2 12.467768 6.2125094 10.125 12.144531 C 10.085545 12.133602 10.048622 12.114939 10.015625 12.109375 C 9.7892588 12.071205 9.5904654 12.065054 9.0976562 12.052734 C 6.863648 11.997017 5 13.818575 5 16.052734 L 5 28 C 5 29.492222 4.971214 30.467798 5.1113281 31.347656 C 5.2514426 32.227515 5.6416929 33.001322 6.265625 33.677734 L 6.2109375 33.615234 C 6.2109375 33.615234 11.739969 40.71043 13 42.34375 L 13 45.140625 C 13 45.880703 13.509154 46.563206 13.994141 46.794922 C 14.479126 47.026638 14.9 47 15 47 L 22 47 C 22.69 47 23.38702 46.720034 23.71875 46.199219 C 24.05048 45.678403 24 45.215 24 45 L 24 40 C 24 39.93625 23.996198 39.82221 23.986328 39.701172 C 23.934188 37.373116 23.187577 35.408453 22.136719 33.738281 C 23.06681 33.906742 24.02325 34 25 34 C 25.977285 34 26.932847 33.906452 27.863281 33.738281 C 26.812433 35.408443 26.065812 37.373116 26.013672 39.701172 C 26.003781 39.82221 26 39.936253 26 40 L 26 45 C 26 45.215 25.94952 45.678403 26.28125 46.199219 C 26.61298 46.720034 27.31 47 28 47 L 35 47 C 35.1 47 35.520873 47.02664 36.005859 46.794922 C 36.490846 46.563206 37 45.880703 37 45.140625 L 37 42.34375 C 38.260031 40.71043 43.789063 33.615234 43.789062 33.615234 L 43.734375 33.677734 C 44.358307 33.001322 44.748557 32.227515 44.888672 31.347656 C 45.028789 30.467798 45 29.492222 45 28 L 45 16.052734 C 45 13.818575 43.136352 11.997017 40.902344 12.052734 C 40.409535 12.065054 40.210741 12.071205 39.984375 12.109375 C 39.951378 12.114938 39.914752 12.133701 39.875 12.144531 C 37.532232 6.2125094 31.756243 2 25 2 z M 24 4.4589844 L 24 9.9785156 L 20.15625 9.9785156 C 20.390284 9.1941103 20.654861 8.4639151 20.957031 7.8164062 C 21.85745 5.8869354 22.936084 4.9241537 24 4.4589844 z M 26 4.4589844 C 27.063916 4.9241537 28.14255 5.8869354 29.042969 7.8164062 C 29.345139 8.4639151 29.609716 9.1941103 29.84375 9.9785156 L 26 9.9785156 L 26 4.4589844 z M 20.427734 4.7753906 C 19.953022 5.4286529 19.521369 6.1631941 19.144531 6.9707031 C 18.722007 7.8761143 18.366844 8.890798 18.072266 9.9785156 L 13.529297 9.9785156 C 15.205408 7.583736 17.610667 5.7467395 20.427734 4.7753906 z M 29.572266 4.7753906 C 32.389333 5.7467395 34.794592 7.583736 36.470703 9.9785156 L 31.927734 9.9785156 C 31.633156 8.890798 31.277993 7.8761143 30.855469 6.9707031 C 30.478631 6.1631941 30.046978 5.4286529 29.572266 4.7753906 z M 12.349609 12.021484 L 17.613281 12.021484 C 17.326028 13.582368 17.14784 15.255081 17.09375 17 L 11.050781 17 C 11.175858 15.226956 11.627403 13.549812 12.349609 12.021484 z M 19.648438 12.021484 L 24 12.021484 L 24 17 L 19.09375 17 C 19.153581 15.238699 19.343602 13.554689 19.648438 12.021484 z M 26 12.021484 L 30.351562 12.021484 C 30.656398 13.554689 30.846419 15.238699 30.90625 17 L 26 17 L 26 12.021484 z M 32.386719 12.021484 L 37.650391 12.021484 C 38.372597 13.549812 38.824142 15.226956 38.949219 17 L 32.90625 17 C 32.85216 15.255081 32.673972 13.582368 32.386719 12.021484 z M 9 14.070312 L 9 18 L 9 28 A 1.0001 1.0001 0 0 0 9.1171875 28.482422 C 9.1060355 29.257662 9.2798116 30.042082 9.7773438 30.705078 L 14.199219 36.599609 A 1.0003905 1.0003905 0 1 0 15.800781 35.400391 L 11.378906 29.503906 A 1.0001 1.0001 0 0 0 11.376953 29.503906 C 10.82939 28.774241 10.974882 27.770618 11.705078 27.222656 L 11.707031 27.21875 C 12.380712 26.71349 13.305518 26.796155 13.878906 27.412109 L 18.328125 32.191406 A 1.0001 1.0001 0 0 0 18.351562 32.214844 C 20.319412 34.192246 21.926705 36.547666 21.990234 39.769531 A 1.0001 1.0001 0 0 0 21.998047 39.875 C 21.996608 39.863397 22 39.92 22 40 L 22 45 L 15 45 L 15 42 A 1.0001 1.0001 0 0 0 14.792969 41.390625 C 13.747323 40.030271 7.7890625 32.384766 7.7890625 32.384766 A 1.0001 1.0001 0 0 0 7.734375 32.322266 C 7.2883066 31.838678 7.178323 31.613344 7.0859375 31.033203 C 6.993552 30.453062 7 29.507778 7 28 L 7 16.052734 C 7 14.935182 7.8914424 14.071464 9 14.070312 z M 41 14.070312 C 42.108558 14.071513 43 14.935182 43 16.052734 L 43 28 C 43 29.507778 43.006403 30.453062 42.914062 31.033203 C 42.821672 31.613344 42.711693 31.838678 42.265625 32.322266 A 1.0001 1.0001 0 0 0 42.210938 32.384766 C 42.210938 32.384766 36.252677 40.030271 35.207031 41.390625 A 1.0001 1.0001 0 0 0 35 42 L 35 45 L 28 45 L 28 40 C 28 39.92 28.003353 39.863405 28.001953 39.875 A 1.0001 1.0001 0 0 0 28.009766 39.769531 C 28.073296 36.547666 29.680588 34.192246 31.648438 32.214844 A 1.0001 1.0001 0 0 0 31.671875 32.191406 L 36.121094 27.412109 C 36.694482 26.796155 37.619288 26.71349 38.292969 27.21875 L 38.294922 27.222656 C 39.025118 27.770618 39.17061 28.774241 38.623047 29.503906 A 1.0001 1.0001 0 0 0 38.621094 29.503906 L 34.199219 35.400391 A 1.0003905 1.0003905 0 0 0 35.800781 36.599609 L 40.222656 30.705078 L 40.222656 30.703125 C 40.719432 30.040441 40.893957 29.25709 40.882812 28.482422 A 1.0001 1.0001 0 0 0 41 28 L 41 18 L 41 14.070312 z M 11.037109 19 L 17.09375 19 C 17.159604 21.124425 17.405883 23.143498 17.816406 24.978516 L 14 24.978516 A 1.0216021 1.0216021 0 0 0 13.558594 25.072266 C 13.337049 25.029072 13.112143 25.006274 12.884766 25.005859 C 11.847565 23.216072 11.190687 21.180902 11.037109 19 z M 19.09375 19 L 24 19 L 24 24.978516 L 19.873047 24.978516 C 19.438021 23.190789 19.166941 21.154593 19.09375 19 z M 26 19 L 30.90625 19 C 30.833059 21.154593 30.561979 23.190789 30.126953 24.978516 L 26 24.978516 L 26 19 z M 32.90625 19 L 38.962891 19 C 38.809334 21.180718 38.152513 23.215394 37.115234 25.005859 C 36.887857 25.006274 36.662951 25.029072 36.441406 25.072266 A 1.0216021 1.0216021 0 0 0 36 24.978516 L 32.183594 24.978516 C 32.594117 23.143498 32.840396 21.124425 32.90625 19 z M 16.248047 27.021484 L 18.367188 27.021484 C 18.597235 27.732684 18.8543 28.407372 19.144531 29.029297 C 19.52334 29.841031 19.957851 30.578652 20.435547 31.234375 C 20.402153 31.222853 20.367265 31.214892 20.333984 31.203125 L 20.28125 31.349609 C 20.111744 31.166771 19.943999 30.980033 19.771484 30.806641 L 16.248047 27.021484 z M 20.486328 27.021484 L 24 27.021484 L 24 31.541016 C 22.936084 31.075846 21.85745 30.113065 20.957031 28.183594 C 20.788189 27.821788 20.634266 27.427992 20.486328 27.021484 z M 26 27.021484 L 29.513672 27.021484 C 29.365734 27.427992 29.211811 27.821788 29.042969 28.183594 C 28.14255 30.113065 27.063916 31.075846 26 31.541016 L 26 27.021484 z M 31.632812 27.021484 L 33.751953 27.021484 L 30.230469 30.804688 L 30.228516 30.806641 C 30.137939 30.897678 30.050803 30.998173 29.960938 31.091797 C 29.834376 31.139823 29.7025 31.176269 29.574219 31.220703 C 30.047909 30.568287 30.479307 29.835358 30.855469 29.029297 C 31.1457 28.407372 31.402765 27.732684 31.632812 27.021484 z"/></svg>
                      {% when 'plant' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 10.96875 3 C 10.746094 3.023438 10.535156 3.125 10.375 3.28125 C 10.375 3.28125 3 10.136719 3 18.4375 C 3 22.863281 4.726563 27.210938 9.125 29.9375 C 9.601563 31.90625 9.945313 33.679688 9.6875 35.25 C 9.597656 35.617188 9.722656 36 10.007813 36.246094 C 10.292969 36.492188 10.695313 36.554688 11.042969 36.410156 C 11.390625 36.265625 11.628906 35.9375 11.65625 35.5625 C 12.199219 32.257813 10.878906 29.109375 10.125 25.625 C 9.371094 22.140625 9.078125 18.34375 11.25 13.5625 C 11.402344 13.25 11.382813 12.882813 11.195313 12.589844 C 11.011719 12.300781 10.691406 12.121094 10.34375 12.125 C 9.945313 12.132813 9.585938 12.378906 9.4375 12.75 C 7.0625 17.972656 7.394531 22.351563 8.1875 26.03125 C 8.253906 26.339844 8.335938 26.609375 8.40625 26.90625 C 5.945313 24.648438 5 21.679688 5 18.4375 C 5 12.105469 9.894531 6.78125 11.03125 5.59375 C 12.03125 6.742188 15.15625 10.453125 15.84375 14.625 C 16.3125 17.46875 15.550781 19.882813 14.5625 21.6875 C 14.070313 22.589844 13.523438 23.335938 13.0625 23.875 C 12.601563 24.414063 12.175781 24.804688 12.125 24.84375 C 11.792969 25.039063 11.601563 25.402344 11.632813 25.785156 C 11.660156 26.171875 11.910156 26.5 12.269531 26.640625 C 12.625 26.777344 13.03125 26.699219 13.3125 26.4375 C 13.625 26.203125 14.054688 25.820313 14.59375 25.1875 C 15.132813 24.554688 15.742188 23.699219 16.3125 22.65625 C 17.453125 20.570313 18.367188 17.683594 17.8125 14.3125 C 16.851563 8.476563 11.78125 3.3125 11.78125 3.3125 C 11.574219 3.089844 11.273438 2.976563 10.96875 3 Z M 28.90625 20.0625 C 28.21875 20.0625 27.523438 20.109375 26.8125 20.1875 C 19.246094 21.007813 15.714844 25.230469 14.34375 29.15625 C 12.972656 33.082031 13.5625 36.78125 13.5625 36.78125 C 13.5625 36.792969 13.5625 36.800781 13.5625 36.8125 C 10.496094 40.019531 9.125 42.84375 9.125 42.84375 C 8.914063 43.167969 8.90625 43.585938 9.105469 43.914063 C 9.308594 44.246094 9.683594 44.429688 10.070313 44.386719 C 10.453125 44.34375 10.78125 44.085938 10.90625 43.71875 C 10.90625 43.71875 14.460938 36.398438 23.125 32.3125 C 29.035156 29.523438 36.6875 30.375 36.6875 30.375 C 37.0625 30.472656 37.460938 30.34375 37.707031 30.042969 C 37.953125 29.746094 38.007813 29.332031 37.839844 28.980469 C 37.675781 28.628906 37.324219 28.40625 36.9375 28.40625 C 36.9375 28.40625 34.921875 28.148438 32.09375 28.28125 C 29.265625 28.414063 25.601563 28.933594 22.28125 30.5 C 19.539063 31.792969 17.273438 33.382813 15.4375 35 C 15.410156 33.734375 15.507813 31.84375 16.21875 29.8125 C 17.40625 26.417969 20.101563 22.941406 27.03125 22.1875 C 36.238281 21.1875 43.359375 27.707031 44.71875 29.03125 C 43.746094 30.746094 38.257813 39.722656 28.0625 40.84375 C 22.367188 41.46875 17.375 38.8125 17.375 38.8125 C 17.058594 38.589844 16.644531 38.570313 16.304688 38.757813 C 15.96875 38.949219 15.773438 39.316406 15.800781 39.703125 C 15.828125 40.089844 16.078125 40.421875 16.4375 40.5625 C 16.4375 40.5625 21.859375 43.519531 28.28125 42.8125 C 40.730469 41.441406 46.875 29.28125 46.875 29.28125 C 47.0625 28.910156 47 28.460938 46.71875 28.15625 C 46.71875 28.15625 39.214844 20.058594 28.90625 20.0625 Z"/></svg>
                      {% when 'shop' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 4 6 L 4 12 C 3.9375 12.308594 3 16.839844 3 19.5 C 3 20.878906 3.269531 22.195313 4 23.25 L 4 48 L 46 48 L 46 23.25 C 46.730469 22.195313 47 20.878906 47 19.5 C 47 16.867188 46.082031 12.464844 46 12.0625 C 46 12.042969 46 12.019531 46 12 L 46 6 Z M 6 8 L 44 8 L 44 11 L 39.1875 11 C 39.054688 10.972656 38.914063 10.972656 38.78125 11 L 32.21875 11 C 32.117188 10.972656 32.011719 10.960938 31.90625 10.96875 C 31.875 10.976563 31.84375 10.988281 31.8125 11 L 25.21875 11 C 25.117188 10.972656 25.011719 10.960938 24.90625 10.96875 C 24.875 10.976563 24.84375 10.988281 24.8125 11 L 18.21875 11 C 18.117188 10.972656 18.011719 10.960938 17.90625 10.96875 C 17.875 10.976563 17.84375 10.988281 17.8125 11 L 11.21875 11 C 11.117188 10.972656 11.011719 10.960938 10.90625 10.96875 C 10.875 10.976563 10.84375 10.988281 10.8125 11 L 6 11 Z M 5.875 13 L 10 13 L 10 21.875 C 9.359375 22.550781 8.496094 23 7.5 23 C 6.726563 23 6.238281 22.804688 5.875 22.4375 C 5.8125 22.316406 5.730469 22.210938 5.625 22.125 C 5.226563 21.550781 5 20.605469 5 19.5 C 5 17.527344 5.703125 13.917969 5.875 13 Z M 12 13 L 17 13 L 17 21.875 C 16.359375 22.550781 15.496094 23 14.5 23 C 13.503906 23 12.640625 22.550781 12 21.875 Z M 19 13 L 24 13 L 24 21.875 C 23.359375 22.550781 22.496094 23 21.5 23 C 20.503906 23 19.640625 22.550781 19 21.875 Z M 26 13 L 31 13 L 31 21.90625 C 30.363281 22.574219 29.492188 23 28.5 23 C 27.503906 23 26.640625 22.550781 26 21.875 Z M 33 13 L 38 13 L 38 21.90625 C 37.363281 22.574219 36.492188 23 35.5 23 C 34.507813 23 33.636719 22.574219 33 21.90625 Z M 40 13 L 44.125 13 C 44.296875 13.917969 45 17.527344 45 19.5 C 45 20.484375 44.820313 21.324219 44.5 21.90625 C 44.265625 22.042969 44.097656 22.269531 44.03125 22.53125 C 43.679688 22.839844 43.207031 23 42.5 23 C 41.507813 23 40.636719 22.574219 40 21.90625 Z M 32 23.53125 C 32.960938 24.371094 34.121094 25 35.5 25 C 36.878906 25 38.039063 24.371094 39 23.53125 C 39.960938 24.371094 41.121094 25 42.5 25 C 43.03125 25 43.542969 24.90625 44 24.75 L 44 46 L 41 46 L 41 28 L 30 28 L 30 46 L 6 46 L 6 24.75 C 6.457031 24.90625 6.96875 25 7.5 25 C 8.875 25 10.039063 24.398438 11 23.5625 C 11.960938 24.398438 13.125 25 14.5 25 C 15.875 25 17.039063 24.398438 18 23.5625 C 18.960938 24.398438 20.125 25 21.5 25 C 22.875 25 24.039063 24.398438 25 23.5625 C 25.960938 24.398438 27.125 25 28.5 25 C 29.878906 25 31.039063 24.371094 32 23.53125 Z M 9 28 L 9 43 L 27 43 L 27 28 Z M 11 30 L 25 30 L 25 41 L 11 41 Z M 32 30 L 39 30 L 39 46 L 32 46 Z"/></svg>
                      {% when 'pin' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 1 C 16.175781 1 9 8.175781 9 17 C 9 19.371094 10.007813 22.339844 11.4375 25.59375 C 12.867188 28.847656 14.738281 32.339844 16.625 35.59375 C 20.398438 42.097656 24.1875 47.5625 24.1875 47.5625 C 24.375 47.824219 24.679688 47.980469 25 47.980469 C 25.320313 47.980469 25.625 47.824219 25.8125 47.5625 C 25.8125 47.5625 29.601563 42.097656 33.375 35.59375 C 35.261719 32.339844 37.132813 28.847656 38.5625 25.59375 C 39.992188 22.339844 41 19.371094 41 17 C 41 8.175781 33.824219 1 25 1 Z M 25 3 C 32.742188 3 39 9.257813 39 17 C 39 18.769531 38.132813 21.636719 36.75 24.78125 C 35.367188 27.925781 33.488281 31.382813 31.625 34.59375 C 28.300781 40.320313 25.683594 44.15625 25 45.15625 C 24.316406 44.15625 21.699219 40.320313 18.375 34.59375 C 16.511719 31.382813 14.632813 27.925781 13.25 24.78125 C 11.867188 21.636719 11 18.769531 11 17 C 11 9.257813 17.257813 3 25 3 Z M 25 8 C 20.042969 8 16 12.042969 16 17 C 16 21.957031 20.042969 26 25 26 C 29.957031 26 34 21.957031 34 17 C 34 12.042969 29.957031 8 25 8 Z M 25 10 C 28.878906 10 32 13.121094 32 17 C 32 20.878906 28.878906 24 25 24 C 21.121094 24 18 20.878906 18 17 C 18 13.121094 21.121094 10 25 10 Z M 25 13 C 22.789063 13 21 14.789063 21 17 C 21 19.210938 22.789063 21 25 21 C 27.210938 21 29 19.210938 29 17 C 29 16.4375 28.894531 15.921875 28.6875 15.4375 C 28.414063 15.785156 27.980469 16 27.5 16 C 26.671875 16 26 15.328125 26 14.5 C 26 14.019531 26.214844 13.585938 26.5625 13.3125 C 26.078125 13.105469 25.5625 13 25 13 Z"/></svg>
                      {% when 'megaphone' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 39.851563 2 C 39.574219 2.003906 39.308594 2.125 39.125 2.332031 C 38.738281 2.484375 38.308594 2.5625 38.027344 2.835938 C 37.554688 3.285156 37.152344 3.835938 36.71875 4.464844 C 35.859375 5.71875 34.894531 7.285156 33.515625 8.835938 C 30.761719 11.933594 26.453125 15 18 15 C 17.960938 15 17.925781 15 17.886719 15.003906 L 7 15.003906 C 4.253906 15.003906 2.007813 17.253906 2.007813 20 L 2.007813 25 C 2.007813 27.121094 3.355469 28.933594 5.234375 29.652344 C 5.289063 29.714844 5.351563 29.769531 5.421875 29.816406 C 5.75 30.046875 6.621094 31.613281 7.335938 34.1875 C 8.046875 36.765625 8.773438 40.332031 9.605469 44.675781 C 9.765625 45.511719 10.042969 46.351563 10.691406 47 C 11.339844 47.652344 12.304688 48 13.445313 48 L 16.78125 48 C 17.703125 48 18.539063 47.820313 19.15625 47.25 C 19.777344 46.683594 20 45.84375 20 45 C 20 43.242188 18.953125 42.03125 17.792969 40.875 C 17.976563 41.058594 17.722656 40.71875 17.601563 40.140625 C 17.480469 39.558594 17.371094 38.71875 17.28125 37.667969 C 17.117188 35.777344 17.035156 33.117188 17.015625 29.996094 L 17.828125 29.996094 C 17.929688 30.011719 18.03125 30.011719 18.132813 30 C 18.144531 30 18.15625 30 18.167969 30 C 26.65625 30 30.925781 33.289063 33.664063 36.632813 C 35.03125 38.304688 35.988281 40 36.84375 41.347656 C 37.269531 42.023438 37.664063 42.613281 38.136719 43.097656 C 38.609375 43.582031 39.234375 44 40 44 L 40.074219 44 C 40.464844 44 40.859375 43.792969 41.105469 43.546875 C 41.347656 43.296875 41.503906 43.011719 41.640625 42.671875 C 41.921875 41.992188 42.136719 41.046875 42.332031 39.578125 C 42.640625 37.269531 42.871094 33.605469 42.957031 28 L 43.453125 28 C 45.757813 28 47.660156 26.25 47.875 23.972656 C 47.925781 23.851563 47.953125 23.71875 47.953125 23.585938 C 47.953125 21.113281 45.957031 19 43.453125 19 L 42.957031 19 C 42.867188 12.847656 42.585938 8.839844 42.207031 6.398438 C 41.980469 4.925781 41.738281 3.976563 41.433594 3.296875 C 41.28125 2.960938 41.117188 2.679688 40.871094 2.4375 C 40.625 2.199219 40.246094 2 39.851563 2 Z M 39.613281 4.148438 C 39.789063 4.539063 40.023438 5.34375 40.234375 6.703125 C 40.65625 9.460938 41 14.429688 41 23 C 41 31.574219 40.71875 36.550781 40.347656 39.3125 C 40.167969 40.671875 39.960938 41.46875 39.800781 41.863281 C 39.734375 41.828125 39.71875 41.851563 39.566406 41.699219 C 39.292969 41.417969 38.933594 40.914063 38.53125 40.277344 C 37.722656 39 36.707031 37.195313 35.210938 35.367188 C 32.308594 31.824219 27.492188 28.242188 18.996094 28.019531 L 18.996094 16.976563 C 27.320313 16.730469 32.101563 13.4375 35.011719 10.164063 C 36.523438 8.460938 37.554688 6.78125 38.371094 5.597656 C 38.777344 5.007813 39.132813 4.542969 39.40625 4.28125 C 39.53125 4.160156 39.546875 4.1875 39.613281 4.148438 Z M 7 16.996094 L 17.003906 16.996094 L 17.003906 28.003906 L 16.191406 28.003906 C 16.125 27.992188 16.054688 27.984375 15.984375 27.984375 C 15.925781 27.988281 15.863281 27.992188 15.808594 28.003906 L 7 28.003906 C 5.328125 28.003906 3.992188 26.671875 3.992188 25 L 3.992188 20 C 3.992188 18.328125 5.328125 16.996094 7 16.996094 Z M 42.980469 21 L 43.453125 21 C 44.816406 21 45.953125 22.195313 45.953125 23.585938 C 45.953125 24.976563 44.875 26 43.453125 26 L 42.972656 26 C 42.984375 25.003906 43 24.113281 43 23 C 43 22.257813 42.984375 21.6875 42.980469 21 Z M 8.007813 29.996094 L 15.015625 29.996094 C 15.03125 33.15625 15.117188 35.855469 15.285156 37.835938 C 15.378906 38.9375 15.496094 39.835938 15.644531 40.546875 C 15.792969 41.257813 15.847656 41.757813 16.378906 42.289063 C 17.488281 43.394531 18 43.992188 18 45 C 18 45.53125 17.898438 45.691406 17.804688 45.777344 C 17.710938 45.863281 17.4375 46 16.78125 46 L 13.445313 46 C 12.664063 46 12.34375 45.828125 12.109375 45.59375 C 11.878906 45.359375 11.691406 44.941406 11.570313 44.300781 C 10.738281 39.941406 10.007813 36.351563 9.261719 33.65625 C 8.847656 32.167969 8.480469 30.953125 8.007813 29.996094 Z"/></svg>
                      {% when 'chat' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 4.0625 C 12.414063 4.0625 2.0625 12.925781 2.0625 24 C 2.0625 30.425781 5.625 36.09375 11 39.71875 C 10.992188 39.933594 11 40.265625 10.71875 41.3125 C 10.371094 42.605469 9.683594 44.4375 8.25 46.46875 L 7.21875 47.90625 L 9 47.9375 C 15.175781 47.964844 18.753906 43.90625 19.3125 43.25 C 21.136719 43.65625 23.035156 43.9375 25 43.9375 C 37.582031 43.9375 47.9375 35.074219 47.9375 24 C 47.9375 12.925781 37.582031 4.0625 25 4.0625 Z M 25 5.9375 C 36.714844 5.9375 46.0625 14.089844 46.0625 24 C 46.0625 33.910156 36.714844 42.0625 25 42.0625 C 22.996094 42.0625 21.050781 41.820313 19.21875 41.375 L 18.65625 41.25 L 18.28125 41.71875 C 18.28125 41.71875 15.390625 44.976563 10.78125 45.75 C 11.613281 44.257813 12.246094 42.871094 12.53125 41.8125 C 12.929688 40.332031 12.9375 39.3125 12.9375 39.3125 L 12.9375 38.8125 L 12.5 38.53125 C 7.273438 35.21875 3.9375 29.941406 3.9375 24 C 3.9375 14.089844 13.28125 5.9375 25 5.9375 Z"/></svg>
                      {% when 'chat-dots' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 4.070313 C 12.367188 4.070313 2.070313 12.921875 2.070313 24 C 2.070313 30.429688 5.59375 36.027344 11.003906 39.6875 C 10.996094 39.902344 11.011719 40.25 10.730469 41.296875 C 10.378906 42.589844 9.671875 44.414063 8.238281 46.46875 L 7.21875 47.929688 L 9 47.929688 C 15.171875 47.929688 18.742188 43.90625 19.296875 43.261719 C 21.132813 43.691406 23.023438 43.929688 25 43.929688 C 37.632813 43.929688 47.929688 35.078125 47.929688 24 C 47.929688 12.921875 37.632813 4.070313 25 4.070313 Z M 25 5.929688 C 36.769531 5.929688 46.070313 14.078125 46.070313 24 C 46.070313 33.921875 36.769531 42.070313 25 42.070313 C 22.960938 42.070313 21.039063 41.875 19.234375 41.402344 L 18.65625 41.25 L 18.277344 41.714844 C 18.277344 41.714844 15.390625 44.972656 10.78125 45.757813 C 11.617188 44.25 12.234375 42.84375 12.519531 41.78125 C 12.921875 40.300781 12.929688 39.300781 12.929688 39.300781 L 12.929688 38.789063 L 12.5 38.515625 C 7.21875 35.15625 3.929688 29.957031 3.929688 24 C 3.929688 14.078125 13.230469 5.929688 25 5.929688 Z M 15 22 C 13.894531 22 13 22.894531 13 24 C 13 25.105469 13.894531 26 15 26 C 16.105469 26 17 25.105469 17 24 C 17 22.894531 16.105469 22 15 22 Z M 25 22 C 23.894531 22 23 22.894531 23 24 C 23 25.105469 23.894531 26 25 26 C 26.105469 26 27 25.105469 27 24 C 27 22.894531 26.105469 22 25 22 Z M 35 22 C 33.894531 22 33 22.894531 33 24 C 33 25.105469 33.894531 26 35 26 C 36.105469 26 37 25.105469 37 24 C 37 22.894531 36.105469 22 35 22 Z"/></svg>
                      {% when 'people' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 26.8125 5 C 22.355469 5 19.097656 6.761719 17.375 9.375 C 17.019531 9.910156 16.753906 10.472656 16.53125 11.0625 L 16.28125 10.5625 C 16.117188 10.226563 15.78125 10.007813 15.40625 10 C 11.945313 10 9.359375 11.40625 8 13.5 C 6.757813 15.410156 6.683594 17.8125 7.46875 20.0625 C 7.40625 20.164063 7.273438 20.21875 7.21875 20.34375 C 7.03125 20.761719 6.929688 21.289063 7 21.90625 C 7 21.917969 7 21.925781 7 21.9375 C 7.113281 22.847656 7.34375 23.5 7.71875 24 C 7.878906 24.210938 8.132813 24.210938 8.34375 24.34375 C 8.492188 25.085938 8.703125 25.824219 9 26.4375 C 9.175781 26.804688 9.363281 27.121094 9.5625 27.40625 C 9.628906 27.503906 9.742188 27.570313 9.8125 27.65625 C 9.8125 28.421875 9.785156 29.109375 9.71875 30 C 9.539063 30.457031 9.101563 30.859375 8.28125 31.28125 C 7.441406 31.710938 6.316406 32.109375 5.15625 32.625 C 3.996094 33.140625 2.757813 33.765625 1.78125 34.78125 C 0.804688 35.796875 0.113281 37.21875 0 39.03125 C -0.015625 39.308594 0.0820313 39.578125 0.269531 39.777344 C 0.460938 39.980469 0.722656 40.09375 1 40.09375 L 7.21875 40.09375 C 7.066406 40.664063 6.945313 41.277344 6.90625 41.9375 C 6.890625 42.214844 6.988281 42.484375 7.175781 42.683594 C 7.367188 42.886719 7.628906 43 7.90625 43 L 42.09375 43 C 42.371094 43 42.632813 42.886719 42.824219 42.683594 C 43.011719 42.484375 43.109375 42.214844 43.09375 41.9375 C 43.050781 41.238281 42.917969 40.601563 42.75 40 L 49 40 C 49.277344 40 49.539063 39.886719 49.730469 39.683594 C 49.917969 39.484375 50.015625 39.214844 50 38.9375 C 49.886719 37.128906 49.1875 35.703125 48.21875 34.6875 C 47.25 33.671875 46.054688 33.046875 44.90625 32.53125 C 43.757813 32.015625 42.644531 31.617188 41.8125 31.1875 C 41.019531 30.777344 40.597656 30.355469 40.40625 29.90625 C 40.339844 29.097656 40.3125 28.378906 40.3125 27.5625 C 40.378906 27.476563 40.496094 27.40625 40.5625 27.3125 C 40.761719 27.03125 40.921875 26.707031 41.09375 26.34375 C 41.371094 25.753906 41.574219 25.050781 41.6875 24.34375 C 41.890625 24.214844 42.128906 24.203125 42.28125 24 C 42.65625 23.5 42.886719 22.847656 43 21.9375 C 43.070313 21.386719 43.007813 20.871094 42.84375 20.4375 C 42.796875 20.316406 42.625 20.242188 42.5625 20.125 C 42.78125 19.613281 43.03125 19.128906 43.09375 18.5 C 43.183594 17.566406 43.109375 16.519531 42.84375 15.5 C 42.578125 14.480469 42.117188 13.46875 41.3125 12.6875 C 40.664063 12.054688 39.75 11.683594 38.71875 11.53125 L 38.3125 10.59375 C 38.152344 10.234375 37.796875 10.003906 37.40625 10 C 36.023438 10 34.550781 10.136719 33.1875 10.5625 C 32.832031 9.773438 32.363281 9.023438 31.71875 8.40625 C 30.863281 7.582031 29.660156 7.089844 28.28125 6.9375 L 27.71875 5.59375 C 27.558594 5.234375 27.203125 5.003906 26.8125 5 Z M 26.15625 7.0625 L 26.6875 8.21875 C 26.847656 8.578125 27.203125 8.808594 27.59375 8.8125 C 28.835938 8.8125 29.679688 9.207031 30.34375 9.84375 C 31.007813 10.480469 31.480469 11.386719 31.75 12.40625 C 32.019531 13.425781 32.09375 14.539063 32 15.46875 C 31.90625 16.398438 31.589844 17.148438 31.40625 17.375 C 31.214844 17.617188 31.148438 17.933594 31.21875 18.230469 C 31.292969 18.53125 31.5 18.78125 31.78125 18.90625 C 31.765625 18.898438 31.78125 18.898438 31.8125 18.96875 C 31.867188 19.097656 31.925781 19.386719 31.90625 19.75 C 31.902344 19.78125 31.910156 19.78125 31.90625 19.8125 C 31.769531 20.640625 31.535156 21.132813 31.375 21.34375 C 31.292969 21.453125 31.234375 21.488281 31.21875 21.5 C 30.746094 21.539063 30.371094 21.90625 30.3125 22.375 C 30.230469 23.121094 29.941406 24.09375 29.59375 24.84375 C 29.421875 25.21875 29.214844 25.535156 29.0625 25.75 C 28.945313 25.910156 28.828125 26 28.84375 26 C 28.507813 26.175781 28.304688 26.527344 28.3125 26.90625 C 28.3125 28.207031 28.304688 29.25 28.40625 30.78125 C 28.414063 30.878906 28.433594 30.972656 28.46875 31.0625 C 29.003906 32.46875 30.210938 33.316406 31.53125 33.96875 C 32.851563 34.621094 34.34375 35.109375 35.75 35.71875 C 37.15625 36.328125 38.464844 37.0625 39.40625 38.03125 C 40.136719 38.78125 40.582031 39.773438 40.84375 41 L 9.15625 41 C 9.417969 39.773438 9.863281 38.78125 10.59375 38.03125 C 11.535156 37.0625 12.84375 36.328125 14.25 35.71875 C 15.65625 35.109375 17.148438 34.621094 18.46875 33.96875 C 19.789063 33.316406 20.996094 32.46875 21.53125 31.0625 C 21.566406 30.972656 21.585938 30.878906 21.59375 30.78125 C 21.695313 29.34375 21.6875 28.207031 21.6875 26.90625 C 21.695313 26.527344 21.492188 26.175781 21.15625 26 C 21.15625 26 21.148438 25.988281 21.125 25.96875 C 21.117188 25.964844 21.105469 25.972656 21.09375 25.96875 C 21.046875 25.929688 20.980469 25.847656 20.90625 25.75 C 20.746094 25.539063 20.527344 25.210938 20.34375 24.84375 C 19.976563 24.113281 19.675781 23.1875 19.59375 22.40625 C 19.546875 21.96875 19.214844 21.613281 18.78125 21.53125 C 18.683594 21.511719 18.171875 21.535156 18 19.8125 C 17.929688 19.042969 18.292969 18.816406 18.25 18.84375 C 18.667969 18.578125 18.828125 18.046875 18.625 17.59375 C 17.984375 16.121094 17.78125 14.601563 18 13.1875 C 18.226563 12.933594 18.3125 12.578125 18.21875 12.25 C 18.40625 11.628906 18.671875 11.046875 19.03125 10.5 C 20.277344 8.613281 22.621094 7.230469 26.15625 7.0625 Z M 36.78125 12.09375 L 37.09375 12.78125 C 37.242188 13.152344 37.601563 13.398438 38 13.40625 C 38.890625 13.40625 39.445313 13.675781 39.90625 14.125 C 40.367188 14.574219 40.707031 15.242188 40.90625 16 C 41.105469 16.757813 41.160156 17.582031 41.09375 18.28125 C 41.027344 18.980469 40.789063 19.550781 40.6875 19.6875 C 40.511719 19.914063 40.445313 20.203125 40.5 20.484375 C 40.550781 20.765625 40.722656 21.011719 40.96875 21.15625 C 41.003906 21.246094 41.03125 21.441406 41 21.6875 C 40.914063 22.375 40.761719 22.710938 40.6875 22.8125 C 40.21875 22.871094 39.851563 23.246094 39.8125 23.71875 C 39.777344 24.234375 39.539063 24.953125 39.28125 25.5 C 39.152344 25.773438 39.019531 25.992188 38.90625 26.15625 C 38.820313 26.277344 38.742188 26.371094 38.75 26.375 C 38.480469 26.558594 38.316406 26.863281 38.3125 27.1875 C 38.3125 28.289063 38.300781 29.136719 38.40625 30.28125 C 38.417969 30.367188 38.4375 30.449219 38.46875 30.53125 C 38.894531 31.707031 39.855469 32.441406 40.875 32.96875 C 41.894531 33.496094 43.042969 33.871094 44.09375 34.34375 C 45.144531 34.816406 46.097656 35.375 46.78125 36.09375 C 47.257813 36.59375 47.574219 37.226563 47.78125 38 L 42 38 C 41.96875 38 41.9375 38 41.90625 38 C 41.59375 37.488281 41.242188 37.035156 40.84375 36.625 C 39.609375 35.355469 38.070313 34.558594 36.5625 33.90625 C 35.054688 33.253906 33.570313 32.75 32.4375 32.1875 C 31.34375 31.644531 30.691406 31.066406 30.40625 30.40625 C 30.332031 29.207031 30.3125 28.308594 30.3125 27.25 C 30.417969 27.128906 30.589844 27.039063 30.6875 26.90625 C 30.941406 26.550781 31.191406 26.152344 31.40625 25.6875 C 31.78125 24.875 32.003906 23.929688 32.15625 23 C 32.429688 22.835938 32.757813 22.84375 32.96875 22.5625 C 33.40625 21.980469 33.707031 21.175781 33.875 20.0625 C 33.886719 20.042969 33.898438 20.019531 33.90625 20 C 33.96875 19.3125 33.871094 18.699219 33.65625 18.1875 C 33.601563 18.058594 33.417969 18.027344 33.34375 17.90625 C 33.667969 17.25 33.882813 16.511719 33.96875 15.65625 C 34.070313 14.65625 34.035156 13.566406 33.8125 12.46875 C 34.710938 12.222656 35.765625 12.132813 36.78125 12.09375 Z M 14.84375 12.125 L 15.21875 12.84375 C 15.359375 13.144531 15.640625 13.355469 15.96875 13.40625 C 15.835938 14.859375 16.0625 16.367188 16.59375 17.8125 C 16.214844 18.332031 15.910156 19.027344 16 20 C 16.175781 21.746094 16.957031 22.574219 17.71875 23.03125 C 17.878906 23.988281 18.167969 24.957031 18.5625 25.75 C 18.792969 26.210938 19.046875 26.621094 19.3125 26.96875 C 19.410156 27.097656 19.582031 27.167969 19.6875 27.28125 C 19.6875 28.339844 19.667969 29.289063 19.59375 30.40625 C 19.308594 31.066406 18.65625 31.644531 17.5625 32.1875 C 16.429688 32.75 14.945313 33.253906 13.4375 33.90625 C 11.929688 34.558594 10.390625 35.355469 9.15625 36.625 C 8.734375 37.058594 8.355469 37.542969 8.03125 38.09375 C 8.019531 38.09375 8.011719 38.09375 8 38.09375 L 2.25 38.09375 C 2.460938 37.320313 2.769531 36.6875 3.25 36.1875 C 3.941406 35.46875 4.90625 34.910156 5.96875 34.4375 C 7.03125 33.964844 8.160156 33.589844 9.1875 33.0625 C 10.214844 32.535156 11.195313 31.835938 11.625 30.65625 C 11.660156 30.566406 11.679688 30.472656 11.6875 30.375 C 11.789063 29.136719 11.8125 28.3125 11.8125 27.3125 C 11.820313 26.957031 11.644531 26.625 11.34375 26.4375 C 11.3125 26.402344 11.246094 26.332031 11.1875 26.25 C 11.070313 26.082031 10.917969 25.847656 10.78125 25.5625 C 10.507813 24.996094 10.269531 24.261719 10.1875 23.65625 C 10.121094 23.210938 9.761719 22.863281 9.3125 22.8125 C 9.238281 22.710938 9.085938 22.375 9 21.6875 C 8.96875 21.402344 9.023438 21.238281 9.0625 21.15625 C 9.46875 20.878906 9.613281 20.351563 9.40625 19.90625 C 8.578125 18.03125 8.722656 16.078125 9.6875 14.59375 C 10.574219 13.226563 12.296875 12.28125 14.84375 12.125 Z"/></svg>
                      {% when 'email' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 0 7 L 0 43 L 50 43 L 50 7 Z M 2 9 L 48 9 L 48 11.53125 L 25 29.71875 L 2 11.53125 Z M 2 14.09375 L 24.375 31.78125 C 24.742188 32.074219 25.257813 32.074219 25.625 31.78125 L 48 14.09375 L 48 41 L 2 41 Z"/></svg>
                      {% when 'globe' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 1.9199219 C 12.265113 1.9199219 1.9199219 12.265113 1.9199219 25 C 1.9199219 37.734887 12.265113 48.080078 25 48.080078 C 37.734887 48.080078 48.080078 37.734887 48.080078 25 C 48.080078 12.265113 37.734887 1.9199219 25 1.9199219 z M 24 4.0371094 L 24 13.970703 C 21.43251 13.91011 18.991445 13.623022 16.740234 13.152344 C 17.136627 11.9515 17.587103 10.835559 18.087891 9.8339844 C 19.740825 6.5281156 21.837286 4.5160498 24 4.0371094 z M 26 4.0371094 C 28.162714 4.5160498 30.259175 6.5281156 31.912109 9.8339844 C 32.414496 10.838757 32.866379 11.958806 33.263672 13.164062 C 31.015269 13.630037 28.5709 13.911377 26 13.970703 L 26 4.0371094 z M 19.171875 4.7402344 C 18.086481 5.8772845 17.118637 7.2998344 16.298828 8.9394531 C 15.727502 10.082106 15.226366 11.34271 14.789062 12.6875 C 12.825936 12.158279 11.050587 11.492361 9.5175781 10.71875 C 12.097501 7.9227151 15.411073 5.8180109 19.171875 4.7402344 z M 30.828125 4.7402344 C 34.585289 5.8169681 37.895737 7.9189636 40.474609 10.710938 C 38.96425 11.498668 37.190868 12.172098 35.216797 12.703125 C 34.778353 11.352409 34.274712 10.086534 33.701172 8.9394531 C 32.881363 7.2998344 31.913519 5.8772845 30.828125 4.7402344 z M 41.783203 12.273438 C 44.280319 15.563254 45.849591 19.596544 46.054688 24 L 37.013672 24 C 36.940686 20.642697 36.511581 17.472843 35.777344 14.632812 C 38.021162 14.025788 40.043564 13.230357 41.783203 12.273438 z M 8.2050781 12.289062 C 9.9635362 13.222752 11.989301 14.00879 14.226562 14.615234 C 13.489633 17.459671 13.059462 20.635904 12.986328 24 L 3.9453125 24 C 4.1500856 19.603485 5.7148727 15.576078 8.2050781 12.289062 z M 16.175781 15.085938 C 18.608124 15.603479 21.237145 15.911692 24 15.972656 L 24 24 L 14.986328 24 C 15.060725 20.787369 15.480743 17.762271 16.175781 15.085938 z M 33.828125 15.099609 C 34.521088 17.7726 34.939401 20.792796 35.013672 24 L 26 24 L 26 15.972656 C 28.764457 15.913393 31.396477 15.612271 33.828125 15.099609 z M 3.9453125 26 L 12.986328 26 C 13.059314 29.357303 13.488419 32.527156 14.222656 35.367188 C 11.978838 35.974212 9.9564363 36.769643 8.2167969 37.726562 C 5.7196806 34.436746 4.1504088 30.403456 3.9453125 26 z M 14.986328 26 L 24 26 L 24 34.027344 C 21.235543 34.086607 18.603523 34.387729 16.171875 34.900391 C 15.478912 32.2274 15.060599 29.207204 14.986328 26 z M 26 26 L 35.013672 26 C 34.939275 29.212631 34.519257 32.237729 33.824219 34.914062 C 31.391876 34.396521 28.762855 34.088308 26 34.027344 L 26 26 z M 37.013672 26 L 46.054688 26 C 45.849914 30.396515 44.285127 34.423922 41.794922 37.710938 C 40.036464 36.777248 38.010699 35.99121 35.773438 35.384766 C 36.510367 32.540329 36.940538 29.364096 37.013672 26 z M 24 36.029297 L 24 45.962891 C 21.837286 45.48395 19.740825 43.471884 18.087891 40.166016 C 17.585504 39.161243 17.133621 38.041194 16.736328 36.835938 C 18.984731 36.369963 21.4291 36.088623 24 36.029297 z M 26 36.029297 C 28.56749 36.08989 31.008555 36.376978 33.259766 36.847656 C 32.863373 38.0485 32.412897 39.16444 31.912109 40.166016 C 30.259175 43.471884 28.162714 45.48395 26 45.962891 L 26 36.029297 z M 14.783203 37.296875 C 15.221647 38.647591 15.725288 39.913466 16.298828 41.060547 C 17.118637 42.700166 18.086481 44.122716 19.171875 45.259766 C 15.414711 44.183032 12.104263 42.081036 9.5253906 39.289062 C 11.03575 38.501332 12.809132 37.827902 14.783203 37.296875 z M 35.210938 37.3125 C 37.174064 37.841721 38.949413 38.507639 40.482422 39.28125 C 37.902499 42.077285 34.588927 44.181989 30.828125 45.259766 C 31.913519 44.122716 32.881363 42.700166 33.701172 41.060547 C 34.272498 39.917894 34.773634 38.65729 35.210938 37.3125 z"/></svg>
                      {% when 'newsletter' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 3 C 24.065529 3 22.342634 3.1922485 20.857422 4.4609375 C 20.398681 4.8083214 19.373867 5.711436 17.886719 7 L 11 7 A 1.0001 1.0001 0 0 0 10 8 L 10 13.962891 C 6.471313 17.084086 3.3339844 19.878906 3.3339844 19.878906 A 1.0001 1.0001 0 0 0 3 20.625 L 3 46 A 1.0001 1.0001 0 0 0 4 47 L 46 47 A 1.0001 1.0001 0 0 0 47 46 L 47 20.625 A 1.0001 1.0001 0 0 0 46.666016 19.878906 C 46.666016 19.878906 43.528795 17.083407 40 13.962891 L 40 8 A 1.0001 1.0001 0 0 0 39 7 L 32.052734 7 C 30.621699 5.7124523 29.597448 4.8047971 29.140625 4.4589844 C 27.65593 3.1920488 25.933993 3 25 3 z M 25 5 C 25.639946 5 26.886525 5.1588743 27.849609 5.984375 A 1.0001 1.0001 0 0 0 27.900391 6.0253906 C 27.959185 6.0694411 28.697665 6.7277272 29.013672 7 L 20.960938 7 C 21.279381 6.7285566 22.041101 6.0692269 22.099609 6.0253906 A 1.0001 1.0001 0 0 0 22.150391 5.984375 C 23.113477 5.1588747 24.360054 5 25 5 z M 12 9 L 17.988281 9 A 1.0001 1.0001 0 0 0 18.462891 9 L 31.513672 9 A 1.0001 1.0001 0 0 0 31.859375 9 L 38 9 L 38 14.417969 L 38 26.542969 L 32.361328 30.980469 C 31.298143 30.145647 29.356777 28.62871 29.125 28.443359 C 27.584365 27.210381 25.936054 27 25 27 C 23.990342 27 22.424011 27.305375 20.908203 28.417969 C 20.632913 28.619667 18.627455 30.201185 17.632812 30.976562 L 12 26.542969 L 12 14.546875 A 1.0001 1.0001 0 0 0 12 14.246094 L 12 9 z M 16 14 A 1.0001 1.0001 0 1 0 16 16 L 34 16 A 1.0001 1.0001 0 1 0 34 14 L 16 14 z M 10 16.636719 L 10 24.966797 L 5.0253906 21.050781 C 5.2639072 20.838199 7.6995528 18.67611 10 16.636719 z M 40 16.636719 C 42.300878 18.676338 44.736918 20.840822 44.974609 21.052734 L 40 24.96875 L 40 16.636719 z M 16 19 A 1.0001 1.0001 0 1 0 16 21 L 27 21 A 1.0001 1.0001 0 1 0 27 19 L 16 19 z M 5 23.578125 L 16.009766 32.244141 C 14.380662 33.519885 13.64873 34.080591 11.683594 35.625 C 8.2747801 38.304 6.1117199 40.008897 5 40.884766 L 5 23.578125 z M 45 23.578125 L 45 40.888672 C 43.543056 39.746848 38.508414 35.801673 33.982422 32.251953 L 45 23.578125 z M 25 29 C 25.639946 29 26.791635 29.138837 27.875 30.005859 C 29.231039 31.090278 44.471585 43.016199 45 43.429688 L 45 45 L 5 45 L 5 43.427734 C 5.2740923 43.211869 8.8819407 40.370731 12.919922 37.197266 C 17.108371 33.905547 21.554155 30.425166 22.091797 30.03125 C 23.199989 29.217844 24.433658 29 25 29 z"/></svg>
                      {% when 'love' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 11.90625 6 C 5.339844 6 0 11.339844 0 17.90625 C 0 31.394531 15.214844 38.480469 20.375 42.78125 L 21 43.3125 L 21.625 42.78125 C 22.628906 41.945313 24.074219 40.933594 25.65625 39.84375 C 27.027344 41.613281 28.308594 43.132813 29.03125 44.28125 L 29.46875 45 L 30.25 44.6875 C 34.554688 42.96875 46.523438 41.800781 49.5625 32.46875 C 51.054688 27.878906 48.527344 22.929688 43.9375 21.4375 C 43.191406 21.195313 42.433594 21.09375 41.6875 21.0625 C 41.878906 20.050781 42 18.996094 42 17.90625 C 42 11.339844 36.660156 6 30.09375 6 C 26.410156 6 23.179688 7.808594 21 10.4375 C 18.820313 7.808594 15.589844 6 11.90625 6 Z M 11.90625 8 C 15.339844 8 18.375 9.878906 20.15625 12.5625 L 21 13.8125 L 21.84375 12.5625 C 23.625 9.878906 26.660156 8 30.09375 8 C 35.578125 8 40 12.421875 40 17.90625 C 40 19.050781 39.855469 20.140625 39.625 21.1875 C 38.65625 21.386719 37.730469 21.757813 36.875 22.25 C 35.902344 20.054688 34.113281 18.238281 31.65625 17.4375 C 30.507813 17.0625 29.320313 16.933594 28.1875 17.03125 C 24.785156 17.324219 21.746094 19.621094 20.625 23.0625 C 18.734375 28.867188 21.519531 34.148438 24.46875 38.25 C 23.171875 39.152344 22.015625 39.980469 21 40.78125 C 14.984375 36.027344 2 29.535156 2 17.90625 C 2 12.421875 6.421875 8 11.90625 8 Z M 29.03125 19 C 29.691406 19.007813 30.363281 19.125 31.03125 19.34375 C 33.273438 20.074219 34.878906 21.859375 35.46875 23.96875 L 35.84375 25.40625 L 37.03125 24.46875 C 38.746094 23.109375 41.101563 22.613281 43.34375 23.34375 C 46.90625 24.503906 48.8125 28.28125 47.65625 31.84375 C 45.183594 39.433594 35.507813 40.671875 30.25 42.5625 C 27.109375 37.9375 20.03125 31.238281 22.5 23.65625 C 23.367188 20.984375 25.75 19.253906 28.375 19.03125 C 28.59375 19.011719 28.8125 18.996094 29.03125 19 Z"/></svg>
                      {% when 'phone' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 11.839844 2.988281 C 11.070313 2.925781 10.214844 3.148438 9.425781 3.703125 C 8.730469 4.1875 7.230469 5.378906 5.828125 6.726563 C 5.128906 7.398438 4.460938 8.097656 3.945313 8.785156 C 3.425781 9.472656 2.972656 10.101563 3 11.015625 C 3.027344 11.835938 3.109375 14.261719 4.855469 17.980469 C 6.601563 21.695313 9.988281 26.792969 16.59375 33.402344 C 23.203125 40.011719 28.300781 43.398438 32.015625 45.144531 C 35.730469 46.890625 38.160156 46.972656 38.980469 47 C 39.890625 47.027344 40.519531 46.574219 41.207031 46.054688 C 41.894531 45.535156 42.59375 44.871094 43.265625 44.171875 C 44.609375 42.769531 45.800781 41.269531 46.285156 40.574219 C 47.390625 39 47.207031 37.140625 45.976563 36.277344 C 45.203125 35.734375 38.089844 31 37.019531 30.34375 C 35.933594 29.679688 34.683594 29.980469 33.566406 30.570313 C 32.6875 31.035156 30.308594 32.398438 29.628906 32.789063 C 29.117188 32.464844 27.175781 31.171875 23 26.996094 C 18.820313 22.820313 17.53125 20.878906 17.207031 20.367188 C 17.597656 19.6875 18.957031 17.320313 19.425781 16.425781 C 20.011719 15.3125 20.339844 14.050781 19.640625 12.957031 C 19.347656 12.492188 18.015625 10.464844 16.671875 8.429688 C 15.324219 6.394531 14.046875 4.464844 13.714844 4.003906 L 13.714844 4 C 13.28125 3.402344 12.605469 3.050781 11.839844 2.988281 Z M 11.65625 5.03125 C 11.929688 5.066406 12.09375 5.175781 12.09375 5.175781 C 12.253906 5.398438 13.65625 7.5 15 9.53125 C 16.34375 11.566406 17.714844 13.652344 17.953125 14.03125 C 17.992188 14.089844 18.046875 14.753906 17.65625 15.492188 L 17.65625 15.496094 C 17.214844 16.335938 15.15625 19.933594 15.15625 19.933594 L 14.871094 20.4375 L 15.164063 20.9375 C 15.164063 20.9375 16.699219 23.527344 21.582031 28.410156 C 26.46875 33.292969 29.058594 34.832031 29.058594 34.832031 L 29.558594 35.125 L 30.0625 34.839844 C 30.0625 34.839844 33.652344 32.785156 34.5 32.339844 C 35.238281 31.953125 35.902344 32.003906 35.980469 32.050781 C 36.671875 32.476563 44.355469 37.582031 44.828125 37.914063 C 44.84375 37.925781 45.261719 38.558594 44.652344 39.425781 L 44.648438 39.425781 C 44.28125 39.953125 43.078125 41.480469 41.824219 42.785156 C 41.195313 43.4375 40.550781 44.046875 40.003906 44.457031 C 39.457031 44.867188 38.96875 44.996094 39.046875 45 C 38.195313 44.972656 36.316406 44.953125 32.867188 43.332031 C 29.417969 41.714844 24.496094 38.476563 18.007813 31.984375 C 11.523438 25.5 8.285156 20.578125 6.664063 17.125 C 5.046875 13.675781 5.027344 11.796875 5 10.949219 C 5.003906 11.027344 5.132813 10.535156 5.542969 9.988281 C 5.953125 9.441406 6.558594 8.792969 7.210938 8.164063 C 8.519531 6.910156 10.042969 5.707031 10.570313 5.339844 L 10.570313 5.34375 C 11.003906 5.039063 11.382813 5 11.65625 5.03125 Z"/></svg>
                      {% when 'share' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 40 0 C 34.53125 0 30.066406 4.421875 30 9.875 L 15.90625 16.9375 C 14.25 15.71875 12.207031 15 10 15 C 4.488281 15 0 19.488281 0 25 C 0 30.511719 4.488281 35 10 35 C 12.207031 35 14.25 34.28125 15.90625 33.0625 L 30 40.125 C 30.066406 45.578125 34.53125 50 40 50 C 45.511719 50 50 45.511719 50 40 C 50 34.488281 45.511719 30 40 30 C 37.875 30 35.902344 30.675781 34.28125 31.8125 L 20.625 25 L 34.28125 18.1875 C 35.902344 19.324219 37.875 20 40 20 C 45.511719 20 50 15.511719 50 10 C 50 4.488281 45.511719 0 40 0 Z M 40 2 C 44.429688 2 48 5.570313 48 10 C 48 14.429688 44.429688 18 40 18 C 38.363281 18 36.859375 17.492188 35.59375 16.65625 C 35.46875 16.238281 35.089844 15.949219 34.65625 15.9375 C 34.652344 15.933594 34.628906 15.941406 34.625 15.9375 C 33.230469 14.675781 32.292969 12.910156 32.0625 10.9375 C 32.273438 10.585938 32.25 10.140625 32 9.8125 C 32.101563 5.472656 35.632813 2 40 2 Z M 30.21875 12 C 30.589844 13.808594 31.449219 15.4375 32.65625 16.75 L 19.8125 23.1875 C 19.472656 21.359375 18.65625 19.710938 17.46875 18.375 Z M 10 17 C 11.851563 17 13.554688 17.609375 14.90625 18.65625 C 14.917969 18.664063 14.925781 18.679688 14.9375 18.6875 C 14.945313 18.707031 14.957031 18.730469 14.96875 18.75 C 15.054688 18.855469 15.160156 18.9375 15.28125 19 C 15.285156 19.003906 15.308594 18.996094 15.3125 19 C 16.808594 20.328125 17.796875 22.222656 17.96875 24.34375 C 17.855469 24.617188 17.867188 24.925781 18 25.1875 C 17.980469 25.269531 17.96875 25.351563 17.96875 25.4375 C 17.847656 27.65625 16.839844 29.628906 15.28125 31 C 15.1875 31.058594 15.101563 31.132813 15.03125 31.21875 C 13.65625 32.332031 11.914063 33 10 33 C 5.570313 33 2 29.429688 2 25 C 2 20.570313 5.570313 17 10 17 Z M 19.8125 26.8125 L 32.65625 33.25 C 31.449219 34.5625 30.589844 36.191406 30.21875 38 L 17.46875 31.625 C 18.65625 30.289063 19.472656 28.640625 19.8125 26.8125 Z M 40 32 C 44.429688 32 48 35.570313 48 40 C 48 44.429688 44.429688 48 40 48 C 35.570313 48 32 44.429688 32 40 C 32 37.59375 33.046875 35.433594 34.71875 33.96875 C 34.742188 33.949219 34.761719 33.929688 34.78125 33.90625 C 34.785156 33.902344 34.808594 33.910156 34.8125 33.90625 C 34.972656 33.839844 35.113281 33.730469 35.21875 33.59375 C 36.554688 32.597656 38.199219 32 40 32 Z"/></svg>
                      {% when 'box' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 1 3 L 1 15 L 3 15 L 3 48 L 47 48 L 47 15 L 49 15 L 49 3 Z M 3 5 L 47 5 L 47 13 L 3 13 Z M 5 15 L 45 15 L 45 46 L 5 46 Z M 17.5 19 C 15.578125 19 14 20.578125 14 22.5 C 14 24.421875 15.578125 26 17.5 26 L 32.5 26 C 34.421875 26 36 24.421875 36 22.5 C 36 20.578125 34.421875 19 32.5 19 Z M 17.5 21 L 32.5 21 C 33.339844 21 34 21.660156 34 22.5 C 34 23.339844 33.339844 24 32.5 24 L 17.5 24 C 16.660156 24 16 23.339844 16 22.5 C 16 21.660156 16.660156 21 17.5 21 Z"/></svg>
                      {% when 'worldwide' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 1 C 24.004424 1 23.03298 1.1027617 22.083984 1.2675781 A 1.0001 1.0001 0 0 0 21.984375 1.2636719 A 1.0001 1.0001 0 0 0 21.623047 1.3378906 C 14.120707 2.8551932 8.417162 9.3288867 8.0410156 17.181641 L 5.6601562 18.060547 L 5.6542969 18.0625 A 1.0001 1.0001 0 0 0 5.1523438 18.470703 L 0.15234375 26.470703 A 1.0001 1.0001 0 0 0 0.61132812 27.921875 L 5 29.769531 L 5 41 A 1.0001 1.0001 0 0 0 5.6542969 41.9375 L 24.654297 48.9375 A 1.0001 1.0001 0 0 0 25.345703 48.9375 L 44.345703 41.9375 A 1.0001 1.0001 0 0 0 45 41 L 45 29.769531 L 49.388672 27.921875 A 1.0001 1.0001 0 0 0 49.847656 26.470703 L 44.847656 18.470703 A 1.0001 1.0001 0 0 0 44.345703 18.0625 A 1.0001 1.0001 0 0 0 44.306641 18.048828 L 41.958984 17.183594 C 41.68653 11.482016 38.607033 6.5181936 34.0625 3.640625 A 1.0001 1.0001 0 0 0 33.625 3.375 C 31.092974 1.876422 28.150842 1 25 1 z M 25 3 C 27.359708 3 29.582036 3.5584421 31.566406 4.5253906 L 28.355469 7.2363281 A 1.0001 1.0001 0 0 0 28 8 L 28 12 A 1.0001 1.0001 0 0 0 29 13 L 32.585938 13 L 35.148438 15.5625 L 33.837891 16 L 29 16 A 1.0001 1.0001 0 0 0 28 17 L 28 21 A 1.0001 1.0001 0 0 0 28.234375 21.642578 L 29.580078 23.248047 L 25 24.935547 L 19 22.724609 L 19 20 A 1.0001 1.0001 0 0 0 18.554688 19.167969 L 16 17.464844 L 16 15.78125 L 19.242188 14.970703 A 1.0001 1.0001 0 0 0 19.800781 14.599609 L 22.800781 10.599609 A 1.0001 1.0001 0 0 0 23 10 L 23 3.1484375 C 23.655836 3.0611746 24.319941 3 25 3 z M 21 3.5546875 L 21 9.6660156 L 18.412109 13.115234 L 14.757812 14.029297 A 1.0001 1.0001 0 0 0 14 15 L 14 18 A 1.0001 1.0001 0 0 0 14.445312 18.832031 L 17 20.535156 L 17 21.988281 L 10.089844 19.441406 C 10.038664 18.959881 10 18.479591 10 18 C 10 11.091336 14.654319 5.3032295 21 3.5546875 z M 33.417969 5.578125 C 37.392441 8.2735025 40 12.826135 40 18 C 40 18.479591 39.961336 18.959881 39.910156 19.441406 L 31.574219 22.513672 L 30 20.636719 L 30 18 L 34 18 A 1.0001 1.0001 0 0 0 34.316406 17.949219 L 37.316406 16.949219 A 1.0001 1.0001 0 0 0 37.707031 15.292969 L 33.707031 11.292969 A 1.0001 1.0001 0 0 0 33 11 L 30 11 L 30 8.4648438 L 33.417969 5.578125 z M 6.4179688 20.21875 L 17.501953 24.302734 A 1.0001 1.0001 0 0 0 17.824219 24.421875 L 23.558594 26.533203 L 19.560547 33.728516 L 2.4707031 26.533203 L 6.4179688 20.21875 z M 43.582031 20.21875 L 47.529297 26.533203 L 30.439453 33.730469 L 26.441406 26.533203 L 31.580078 24.640625 A 1.0002925 1.0002925 0 0 0 31.59375 24.634766 L 43.582031 20.21875 z M 24 29.861328 L 24 46.564453 L 7 40.302734 L 7 30.611328 L 19.611328 35.921875 A 1.0001 1.0001 0 0 0 20.875 35.486328 L 24 29.861328 z M 26 29.861328 L 29.125 35.486328 A 1.0001 1.0001 0 0 0 30.388672 35.921875 L 43 30.611328 L 43 40.302734 L 26 46.564453 L 26 29.861328 z"/></svg>
                      {% when 'truck' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 0 8 L 0 10 L 28.09375 10 C 28.492188 10 29 10.507813 29 10.90625 L 29 38 L 18.90625 38 C 18.429688 35.164063 15.964844 33 13 33 C 10.035156 33 7.570313 35.164063 7.09375 38 L 3 38 C 2.445313 38 2 37.554688 2 37 L 2 28 L 0 28 L 0 37 C 0 38.644531 1.355469 40 3 40 L 7.09375 40 C 7.570313 42.835938 10.035156 45 13 45 C 15.964844 45 18.429688 42.835938 18.90625 40 L 34.09375 40 C 34.570313 42.835938 37.035156 45 40 45 C 42.964844 45 45.429688 42.835938 45.90625 40 L 47 40 C 47.832031 40 48.5625 39.625 49.09375 39.09375 C 49.625 38.5625 50 37.832031 50 37 L 50 27.40625 C 50 26.28125 49.570313 25.25 49.1875 24.46875 C 48.804688 23.6875 48.40625 23.125 48.40625 23.125 L 48.40625 23.09375 L 44.3125 17.59375 L 44.28125 17.59375 L 44.28125 17.5625 C 43.394531 16.453125 41.972656 15 40 15 L 32 15 C 31.640625 15 31.3125 15.066406 31 15.1875 L 31 10.90625 C 31 9.304688 29.695313 8 28.09375 8 Z M 0 12 L 0 14 L 18 14 L 18 12 Z M 0 16 L 0 18 L 15 18 L 15 16 Z M 32 17 L 36 17 L 36 26 C 36 26.832031 36.375 27.5625 36.90625 28.09375 C 37.4375 28.625 38.167969 29 39 29 L 48 29 L 48 37 C 48 37.167969 47.875 37.4375 47.65625 37.65625 C 47.4375 37.875 47.167969 38 47 38 L 45.90625 38 C 45.429688 35.164063 42.964844 33 40 33 C 37.035156 33 34.570313 35.164063 34.09375 38 L 31 38 L 31 18 C 31 17.832031 31.125 17.5625 31.34375 17.34375 C 31.5625 17.125 31.832031 17 32 17 Z M 38 17 L 40 17 C 40.824219 17 41.972656 17.925781 42.6875 18.8125 L 46.78125 24.28125 L 46.8125 24.3125 C 46.832031 24.339844 47.101563 24.722656 47.40625 25.34375 C 47.660156 25.859375 47.792969 26.472656 47.875 27 L 39 27 C 38.832031 27 38.5625 26.875 38.34375 26.65625 C 38.125 26.4375 38 26.167969 38 26 Z M 0 20 L 0 22 L 12 22 L 12 20 Z M 0 24 L 0 26 L 9 26 L 9 24 Z M 13 35 C 15.222656 35 17 36.777344 17 39 C 17 41.222656 15.222656 43 13 43 C 10.777344 43 9 41.222656 9 39 C 9 36.777344 10.777344 35 13 35 Z M 40 35 C 42.222656 35 44 36.777344 44 39 C 44 41.222656 42.222656 43 40 43 C 37.777344 43 36 41.222656 36 39 C 36 36.777344 37.777344 35 40 35 Z"/></svg>
                      {% when 'return_arrow' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 31 0.59375 L 30.28125 1.28125 L 25.28125 6.28125 C 24.882813 6.679688 24.882813 7.320313 25.28125 7.71875 C 25.679688 8.117188 26.320313 8.117188 26.71875 7.71875 L 30 4.4375 L 30 22 C 29.996094 22.359375 30.183594 22.695313 30.496094 22.878906 C 30.808594 23.058594 31.191406 23.058594 31.503906 22.878906 C 31.816406 22.695313 32.003906 22.359375 32 22 L 32 4.4375 L 35.28125 7.71875 C 35.679688 8.117188 36.320313 8.117188 36.71875 7.71875 C 37.117188 7.320313 37.117188 6.679688 36.71875 6.28125 L 31.71875 1.28125 Z M 1.71875 2 C 1.6875 2.007813 1.65625 2.019531 1.625 2.03125 C 1.613281 2.03125 1.605469 2.027344 1.59375 2.03125 C 1.574219 2.039063 1.550781 2.050781 1.53125 2.0625 C 0.652344 2.273438 0 3.058594 0 4 C 0 5.105469 0.894531 6 2 6 C 3.105469 6 4 5.105469 4 4 L 8.65625 4 C 9.902344 4 10.550781 4.257813 11.03125 4.6875 C 11.503906 5.113281 11.886719 5.832031 12.21875 6.9375 L 20.21875 39.21875 C 20.519531 40.363281 20.820313 41.542969 21.65625 42.5 C 22.003906 42.898438 22.441406 43.222656 22.96875 43.46875 C 22.382813 44.164063 22 45.027344 22 46 C 22 48.199219 23.800781 50 26 50 C 28.199219 50 30 48.199219 30 46 C 30 45.265625 29.785156 44.59375 29.4375 44 L 35.5625 44 C 35.214844 44.59375 35 45.265625 35 46 C 35 48.199219 36.800781 50 39 50 C 41.199219 50 43 48.199219 43 46 C 43 44.972656 42.582031 44.054688 41.9375 43.34375 C 42.050781 43.039063 42.003906 42.695313 41.820313 42.429688 C 41.632813 42.160156 41.328125 42 41 42 L 25.71875 42 C 24.175781 42 23.546875 41.671875 23.125 41.1875 C 22.707031 40.707031 22.453125 39.867188 22.15625 38.75 L 22.15625 38.71875 L 21.46875 36 L 39.8125 36 C 40.230469 36 40.609375 35.738281 40.75 35.34375 L 47.9375 16.34375 C 48.070313 16.007813 48.011719 15.628906 47.785156 15.351563 C 47.558594 15.074219 47.199219 14.9375 46.84375 15 C 46.480469 15.058594 46.183594 15.308594 46.0625 15.65625 L 39.125 34 L 21 34 L 14.15625 6.46875 C 14.15625 6.449219 14.15625 6.425781 14.15625 6.40625 C 13.773438 5.117188 13.277344 4 12.375 3.1875 C 11.472656 2.375 10.203125 2 8.65625 2 L 2 2 C 1.96875 2 1.9375 2 1.90625 2 C 1.875 2 1.84375 2 1.8125 2 C 1.78125 2 1.75 2 1.71875 2 Z M 26 44 C 27.117188 44 28 44.882813 28 46 C 28 47.117188 27.117188 48 26 48 C 24.882813 48 24 47.117188 24 46 C 24 44.882813 24.882813 44 26 44 Z M 39 44 C 40.117188 44 41 44.882813 41 46 C 41 47.117188 40.117188 48 39 48 C 37.882813 48 37 47.117188 37 46 C 37 44.882813 37.882813 44 39 44 Z"/></svg>
                      {% when 'time' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 2 C 12.309534 2 2 12.309534 2 25 C 2 37.690466 12.309534 48 25 48 C 32.287796 48 38.785186 44.597914 43 39.300781 L 43 47 A 1.0001 1.0001 0 1 0 45 47 L 45 36 L 43.490234 36 L 43.390625 36 L 34 36 A 1.0001 1.0001 0 1 0 34 38 L 41.480469 38 C 37.635193 42.869289 31.692686 46 25 46 C 13.390466 46 4 36.609534 4 25 C 4 13.390466 13.390466 4 25 4 C 36.609534 4 46 13.390466 46 25 A 1.0001 1.0001 0 1 0 48 25 C 48 12.309534 37.690466 2 25 2 z M 24.984375 8.9863281 A 1.0001 1.0001 0 0 0 24 10 L 24 22.175781 A 3 3 0 0 0 25 28 A 3 3 0 0 0 27.826172 26 L 36 26 A 1.0001 1.0001 0 1 0 36 24 L 27.828125 24 A 3 3 0 0 0 26 22.171875 L 26 10 A 1.0001 1.0001 0 0 0 24.984375 8.9863281 z"/></svg>
                      {% when 'payment' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 7 9 C 4.2504839 9 2 11.250484 2 14 L 2 36 C 2 38.749516 4.2504839 41 7 41 L 25 41 L 29.636719 41 C 29.389719 40.366 29.166797 39.699 28.966797 39 L 25 39 L 7 39 C 5.3315161 39 4 37.668484 4 36 L 4 21 L 46 21 L 46 24 L 46 27.859375 C 46.562 28.091375 47.21 28.321922 48 28.544922 L 48 24 L 48 14 C 48 11.250484 45.749516 9 43 9 L 7 9 z M 7 11 L 43 11 C 44.668484 11 46 12.331516 46 14 L 46 16 L 4 16 L 4 14 C 4 12.331516 5.3315161 11 7 11 z M 9 24 L 9 26 L 25 26 L 25 24 L 9 24 z M 40 27.900391 C 38.5 27.900391 37.500781 28.4 36.300781 29 C 35.100781 29.6 33.600781 30.300391 30.800781 30.900391 L 30 31.099609 L 30 31.900391 C 30 44.900391 39.2 49.600781 39.5 49.800781 L 40 50 L 40.5 49.800781 C 40.9 49.600781 50 44.500391 50 31.900391 L 50 31.099609 L 49.199219 30.900391 C 46.399219 30.300391 44.899219 29.6 43.699219 29 C 42.499219 28.4 41.5 27.900391 40 27.900391 z M 40 29.900391 C 41.1 29.900391 41.700781 30.200781 42.800781 30.800781 C 44.000781 31.300781 45.5 32.099219 48 32.699219 C 47.7 41.999219 41.7 46.599219 40 47.699219 C 38.2 46.699219 32.3 42.399219 32 32.699219 C 34.6 32.099219 36.099219 31.300781 37.199219 30.800781 C 38.299219 30.200781 38.9 29.900391 40 29.900391 z"/></svg>
                      {% when 'mobile' %}
                      <svg xmlns="http://www.w3.org/2000/svg" width="50px" height="50px" viewBox="0 0 50 50" fill="currentColor"><path d="M 25 3 C 18.363281 3 13 8.363281 13 15 L 13 20 L 9 20 C 7.355469 20 6 21.355469 6 23 L 6 47 C 6 48.644531 7.355469 50 9 50 L 41 50 C 42.644531 50 44 48.644531 44 47 L 44 23 C 44 21.355469 42.644531 20 41 20 L 37 20 L 37 15 C 37 8.363281 31.636719 3 25 3 Z M 25 5 C 30.566406 5 35 9.433594 35 15 L 35 20 L 15 20 L 15 15 C 15 9.433594 19.433594 5 25 5 Z M 9 22 L 41 22 C 41.554688 22 42 22.445313 42 23 L 42 47 C 42 47.554688 41.554688 48 41 48 L 9 48 C 8.445313 48 8 47.554688 8 47 L 8 23 C 8 22.445313 8.445313 22 9 22 Z M 25 30 C 23.300781 30 22 31.300781 22 33 C 22 33.898438 22.398438 34.6875 23 35.1875 L 23 38 C 23 39.101563 23.898438 40 25 40 C 26.101563 40 27 39.101563 27 38 L 27 35.1875 C 27.601563 34.6875 28 33.898438 28 33 C 28 31.300781 26.699219 30 25 30 Z"></path></svg>
                      {% when 'shield' %}
                      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 50 50" width="50px" height="50px" fill="currentColor"><path d="M 25 0 C 22.546875 0 20.476563 1.355469 17.40625 2.9375 C 14.335938 4.519531 10.214844 6.429688 3.78125 7.84375 L 3 8.03125 L 3 8.8125 C 3 24.132813 8.371094 34.371094 13.75 40.71875 C 19.128906 47.066406 24.59375 49.59375 24.59375 49.59375 L 25.03125 49.8125 L 25.46875 49.59375 C 25.46875 49.59375 47 38.46875 47 8.8125 L 47 8.03125 L 46.21875 7.84375 C 39.785156 6.429688 35.664063 4.519531 32.59375 2.9375 C 29.523438 1.355469 27.453125 0 25 0 Z M 25 2 C 26.644531 2 28.550781 3.105469 31.6875 4.71875 C 34.667969 6.25 38.859375 8.128906 44.9375 9.5625 C 44.570313 36.679688 26.253906 46.851563 24.96875 47.53125 C 24.347656 47.230469 20.019531 45.066406 15.25 39.4375 C 10.222656 33.503906 5.246094 23.960938 5.0625 9.5625 C 11.140625 8.128906 15.332031 6.25 18.3125 4.71875 C 21.449219 3.105469 23.355469 2 25 2 Z M 32.6875 14.625 L 32.15625 15.46875 L 24.03125 28.09375 L 18.65625 23.25 L 17.9375 22.59375 L 16.59375 24.0625 L 17.34375 24.75 L 23.53125 30.375 L 24.40625 31.15625 L 25.0625 30.15625 L 33.84375 16.53125 L 34.375 15.6875 Z"/></svg>
                      {% else %}
                      {{ 'product-1' | placeholder_svg_tag }}
                    {% endcase %}
                  {% endif %}
                </div>
              {% endif %}
            {% endfor %}              
          </div>
          <div class="feature-tabs-text-body-{{ section.id }}">
            {% for block in section.blocks %} 
              {% if block.settings.tab_title != blank %}
                <div class="feature-tabs-title-{{ section.id }}">
                  {{ block.settings.tab_title }}
                </div>
              {% endif %}                
            {% endfor %}              
          </div>
          <div class="feature-text-body-{{ section.id }}">
            {% for block in section.blocks %}
              <div class="feature-text-content-{{ section.id }} {% if forloop.first %}active{% endif %}" data-id="{{ block.id }}">
                {% if block.settings.title != blank %}
                  <p class="feature-title-{{ section.id }}">{{ block.settings.title }}</p>
                {% endif %}                
                {% if block.settings.text != blank %}
                  <div class="feature-text-{{ section.id }}">{{ block.settings.text }}</div> 
                {% endif %}                
                {% if block.settings.sub_text != blank %}
                  <div class="feature-subtext-{{ section.id }}">{{ block.settings.sub_text }}</div>
                {% endif %}                
              </div>
            {% endfor %}
          </div>
          {% if button != blank %}
            <div class="feature-button-wrapper-{{ section.id }}">
              <a href="{{ button_url }}" class="feature-button-{{ section.id }}">
                <p class="feature-button-inner-{{ section.id }}">
                {{ button }}
                {% if button_style == "non_outline_arrow" or button_style == "outline_arrow" %}
                    <svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M187.827 98.7858C188.123 98.4749 188.123 98.4749 188.123 98.1761C188.419 97.8652 188.419 97.5663 188.704 97.5663C189 96.9566 189 96.6458 189 96.0361C189 95.4263 189 94.8166 188.704 94.5058C188.704 94.195 188.408 93.8961 188.123 93.5852C188.123 93.2744 187.827 93.2744 187.827 92.9755L103.287 4.21945C102.41 3.29889 101.533 3 100.668 3C99.791 3 98.6295 3.31083 98.0488 4.21945C97.1719 5.14 96.8872 6.06058 96.8872 6.96919C96.8872 7.88974 97.1833 9.10918 98.0488 9.7189L175.587 92.0539H6.79206C4.75371 92.0539 3 93.895 3 96.0351C3 98.1751 4.75365 100.016 6.79206 100.016H175.575L97.7543 182.042C96.2967 183.572 96.2967 186.322 97.7543 187.852C99.2119 189.383 101.831 189.383 103.288 187.852L187.827 98.7858Z" fill="black"></path>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M188.607 97.5657C188.432 97.5657 188.365 97.6788 188.27 97.8382C188.211 97.9378 188.141 98.0554 188.027 98.1748C188.027 98.4734 188.027 98.4734 187.731 98.7839L103.281 187.759C101.825 189.287 99.2085 189.287 97.7524 187.759C96.2963 186.23 96.2963 183.483 97.7524 181.954L175.492 100.013L6.88489 100.013C4.8486 100.013 3.09677 98.1739 3.09677 96.036C3.09677 93.8982 4.84866 92.059 6.88489 92.059L175.504 92.059L98.0465 9.80878C97.182 9.19968 96.8862 7.9815 96.8862 7.0619C96.8862 6.15422 97.1706 5.2346 98.0465 4.315C98.6267 3.40732 99.787 3.0968 100.663 3.0968C101.527 3.0968 102.403 3.39539 103.279 4.315L187.731 92.9796C187.731 93.1274 187.804 93.2021 187.877 93.2774C187.952 93.3543 188.027 93.4319 188.027 93.5887C188.046 93.6098 188.066 93.6308 188.085 93.6518C188.338 93.9267 188.584 94.1935 188.606 94.4689C188.607 94.482 188.607 94.4951 188.607 94.5083C188.903 94.8188 188.903 95.4279 188.903 96.037C188.903 96.6461 188.903 96.9566 188.607 97.5657ZM191.489 93.2767C191.79 93.8661 191.89 94.4204 191.934 94.7363C192.001 95.2226 192 95.7194 192 95.9856L192 96.037C192 96.0544 192 96.0729 192 96.0926C192 96.3523 192.001 96.8096 191.924 97.2931C191.828 97.8884 191.64 98.41 191.393 98.9184L190.546 100.663H190.212C190.127 100.759 190.038 100.852 189.988 100.905L189.974 100.92L105.527 189.891C102.85 192.701 98.1865 192.704 95.51 189.895C94.1476 188.464 93.5636 186.587 93.5636 184.857C93.5636 183.128 94.1468 181.252 95.5071 179.822M191.489 93.2767C191.316 92.7908 191.078 92.4357 190.938 92.2406C190.903 92.1912 190.866 92.142 190.828 92.0939V91.7408L105.522 2.17912C104.076 0.661813 102.397 0 100.663 0C99.3914 0 97.0569 0.401401 95.6212 2.37737C94.3151 3.83819 93.7895 5.45521 93.7895 7.0619C93.7895 8.26663 94.1183 10.6061 95.9608 12.111L168.333 88.9622L6.88489 88.9622C2.9981 88.9622 0 92.3316 0 96.036C0 99.7405 2.99801 103.11 6.88489 103.11L168.285 103.11" fill="black"></path>
                      <path d="M169.5 104L16.5 102.5V90H171L97.5 10.5V7L107.5 4.5C131.167 29.6667 180.2 81.5 187 87.5C193.8 93.5 191.5 99 189.5 101L105.5 184L94 181.5L169.5 104Z" fill="black"></path>
                    </svg>
                  {% endif %}
                </p>
              </a>
            </div>            
          {% endif %}          
        </div>
        <div class="feature-images-{{ section.id }}">     
          {% for block in section.blocks %}
            <div class="feature-image-{{ section.id }} {% if forloop.first %}active{% endif %}" data-id="{{ block.id }}">
              {% if block.settings.image != blank %}
                <img src="{{ block.settings.image | image_url }}" alt="{{ block.settings.image.alt }}" {% if lazy %}loading="lazy"{% endif %}>      
              {% else %}
                {{ 'image' | placeholder_svg_tag }}
              {% endif %} 
            </div>            
          {% endfor %}       
        </div>
      </div>
    </div>
</div>

<script>

function initFeature24() {
  const featureSection = document.querySelector('.feature-{{ section.id }}');
  if (!featureSection) return;

  const tabsWrapper = featureSection.querySelector('.feature-tabs-icons-{{ section.id }}');
  const tabs = featureSection.querySelectorAll('.feature-tabs-icon-{{ section.id }}');
  const allTextContent = featureSection.querySelectorAll('.feature-text-content-{{ section.id }}');
  const allImages = featureSection.querySelectorAll('.feature-image-{{ section.id }}');
  const textBody = featureSection.querySelector('.feature-text-body-{{ section.id }}');
  let activeIndex = 0;
  let intervalId;
  let maxHeight = 0;

  function activateTab(index, isAutoplay = false) {
    tabs.forEach((tab, i) => {
      if (i === index) {
        tab.classList.add(isAutoplay ? 'active' : 'static-active');
        // Force a reflow to restart the animation
        tab.querySelector('.feature-progress-{{ section.id }}').style.animation = 'none';
        tab.offsetHeight; // Trigger reflow
        tab.querySelector('.feature-progress-{{ section.id }}').style.animation = '';
      } else {
        tab.classList.remove('active', 'static-active');
      }
    });

    allTextContent.forEach(el => {
      el.classList.toggle('active', tabs[index].id === el.getAttribute('data-id'));
    });

    allImages.forEach(el => {
      el.classList.toggle('active', tabs[index].id === el.getAttribute('data-id'));
    });
  }

  function startAutoplay() {
    if (intervalId) {
      clearInterval(intervalId);
    }
    activeIndex = 0;
    activateTab(activeIndex, true);

    intervalId = setInterval(() => {
      activeIndex = (activeIndex + 1) % tabs.length;
      activateTab(activeIndex, true);
    }, {{ icons_autoplay_delay }}000);
  }

  function stopAutoplay() {
    clearInterval(intervalId);
    intervalId = null;
  }

  function setupTabs() {
    tabs.forEach((tab, index) => {
      tab.addEventListener("click", () => {
        stopAutoplay(); 
        activateTab(index, false);
        activeIndex = index;
      });
    });
  }

  function setMaxHeight() {
    if (allTextContent.length) {  
      maxHeight = Math.max(...Array.from(allTextContent).map(el => el.offsetHeight));
      if (textBody) {
        textBody.style.minHeight = `${maxHeight}px`;
      }
    }
  }

  // Lazy load the functionality
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        setupTabs();
        setMaxHeight();
        if ("{{ use_autoplay }}" === "true") {
          startAutoplay();
        } else {
          activateTab(0, false);
        }
        observer.disconnect();
      }
    });
  }, {
    root: null,
    threshold: 0.1
  });

  if (tabsWrapper) {
    observer.observe(tabsWrapper);
  }
}

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', initFeature24);

// Handle Shopify theme editor events
if (Shopify.designMode) {
  document.addEventListener('shopify:section:load', initFeature24);
}

</script>

{% schema %}
  {
    "name": "SS - Feature #24",
    "settings": [
      {
        "type": "header",
        "content": "Desktop layout settings"
      },
      {
        "type": "select",
        "id": "body_layout",
        "label": "Image placement",
        "default": "text_image",
        "options": [
          {
            "label": "Align image right",
            "value": "text_image"
          },
          {
            "label": "Align image left",
            "value": "image_text"
          }
        ]
      },
      {
        "type": "range",
        "id": "body_gap",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Gap",
        "default": 92
      },
      {
        "type": "header",
        "content": "Content settings"
      }, 
      {
        "type": "text_alignment",
        "id": "content_align",
        "label": "Alignment",
        "default": "center"
      },
      {
        "type": "text_alignment",
        "id": "content_align_mobile",
        "label": "Alignment - mobile",
        "default": "center"
      },
      {
        "type": "header",
        "content": "Heading settings"
      },
      {
        "type": "richtext",
        "id": "heading",
        "label": "Heading",
        "default": "<h2>Heading</h2>"
      },
      {
        "type": "checkbox",
        "id": "heading_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "heading_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "heading_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 36
      },
      {
        "type": "range",
        "id": "heading_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 24
      },
      {
        "type": "range",
        "id": "heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },
      {
        "type": "header",
        "content": "Subheading settings"
      },
      {
        "type": "richtext",
        "id": "sub_heading",
        "label": "Subheading",
        "default": "<p>Subheading</p>"
      },
      {
        "type": "checkbox",
        "id": "sub_heading_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "sub_heading_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "sub_heading_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 18
      },
      {
        "type": "range",
        "id": "sub_heading_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "sub_heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 150
      },
      {
        "type": "range",
        "id": "sub_heading_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 16
      },
      {
        "type": "range",
        "id": "sub_heading_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 24
      },
      {
        "type": "header",
        "content": "Icons settings"
      },      
      {
        "type": "range",
        "id": "icons_padding_horizontal",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal",
        "default": 6
      },
      {
        "type": "range",
        "id": "icons_padding_horizontal_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal - mobile",
        "default": 6
      },
      {
        "type": "range",
        "id": "icons_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical",
        "default": 4
      },
      {
        "type": "range",
        "id": "icons_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical - mobile",
        "default": 4
      }, 
      {
        "type": "range",
        "id": "icons_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Roundness",
        "default": 100
      },
      {
        "type": "range",
        "id": "icons_gap",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Gap",
        "default": 16
      },
      {
        "type": "range",
        "id": "icons_gap_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Gap - mobile",
        "default": 10
      }, 
      {
        "type": "range",
        "id": "icons_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 48
      },
      {
        "type": "range",
        "id": "icons_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 40
      },
      {
        "type": "header",
        "content": "Icons autoplay settings"
      },
      {
        "type": "checkbox",
        "id": "use_autoplay",
        "label": "Use autoplay",
        "default": true
      },  
      {
        "type": "range",
        "id": "icons_autoplay_delay",
        "min": 1,
        "max": 15,
        "step": 1,
        "label": "Autoplay delay",
        "info": "Save settings to view updates",
        "default": 6
      },      
      {
        "type": "header",
        "content": "Icon settings",
        "info": "Adjust icon sizing to fit the bar"
      },
      {
        "type": "range",
        "id": "icon_size",
        "min": 10,
        "max": 50,
        "step": 2,
        "unit": "px",
        "label": "Size",
        "default": 22
      },
      {
        "type": "range",
        "id": "icon_size_mobile",
        "min": 10,
        "max": 50,
        "step": 2,
        "unit": "px",
        "label": "Size - mobile",
        "default": 18
      },    
      {
        "type": "header",
        "content": "Tab title settings"
      },
      {
        "type": "checkbox",
        "id": "tab_title_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "tab_title_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "tab_title_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 12
      },
      {
        "type": "range",
        "id": "tab_title_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 12
      },
      {
        "type": "range",
        "id": "tab_title_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },  
      {
        "type": "range",
        "id": "tab_title_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 6
      },
      {
        "type": "range",
        "id": "tab_title_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 10
      },
      {
        "type": "header",
        "content": "Feature title settings"
      },
      {
        "type": "checkbox",
        "id": "title_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "title_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "title_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 20
      },
      {
        "type": "range",
        "id": "title_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "title_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      }, 
      {
        "type": "range",
        "id": "title_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 52
      },
      {
        "type": "range",
        "id": "title_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 40
      },
      {
        "type": "header",
        "content": "Feature text settings"
      },
      {
        "type": "checkbox",
        "id": "text_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "text_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "text_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 14
      },
      {
        "type": "range",
        "id": "text_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 14
      },
      {
        "type": "range",
        "id": "text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 150
      }, 
      {
        "type": "range",
        "id": "text_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 12
      },
      {
        "type": "range",
        "id": "text_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 10
      },
      {
        "type": "header",
        "content": "Feature subtext settings"
      },
      {
        "type": "checkbox",
        "id": "sub_text_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "sub_text_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "sub_text_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 12
      },
      {
        "type": "range",
        "id": "sub_text_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 12
      },
      {
        "type": "range",
        "id": "sub_text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      }, 
      {
        "type": "range",
        "id": "sub_text_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 10
      },
      {
        "type": "range",
        "id": "sub_text_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 8
      },
      {
        "type": "header",
        "content": "Button settings"
      },
      {
        "type": "text",
        "id": "button",
        "label": "Button label",
        "default": "Get started",
        "info": "Leave the label blank to hide the button."
      },
      {
        "type": "url",
        "id": "button_url",
        "label": "URL"
      },
      {
        "type": "checkbox",
        "id": "button_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "button_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "button_size",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "px",
        "label": "Font size",
        "default": 14
      },
      {
        "type": "range",
        "id": "button_size_mobile",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 14
      },
      {
        "type": "range",
        "id": "button_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 100
      },
      {
        "type": "range",
        "id": "button_padding_horizontal",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal",
        "default": 32
      },
      {
        "type": "range",
        "id": "button_padding_horizontal_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding horizontal - mobile",
        "default": 32
      },
      {
        "type": "range",
        "id": "button_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical",
        "default": 10
      },
      {
        "type": "range",
        "id": "button_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding vertical - mobile",
        "default": 10
      },      
      {
        "type": "range",
        "id": "button_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Roundness",
        "default": 100
      },
      {
        "type": "range",
        "id": "button_border_thickness",
        "min": 0,
        "max": 10,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 1
      },
      {
        "type": "range",
        "id": "button_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 52
      },
      {
        "type": "range",
        "id": "button_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 52
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button style",
        "default": "outline",
        "options": [
          {
            "label": "Link",
            "value": "link"
          },
          {
            "label": "Non-outline",
            "value": "non_outline"
          },
          {
            "label": "Non-outline & arrow",
            "value": "non_outline_arrow"
          },
          {
            "label": "Outline",
            "value": "outline"
          },
          {
            "label": "Outline & arrow",
            "value": "outline_arrow"
          }
        ]
      },
      {
        "type": "header",
        "content": "Image settings"
      },
      {
        "type": "range",
        "id": "image_width",
        "min": 20,
        "max": 60,
        "step": 2,
        "unit": "%",
        "label": "Width - desktop",
        "default": 40
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Aspect ratio",
        "default": "square",
        "options": [
          {
            "label": "Portrait",
            "value": "portrait"
          },
          {
            "label": "Square",
            "value": "square"
          },
          {
            "label": "Landscape",
            "value": "landscape"
          },
          {
            "label": "Original",
            "value": "original"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_ratio_mobile",
        "label": "Aspect ratio - mobile",
        "default": "landscape",
        "options": [
          {
            "label": "Portrait",
            "value": "portrait"
          },
          {
            "label": "Square",
            "value": "square"
          },
          {
            "label": "Landscape",
            "value": "landscape"
          },
          {
            "label": "Original",
            "value": "original"
          }
        ]
      },
      {
        "type": "range",
        "id": "image_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Roundness",
        "default": 12
      },  
      {
        "type": "range",
        "id": "images_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 40
      },
      {
        "type": "header",
        "content": "Tabs colors"
      },
      {
        "type": "color",
        "label": "Title",
        "id": "tab_title_color",
        "default": "#39392D"
      },
      {
        "type": "color",
        "label": "Icon",
        "id": "icon_color",
        "default": "#39392D"
      },
      {
        "type": "color",
        "label": "Icon background",
        "id": "icon_bg_color",
        "default": "#FFFFFF"
      }, 
      {
        "type": "color",
        "label": "Icons bar background",
        "id": "icons_bg_color",
        "default": "#e7e7e2"
      }, 
      {
        "type": "color",
        "label": "Progress",
        "id": "circle_color",
        "default": "#f4f5f0"
      }, 
      {
        "type": "color",
        "label": "Progress active",
        "id": "circle_active_color",
        "default": "#ff6a1b"
      },  
      {
        "type": "header",
        "content": "Feature content colors"
      },
      {
        "type": "color",
        "label": "Title",
        "id": "title_color",
        "default": "#39392D"
      }, 
      {
        "type": "color",
        "label": "Text",
        "id": "text_color",
        "default": "#39392D"
      }, 
      {
        "type": "color",
        "label": "Subtext",
        "id": "sub_text_color",
        "default": "#39392D"
      }, 
      {
        "type": "header",
        "content": "Button colors"
      },
      {
        "type": "color",
        "label": "Text",
        "id": "button_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color",
        "label": "Hover text",
        "id": "button_hover_color",
        "default": "#39392D"
      },
      {
        "type": "color",
        "label": "Background",
        "id": "button_bg_color",
        "default": "#39392D"
      },
      {
        "type": "color",
        "label": "Background hover",
        "id": "button_bg_hover_color",
        "default": "#f9f9f6"
      },
      {
        "type": "color",
        "label": "Button border",
        "id": "button_border_color",
        "default": "#39392D"
      },
      {
        "type": "color",
        "label": "Border hover",
        "id": "button_border_hover_color",
        "default": "#39392D"
      }, 
      {
        "type": "header",
        "content": "Section colors"
      },
      {
        "type": "color",
        "label": "Heading",
        "id": "heading_color",
        "default": "#39392D"
      },
      {
        "type": "color",
        "label": "Subheading",
        "id": "sub_heading_color",
        "default": "#39392D"
      },
      {
        "type": "color",
        "label": "Section background",
        "id": "background_color",
        "default": "#f9f9f6"
      },
      {
        "type": "color_background",
        "id": "background_gradient",
        "label": "Section background gradient",
        "info": "Remove gradient to replace with solid colors"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "border_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Section margin (outside)"
      },
      {
        "type": "range",
        "id": "margin_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin top",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_bottom",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin bottom",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Margin sides",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Margin sides mobile",
        "default": 0
      },
      {
        "type": "header",
        "content": "Section padding (inside)"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Padding top",
        "default": 36
      },
      {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Padding bottom",
         "default": 36
      },
      {
        "type": "range",
        "id": "padding_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Padding sides",
        "default": 5
      },
      {
        "type": "range",
        "id": "padding_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Padding sides mobile",
        "default": 1.5
      },
      {
        "type": "header",
        "content": "Section settings"
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full width",
        "default": false
      },
      {
        "type": "range",
        "id": "content_width",
        "min": 800,
        "max": 2000,
        "step": 100,
        "unit": "px",
        "label": "Section content width",
        "default": 1200
      },
      {
        "type": "range",
        "id": "border_thickness",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 0
      },
      {
        "type": "range",
        "id": "section_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Section roundness",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "lazy",
        "label": "Lazy load",
        "info": "Lazy load images for speed optimisation",
        "default": true
      }
    ],
    "blocks": [          
      {
        "type": "feature",
        "name": "Feature",
        "limit": 6,
        "settings": [
          {
            "type": "select",
            "id": "icon",
            "label": "Icon",
            "options": [
              {
                "value": "atc",
                "label": "Add to cart",
                "group": "Shop"
              },
              {
                "value": "gift",
                "label": "Gift",
                "group": "Shop"
              },
              {
                "value": "heart",
                "label": "Heart",
                "group": "Shop"
              },
              {
                "value": "bi-ecology",
                "label": "Ecology",
                "group": "Shop"
              },
              {
                "value": "plant",
                "label": "Plant",
                "group": "Shop"
              },
              {
                "value": "shop",
                "label": "Shop",
                "group": "Shop"
              },
              {
                "value": "pin",
                "label": "Geo pin",
                "group": "Shop"
              },
              {
                "value": "megaphone",
                "label": "Megaphone",
                "group": "Communication"
              },
              {
                "value": "chat",
                "label": "Chat",
                "group": "Communication"
              },
              {
                "value": "chat-dots",
                "label": "Chat w. dots",
                "group": "Communication"
              },
              {
                "value": "people",
                "label": "Real people support",
                "group": "Communication"
              },
              {
                "value": "email",
                "label": "Email",
                "group": "Communication"
              },
              {
                "value": "globe",
                "label": "Globe",
                "group": "Communication"
              },
              {
                "value": "newsletter",
                "label": "Newsletter",
                "group": "Communication"
              },
              {
                "value": "love",
                "label": "Love",
                "group": "Communication"
              },
              {
                "value": "phone",
                "label": "Phone",
                "group": "Communication"
              },
              {
                "value": "share",
                "label": "Share",
                "group": "Communication"
              },
              {
                "value": "box",
                "label": "Box",
                "group": "Delivery"
              },
              {
                "value": "worldwide",
                "label": "Worldwide",
                "group": "Delivery"
              },
              {
                "value": "truck",
                "label": "Truck",
                "group": "Delivery"
              },
              {
                "value": "airplane",
                "label": "Airplane",
                "group": "Delivery"
              },
              {
                "value": "return_arrow",
                "label": "Return purchase",
                "group": "Delivery"
              },
              {
                "value": "time",
                "label": "Time",
                "group": "Delivery"
              },
              {
                "value": "payment",
                "label": "Secure payment",
                "group": "Security"
              },
              {
                "value": "mobile",
                "label": "Lock",
                "group": "Security"
              },
              {
                "value": "shield",
                "label": "Shield",
                "group": "Security"
              },
              {
                "value": "none",
                "label": "None"
              }
            ]
          },
          {
            "type": "image_picker",
            "label": "Custom icon",
            "id": "custom_icon"
          },  
          {
            "type": "text",
            "id": "tab_title",
            "label": "Tab title",
            "default": "Tab title"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Title"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Text</p>"
          },
          {
            "type": "richtext",
            "id": "sub_text",
            "label": "Subtext",
            "default": "<p>Subtext</p>"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "SS - Feature #24",
        "blocks": [
          {
            "type": "feature"
          },
          {
            "type": "feature"
          },
          {
            "type": "feature"
          },
          {
            "type": "feature"
          },
          {
            "type": "feature"
          },
          {
            "type": "feature"
          }
        ]
      }
    ]
  }
{% endschema %}