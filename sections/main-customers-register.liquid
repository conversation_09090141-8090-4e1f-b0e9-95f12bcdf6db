<div class="sm:container">
  <div class="customer-form">
    {%- if section.settings.image != blank -%}
      {{- section.settings.image | image_url: width: section.settings.image.width | image_tag: sizes: '50vw', widths: '300,400,500,600,700,800,1000,1200,1400', class: 'customer-form__image rounded' -}}
    {%- endif -%}

    <div {% render 'surface', class: 'customer-form__box', background_gradient: section.settings.background_gradient, background: section.settings.background, text_color: section.settings.text_color, background_fallback: 'bg-secondary' %}>
      <div class="customer-form__box-inner text-center">
        <div  class="v-stack gap-12">
          <h1 class="h2">
            {%- assign content = 'customer.register.title' | t -%}
            {%- render 'styled-text', content: content, gradient: section.settings.heading_gradient -%}
          </h1>

          <div class="v-stack gap-6">
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when '@app' -%}
                  {%- render block -%}

                {%- when 'liquid' -%}
                  {%- if block.settings.liquid != blank -%}
                    <div {{ block.shopify_attributes }}>
                      {{- block.settings.liquid -}}
                    </div>
                  {%- endif -%}

                {%- when 'fields' -%}
                  <div {{ block.shopify_attributes }}>
                    {%- form 'create_customer', class: 'form' -%}
                      <div class="fieldset">
                        {%- if form.errors -%}
                          {%- assign form_errors = form.errors | default_errors -%}
                          {%- render 'banner', status: 'error', content: form_errors -%}
                        {%- endif -%}

                        {%- if request.locale.iso_code == 'ja' -%}
                          {%- assign last_name_label = 'customer.register.last_name' | t -%}
                          {%- render 'input', name: 'customer[last_name]', label: last_name_label, autocomplete: 'family-name', required: true -%}

                          {%- assign first_name_label = 'customer.register.first_name' | t -%}
                          {%- render 'input', name: 'customer[first_name]', label: first_name_label, autocomplete: 'given-name', required: true -%}
                        {%- else -%}
                          {%- assign first_name_label = 'customer.register.first_name' | t -%}
                          {%- render 'input', name: 'customer[first_name]', label: first_name_label, autocomplete: 'given-name', required: true -%}

                          {%- assign last_name_label = 'customer.register.last_name' | t -%}
                          {%- render 'input', name: 'customer[last_name]', label: last_name_label, autocomplete: 'family-name', required: true -%}
                        {%- endif -%}

                        {%- assign email_label = 'customer.register.email' | t -%}
                        {%- render 'input', type: 'email', name: 'customer[email]', label: email_label, autocomplete: 'email', required: true -%}

                        {%- assign password_label = 'customer.register.password' | t -%}
                        {%- render 'input', type: 'password', name: 'customer[password]', label: password_label, minlength: 5, autocomplete: 'new-password', required: true -%}

                        {%- if block.settings.show_email_consent -%}
                          {%- assign consent_label = 'customer.register.accepts_marketing' | t -%}
                          {%- render 'checkbox', name: 'customer[accepts_marketing]', label: consent_label -%}
                        {%- endif -%}
                      </div>

                      {%- assign submit_label = 'customer.register.submit' | t -%}
                    {%- render 'button', content: submit_label, type: 'submit', size: 'xl', stretch: true, secondary: true, background: section.settings.button_background, text_color: section.settings.button_text_color -%}

                      <div>
                        <a href="{{ routes.account_login_url }}" class="link text-subdued">{{ 'customer.register.login' | t }}</a>
                      </div>
                    {%- endform -%}
                  </div>
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Customer register",
  "class": "shopify-section--main-customers-register",
  "tag": "section",
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "1400 x 1400px .jpg recommended. Does not show on mobile."
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Heading gradient"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "fields",
      "name": "Fields",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_email_consent",
          "label": "Show email consent",
          "default": false
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid"
        }
      ]
    }
  ]
}
{% endschema %}