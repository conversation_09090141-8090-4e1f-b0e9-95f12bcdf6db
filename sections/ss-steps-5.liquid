{% comment %}
---------------------------------------------------------
Copyright © 2023 Section Store. All rights reserved.
Unauthorized copying, modification, distribution, or use
of this code or any portion of it, is strictly prohibited.
Violators will be prosecuted to the fullest extent of the law.
For inquiries or permissions, contact <EMAIL>
---------------------------------------------------------
{% endcomment %}

{%- liquid 
  assign padding_horizontal = section.settings.padding_horizontal
  assign padding_horizontal_mobile = section.settings.padding_horizontal_mobile
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign border_color = section.settings.border_color
  assign border_thickness = section.settings.border_thickness
  assign margin_top = section.settings.margin_top
  assign margin_bottom = section.settings.margin_bottom
  assign margin_horizontal_mobile = section.settings.margin_horizontal_mobile
  assign margin_horizontal = section.settings.margin_horizontal
  assign background_color = section.settings.background_color
  assign background_gradient = section.settings.background_gradient
  assign full_width = section.settings.full_width
  assign content_width = section.settings.content_width
  assign lazy = section.settings.lazy
  assign section_radius = section.settings.section_radius

  assign layout_width = section.settings.layout_width

  assign heading = section.settings.heading
  assign heading_custom = section.settings.heading_custom
  assign heading_font = section.settings.heading_font
  assign heading_size = section.settings.heading_size
  assign heading_size_mobile = section.settings.heading_size_mobile
  assign heading_height = section.settings.heading_height
  assign heading_color = section.settings.heading_color
  assign heading_align = section.settings.heading_align
  assign heading_align_mobile = section.settings.heading_align_mobile

  assign sub_heading = section.settings.sub_heading
  assign sub_heading_custom = section.settings.sub_heading_custom
  assign sub_heading_font = section.settings.sub_heading_font
  assign sub_heading_size = section.settings.sub_heading_size
  assign sub_heading_size_mobile = section.settings.sub_heading_size_mobile
  assign sub_heading_height = section.settings.sub_heading_height
  assign sub_heading_color = section.settings.sub_heading_color
  assign sub_heading_mt = section.settings.sub_heading_mt
  assign sub_heading_mt_mobile = section.settings.sub_heading_mt_mobile

  assign body_mt_mobile = section.settings.body_mt_mobile
  assign body_mt = section.settings.body_mt
  assign body_gap = section.settings.body_gap

  assign active_steps = section.settings.active_steps

  assign number_size = section.settings.number_size
  assign number_size_mobile = section.settings.number_size_mobile
  assign number_color = section.settings.number_color
  assign number_active_color = section.settings.number_active_color
  assign number_bg_color = section.settings.number_bg_color
  assign number_active_bg_color = section.settings.number_active_bg_color
  assign number_border_active_color = section.settings.number_border_active_color
  assign number_custom = section.settings.number_custom
  assign number_font = section.settings.number_font
  assign number_height = section.settings.number_height
  assign number_circle_size = section.settings.number_circle_size
  assign number_circle_size_mobile = section.settings.number_circle_size_mobile
  assign number_border_thickness = section.settings.number_border_thickness
  assign number_border_color = section.settings.number_border_color

  assign question_size = section.settings.question_size
  assign question_size_mobile = section.settings.question_size_mobile
  assign question_color = section.settings.question_color
  assign question_custom = section.settings.question_custom
  assign question_font = section.settings.question_font
  assign question_height = section.settings.question_height

  assign answer_size = section.settings.answer_size
  assign answer_size_mobile = section.settings.answer_size_mobile
  assign answer_color = section.settings.answer_color
  assign answer_custom = section.settings.answer_custom
  assign answer_font = section.settings.answer_font
  assign answer_height = section.settings.answer_height
  assign answer_padding_top = section.settings.answer_padding_top
  assign answer_padding_bottom = section.settings.answer_padding_bottom
  assign answer_border_thickness = section.settings.answer_border_thickness
  assign answer_border_color = section.settings.answer_border_color

  assign image_width = section.settings.image_width
  assign image_ratio = section.settings.image_ratio
  assign image_ratio_mobile = section.settings.image_ratio_mobile
  assign image_radius = section.settings.image_radius
  assign image_mt_mobile = section.settings.image_mt_mobile
-%}

{%- style -%}
  {{ heading_font | font_face: font_display: 'swap' }}
  {{ sub_heading_font | font_face: font_display: 'swap' }}
  {{ number_font | font_face: font_display: 'swap' }}
  {{ question_font | font_face: font_display: 'swap' }}
  {{ answer_font | font_face: font_display: 'swap' }}

  .section-{{ section.id }} {
    border-top: solid {{ border_color }} {{ border_thickness }}px;
    border-bottom: solid {{ border_color }} {{ border_thickness }}px;
    margin-top: {{ margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ margin_bottom | times: 0.75 | round: 0 }}px;
    margin-left: {{ margin_horizontal_mobile }}rem;
    margin-right: {{ margin_horizontal_mobile }}rem;
    border-radius: {{ section_radius | times: 0.6 | round: 0 }}px;
    overflow: hidden;
  }
  
  .section-{{ section.id }}-settings {
    margin: 0 auto;
    padding-top: {{ padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: {{ padding_horizontal_mobile }}rem;
    padding-right: {{ padding_horizontal_mobile }}rem;
  }
  
  .steps-heading-wrapper-{{ section.id }} {
    width: 100%;
  }

  .steps-heading-{{ section.id }} {
    text-align: {{ heading_align_mobile }};
  }

.steps-heading-{{ section.id }} * {
  margin: 0;
  font-size: var(--text-h2) !important;
  color: {{ heading_color }};
  line-height: {{ heading_height }}%;
  text-transform: var(--heading-text-transform);
  text-decoration: none;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  letter-spacing: var(--heading-letter-spacing);
}

  .steps-subheading-{{ section.id }} {
    text-align: {{ heading_align_mobile }};
    margin-top: {{ sub_heading_mt_mobile }}px;
  }

  .steps-subheading-{{ section.id }} * {
    margin: 0;
    font-size: {{ sub_heading_size_mobile }}px;
    color: {{ sub_heading_color }};
    line-height: {{ sub_heading_height }}%;
    text-transform: unset;
    text-decoration: none;
  }

  .steps-body-{{ section.id }} {
    margin-top: {{ body_mt_mobile }}px;
    display: flex;
    align-items: center;
  }
  
  .steps-wrapper-{{ section.id }} {
    flex: 1 1 auto;
  }

  .steps-step-{{ section.id }} {
    position: relative;
  }

  .steps-toggle-{{ section.id }} {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: 12px;
  }

  .steps-number-{{ section.id }} {
    min-width: {{ number_circle_size_mobile }}px;
    width: {{ number_circle_size_mobile }}px;
    height: {{ number_circle_size_mobile }}px;
    border-radius: 100%;
    border: {{ number_border_thickness }}px solid {{ number_border_color }};
    display: flex;
    align-items: center;
    justify-content: center;
    background: {{ number_bg_color }};
    color: {{ number_color }};
    text-align: center;
    font-size: {{ number_size_mobile }}px;
    line-height: {{ number_height }}%;
    text-transform: uppercase;
  }

  .steps-step-{{ section.id }}.active .steps-number-{{ section.id }} {
    background: {{ number_active_bg_color }};
    border: {{ number_border_thickness }}px solid {{ number_border_active_color }}; 
    color: {{ number_active_color }};
  }

  .steps-question-{{ section.id }} {
    margin: 0px;
    font-size: {{ question_size_mobile }}px;
    color: {{ question_color }};
    line-height: {{ question_height }}%;
    text-transform: unset;
    font-weight: 500;
  }

  .steps-answer-{{ section.id }} {
    transition: all 0.5s ease-in-out;
    overflow: hidden;
    margin-left: {{ number_circle_size_mobile | times: 0.5 }}px;
    border-left: {{ answer_border_thickness }}px solid {{ answer_border_color }};
    padding-left: {{ number_circle_size_mobile | times: 0.5 | plus: 12 }}px;
    margin-top: 6px;
    padding-top: {{ answer_padding_top }}px !important;
    padding-bottom: {{ answer_padding_bottom }}px;
  }

  .steps-step-{{ section.id }}:last-child .steps-answer-{{ section.id }} {
    border-left: none;
  }

  .steps-answer-{{ section.id }} * {
    margin: 0px;
    font-size: {{ answer_size_mobile }}px;
    color: {{ answer_color }};
    line-height: {{ answer_height }}%;
    text-transform: unset;
  }

  .steps-answer-{{ section.id }} p {
    min-height: 10px;
  }

  .steps-answer-{{ section.id }} p:not(:first-child ) {
    margin-top: 10px;
  } 

  .steps-height-cal-{{ section.id }} {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    padding-top: {{ answer_padding_top }}px;
    padding-bottom: {{ answer_padding_bottom }}px;
    margin-left: {{ number_circle_size_mobile | times: 0.5 }}px;
    padding-left: {{ number_circle_size_mobile | times: 0.5 | plus: 12 }}px;
  }

  .steps-height-cal-{{ section.id }} * {
    margin: 0px;
    font-size: {{ answer_size_mobile }}px;
    color: {{ answer_color }};
    line-height: {{ answer_height }}%;
    text-transform: unset;
  }

  .steps-height-cal-{{ section.id }} p {
    min-height: 10px;
  }

  .steps-height-cal-{{ section.id }} p:not(:first-child ) {
    margin-top: 10px;
  }  

  .steps-image-mobile-{{ section.id }} {
    margin-top: {{ image_mt_mobile }}px;
    display: block;
    width: 100%;
    overflow: hidden;
    border-radius: {{ image_radius }}px;
    position: relative;
  }
  
  .steps-image-mobile-{{ section.id }} img,
  .steps-image-mobile-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: inherit;
  }

  .steps-image-desktop-{{ section.id }} {
    display: none;
  }

  .steps-image-mobile-{{ section.id }} svg {
    background: #AFAFAF;
  }

  @media(min-width: 1024px) {
    .section-{{ section.id }} {
      margin-top: {{ margin_top }}px;
      margin-bottom: {{ margin_bottom }}px;
      margin-left: {{ margin_horizontal }}rem;
      margin-right: {{ margin_horizontal }}rem;
      border-radius: {{ section_radius }}px;
    }
    
    .section-{{ section.id }}-settings {
      padding: 0 5rem;
      padding-top: {{ padding_top }}px;
      padding-bottom: {{ padding_bottom }}px;
      padding-left: {{ padding_horizontal }}rem;
      padding-right: {{ padding_horizontal }}rem;
    }

    .steps-heading-wrapper-{{ section.id }} {
      max-width: {{ layout_width }}%;
    }

    .steps-heading-{{ section.id }} {
      text-align: {{ heading_align }};
    }
  
    .steps-heading-{{ section.id }} * {
      font-size: {{ heading_size }}px;
    }   

    .steps-subheading-{{ section.id }} {
      margin-top: {{ sub_heading_mt }}px;
      text-align: {{ heading_align }};
    }
  
    .steps-subheading-{{ section.id }} * {
      font-size: {{ sub_heading_size }}px;
    } 

    .steps-body-{{ section.id }} {
      margin-top: {{ body_mt }}px;
      gap: {{ body_gap }}px;
    }

    .steps-number-{{ section.id }} {
      min-width: {{ number_circle_size }}px;
      width: {{ number_circle_size }}px;
      height: {{ number_circle_size }}px;
      font-size: {{ number_size }}px;
    }

    .steps-question-{{ section.id }} {
      font-size: {{ question_size }}px;
    }

    .steps-answer-{{ section.id }} {
      margin-left: {{ number_circle_size | times: 0.5 }}px;
      padding-left: {{ number_circle_size | times: 0.5 | plus: 12 }}px;
    }

    .steps-answer-{{ section.id }} * {
      font-size: {{ answer_size }}px;
    }

    .steps-height-cal-{{ section.id }} {
      margin-left: {{ number_circle_size | times: 0.5 }}px;
      padding-left: {{ number_circle_size | times: 0.5 | plus: 12 }}px;
    }

    .steps-height-cal-{{ section.id }} * {
      font-size: {{ answer_size }}px;
    }

    .steps-image-desktop-{{ section.id }} {
      display: none;
      flex-shrink: 0;
      width: {{ image_width }}%;
      height: 100%;
      overflow: hidden;
      border-radius: {{ image_radius }}px;
      position: relative;
    }

    .steps-image-desktop-{{ section.id }}.active {
      display: block;
    }

  .steps-image-desktop-{{ section.id }} img,
  .steps-image-desktop-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: inherit;
  }
    
    .steps-image-desktop-{{ section.id }} svg {
      background-color: #AFAFAF;
    }

    .steps-image-mobile-{{ section.id }} {
      display: none;
    }
  }
  
{%- endstyle -%}

{% unless full_width %}
  <style>
    .section-{{ section.id }}-settings {
      max-width: {{ content_width }}px;
    }
  </style>
{% endunless %}

{% if margin_horizontal_mobile > 0 %}
  <style>
    .section-{{ section.id }} {
      border-left: solid {{ border_color }} {{ border_thickness }}px;
      border-right: solid {{ border_color }} {{ border_thickness }}px;
    }
    
    @media(min-width: 1024px) {
      .section-{{ section.id }} {
        border-left: 0px;
        border-right: 0px;
      }
    }
  </style>
{% endif %}

{% if margin_horizontal > 0 %}
  <style>
    @media(min-width: 1024px) {
      .section-{{ section.id }} {
        border-left: solid {{ border_color }} {{ border_thickness }}px;
        border-right: solid {{ border_color }} {{ border_thickness }}px;
      }
    }
  </style>
{% endif %}


{% if sub_heading_custom %}
  <style>
    .steps-subheading-{{ section.id }} * {
      font-family: {{ sub_heading_font.family }}, {{ sub_heading_font.fallback_families }};
      font-weight: {{ sub_heading_font.weight }};
      font-style: {{ sub_heading_font.style }};
    }
  </style>
{% endif %}

{% if number_custom %}
  <style>
    .steps-number-{{ section.id }} {
      font-family: {{ number_font.family }}, {{ number_font.fallback_families }};
      font-weight: {{ number_font.weight }};
      font-style: {{ number_font.style }};
    }
  </style>
{% endif %}

{% if question_custom %}
  <style>
    .steps-question-{{ section.id }} {
      font-family: {{ question_font.family }}, {{ question_font.fallback_families }};
      font-weight: {{ question_font.weight }};
      font-style: {{ question_font.style }};
    }
  </style>
{% endif %}

{% if answer_custom %}
  <style>
    .steps-answer-{{ section.id }} *,
    .steps-height-cal-{{ section.id }} * {
      font-family: {{ answer_font.family }}, {{ answer_font.fallback_families }};
      font-weight: {{ answer_font.weight }};
      font-style: {{ answer_font.style }};
    }
  </style>
{% endif %}

{% if image_ratio_mobile == "portrait" %}
  <style>    
    .steps-image-mobile-{{ section.id }} {
      aspect-ratio: 9.6/12; 
    }     
  </style>
{% elsif image_ratio_mobile == "landscape" %}
  <style>    
    .steps-image-mobile-{{ section.id }}{
      aspect-ratio: 12/9; 
    }    
  </style>
{% elsif image_ratio_mobile == "square" %}
  <style>    
    .steps-image-mobile-{{ section.id }} {
      aspect-ratio: 12/12; 
    }    
  </style>
{% endif %}

{% if image_ratio == "portrait" %}
  <style>
    @media(min-width: 1024px) {
     .steps-image-desktop-{{ section.id }} {
       aspect-ratio: 9.6/12; 
      } 
    }
  </style>
{% elsif image_ratio == "landscape" %}
  <style>
    @media(min-width: 1024px) {
      .steps-image-desktop-{{ section.id }}{
       aspect-ratio: 12/9; 
      }
    }
  </style>
{% elsif image_ratio == "square" %}
  <style>
    @media(min-width: 1024px) {
      .steps-image-desktop-{{ section.id }} {
       aspect-ratio: 12/12; 
      }
    }
  </style>
{% endif %}

{% if heading_align == "center" %}
  <style>
    .steps-heading-wrapper-{{ section.id }} {
      margin-left: auto;
      margin-right: auto;
    }
  </style>
{% elsif heading_align == "right"%}
  <style>
    .steps-heading-wrapper-{{ section.id }} {
      margin-left: auto;
    }
  </style>
{% endif %}

<div class="section-{{ section.id }} steps-{{ section.id }}" style="background-color:{{ background_color }}; background-image: {{ background_gradient }};">
  <div class="section-{{ section.id }}-settings">
    <div class="steps-heading-wrapper-{{ section.id }}">
      {% if heading != blank %}
        <div class="steps-heading-{{ section.id }}">
          {{ heading }}
        </div>
      {% endif %}
      {% if sub_heading != blank %}
        <div class="steps-subheading-{{ section.id }}">
          {{ sub_heading }}
        </div>
      {% endif %}
    </div>    
    <div class="steps-body-{{ section.id }}">
      <div class="steps-wrapper-{{ section.id }}">
        {% for block in section.blocks %}
          <div class="steps-step-{{ section.id }}" id="{{ block.id }}">
            <div class="steps-toggle-{{ section.id }}">
              <div class="steps-number-{{ section.id }}">{{ forloop.index }}</div>
              <p class="steps-question-{{ section.id }}">{{ block.settings.question }}</p>              
            </div>            
            <div class="steps-answer-{{ section.id }} {% if forloop.index > active_steps %}hide{% endif %}" style="height: 0px; padding-bottom: 0px; padding-top: 0px;">
              {{ block.settings.answer }}
              <div class="steps-image-mobile-{{ section.id }}">          
                {% if block.settings.image != blank %}
                  <img src="{{ block.settings.image | image_url }}" alt="{{ block.settings.image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                {% else %}
                  {{ 'image' | placeholder_svg_tag }}
                {% endif %}                  
              </div>
            </div>
            <div class="steps-height-cal-{{ section.id }}">
              {{ block.settings.answer }}
              <div class="steps-image-mobile-{{ section.id }}">          
                {% if block.settings.image != blank %}
                  <img src="{{ block.settings.image | image_url }}" alt="{{ block.settings.image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                {% else %}
                  {{ 'image' | placeholder_svg_tag }}
                {% endif %}                  
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
      {% for block in section.blocks %}
        <div class="steps-image-desktop-{{ section.id }} {% if forloop.first %}active{% endif %}" data-id="{{ block.id }}">          
          {% if block.settings.image != blank %}
            <img src="{{ block.settings.image | image_url }}" alt="{{ block.settings.image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
          {% else %}
            {{ 'image' | placeholder_svg_tag }}
          {% endif %}                  
        </div>
    {% endfor %}   
    </div>
  </div>
</div>

<script>
  function initSteps5() {
    const answerPaddingTop = {{ answer_padding_top }};
    const answerPaddingBottom = {{ answer_padding_bottom }};
    const autoClose = {{ section.settings.auto_close }};
  
    const faqs = document.querySelectorAll('.steps-step-{{ section.id }}');
    const buttons = document.querySelectorAll('.steps-toggle-{{ section.id }}');
    const answers = document.querySelectorAll('.steps-answer-{{ section.id }}');
    const answersHeightCalElements = document.querySelectorAll('.steps-height-cal-{{ section.id }}');
    const images = document.querySelectorAll('.steps-image-desktop-{{ section.id }}');
  
    let activeIndex = -1;
  
    function calculateHeight(index) {
      const heightCalElement = answersHeightCalElements[index];
      const totalHeight = heightCalElement.scrollHeight;
      return totalHeight + 24; // Add extra padding to ensure consistent spacing
    }
  
    function openAnswer(index) {
      // If auto-close is enabled, close all other answers first
      if (autoClose) {
        answers.forEach((answer, i) => {
          if (i !== index && !answer.classList.contains('hide')) {
            closeAnswer(i);
          }
        });
      }
  
      const height = calculateHeight(index);
  
      const answer = answers[index];
      answer.style.height = '0px';
      answer.style.paddingTop = '0px';
      answer.style.paddingBottom = '0px';
      
      // Force a reflow
      answer.offsetHeight;
  
      // Set height and padding in a single frame
      requestAnimationFrame(() => {
        answer.style.height = `${height}px`;
        answer.style.paddingTop = `${answerPaddingTop}px`;
        answer.style.paddingBottom = `${answerPaddingBottom}px`;
      });
      
      faqs[index].classList.add('active');
      activateImage(faqs[index].id);
      answers[index].classList.remove('hide');
    }
  
    function closeAnswer(index) {
      answers[index].style.height = '0px';
      answers[index].style.paddingTop = '0px';
      answers[index].style.paddingBottom = '0px';
      faqs[index].classList.remove('active');
  
      answers[index].addEventListener('transitionend', function handler() {
        answers[index].classList.add('hide');
        answers[index].removeEventListener('transitionend', handler);
      }, { once: true });
    }
  
    function toggleAnswer(index) {
      if (answers[index].classList.contains('hide')) {
        openAnswer(index);
        activeIndex = index;
      } else {
        closeAnswer(index);
        activeIndex = -1;
      }
    }
  
    function activateImage(itemId) {
      images.forEach((img) => img.classList.remove('active'));
      const correspondingImage = document.querySelector(`.steps-image-desktop-{{ section.id }}[data-id="${itemId}"]`);
      if (correspondingImage) {
        correspondingImage.classList.add('active');
      }
    }
  
    buttons.forEach((button, index) => {
      button.addEventListener('click', () => toggleAnswer(index));
    });
  
    function recalculateHeights() {
      answers.forEach((answer, index) => {
        if (!answer.classList.contains('hide')) {
          const height = calculateHeight(index);
          answer.style.height = `${height}px`;
        }
      });
    }
  
    let resizeTimer;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(recalculateHeights, 250);
    });
  
    function initializeAnswers() {
      const initialOpenCount = Number('{{ active_steps }}');
      // If auto-close is enabled, only open the first step
      const stepsToOpen = autoClose ? Math.min(1, initialOpenCount) : initialOpenCount;
      for (let i = 0; i < Math.min(stepsToOpen, answers.length); i++) {
        openAnswer(i);
        activeIndex = i;
      }
    }
  
    // Create a MutationObserver to watch for changes in the section
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' || mutation.type === 'subtree') {
          recalculateHeights();
        }
      });
    });
  
    // Start observing the section for changes
    const sectionElement = document.querySelector('.section-{{ section.id }}');
    observer.observe(sectionElement, { childList: true, subtree: true });
  
    // Initialize on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', () => {
      initializeAnswers();
      // Recalculate heights after a short delay
      setTimeout(recalculateHeights, 100);
    });
  
    // Recalculate heights after window load
    window.addEventListener('load', () => {
      setTimeout(recalculateHeights, 100);
    });
  }
  
  initSteps5();
  
  if (Shopify.designMode) {
    document.addEventListener('shopify:section:unload', initSteps5);
    document.addEventListener('shopify:section:load', initSteps5);
  }
</script>


{% schema %}
  {
    "name": "SS - Steps #5",
    "settings": [
      {
        "type": "paragraph",
        "content": "Save settings to view updates"
      },
      {
        "type": "header",
        "content": "Heading settings"
      },
      {
        "type": "richtext",
        "id": "heading",
        "label": "Text",
        "default": "<h2>Steps #5</h2>"
      },
      {
        "type": "checkbox",
        "id": "heading_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "heading_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "heading_size",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 36
      },
      {
        "type": "range",
        "id": "heading_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 36
      },
      {
        "type": "range",
        "id": "heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },
      {
        "type": "header",
        "content": "Subheading settings"
      },
      {
        "type": "richtext",
        "id": "sub_heading",
        "label": "Text",
        "default": "<p>We're here to offer evidence-based truths</p>"
      },
      {
        "type": "checkbox",
        "id": "sub_heading_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "sub_heading_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "sub_heading_size",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 16
      },
      {
        "type": "range",
        "id": "sub_heading_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "sub_heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 150
      },
      {
        "type": "range",
        "id": "sub_heading_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 16
      },
      {
        "type": "range",
        "id": "sub_heading_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 16
      },
      {
        "type": "header",
        "content": "Heading layout settings"
      },
      {
        "type": "range",
        "id": "layout_width",
        "min": 30,
        "max": 100,
        "step": 2,
        "unit": "%",
        "label": "Max width",
        "default": 60
      },
      {
        "type": "text_alignment",
        "id": "heading_align",
        "label": "Alignment",
        "default": "center"
      },
      {
        "type": "text_alignment",
        "id": "heading_align_mobile",
        "label": "Alignment - mobile",
        "default": "center"
      },
      {
        "type": "header",
        "content": "Content layout settings"
      },
      {
        "type": "range",
        "id": "body_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top",
        "default": 40
      },
      {
        "type": "range",
        "id": "body_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 32
      },
      {
        "type": "range",
        "id": "body_gap",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Gap",
        "default": 40
      },
      {
        "type": "header",
        "content": "Steps settings"
      },
      {
        "type": "range",
        "id": "active_steps",
        "min": 0,
        "max": 10,
        "step": 1,
        "label": "Active steps",
        "info": "Set the number of circular steps initially displayed with their content visible.",
        "default": 1
      },
      {
        "type": "checkbox",
        "id": "auto_close",
        "label": "Auto-close tabs",
        "info": "Automatically close other tabs when opening a new one",
        "default": false
      },
      {
        "type": "header",
        "content": "Circle settings"
      },
      {
        "type": "range",
        "id": "number_circle_size",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Circle size",
        "default": 52
      },
      {
        "type": "range",
        "id": "number_circle_size_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Circle size - mobile",
        "default": 52
      },
      {
        "type": "range",
        "id": "number_border_thickness",
        "min": 0,
        "max": 10,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 1
      },
      {
        "type": "header",
        "content": "Number settings"
      },
      {
        "type": "checkbox",
        "id": "number_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "number_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "number_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 26
      },
      {
        "type": "range",
        "id": "number_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 26
      },
      {
        "type": "range",
        "id": "number_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 100
      },      
      {
        "type": "header",
        "content": "Question settings"
      },
      {
        "type": "checkbox",
        "id": "question_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "question_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "question_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 24
      },
      {
        "type": "range",
        "id": "question_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 18
      },
      {
        "type": "range",
        "id": "question_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 130
      },    
      {
        "type": "header",
        "content": "Answer settings"
      },
      {
        "type": "checkbox",
        "id": "answer_custom",
        "label": "Use custom font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "answer_font",
        "label": "Font family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "answer_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size",
        "default": 16
      },
      {
        "type": "range",
        "id": "answer_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Font size - mobile",
        "default": 14
      },
      {
        "type": "range",
        "id": "answer_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Line height",
        "default": 150
      },
      {
        "type": "range",
        "id": "answer_padding_top",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding top",
        "default": 12
      },
      {
        "type": "range",
        "id": "answer_padding_bottom",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Padding bottom",
        "default": 24
      },
      {
        "type": "range",
        "id": "answer_border_thickness",
        "min": 0,
        "max": 5,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 2
      },
      {
        "type": "header",
        "content": "Image settings"
      },
      {
        "type": "range",
        "id": "image_width",
        "min": 20,
        "max": 60,
        "step": 2,
        "unit": "%",
        "label": "Width",
        "default": 40
      }, 
      {
        "type": "range",
        "id": "image_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Roundness",
        "default": 10
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Aspect ratio",
        "default": "square",
        "options": [
          {
            "label": "Portrait",
            "value": "portrait"
          },
          {
            "label": "Square",
            "value": "square"
          },
          {
            "label": "Landscape",
            "value": "landscape"
          },
          {
            "label": "Original",
            "value": "original"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_ratio_mobile",
        "label": "Aspect ratio - mobile",
        "default": "square",
        "options": [
          {
            "label": "Portrait",
            "value": "portrait"
          },
          {
            "label": "Square",
            "value": "square"
          },
          {
            "label": "Landscape",
            "value": "landscape"
          },
          {
            "label": "Original",
            "value": "original"
          }
        ]
      },
      {
        "type": "range",
        "id": "image_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Margin top - mobile",
        "default": 16
      },
      {
        "type": "header",
        "content": "Number colors"
      },
      {
        "type": "color",
        "label": "Text",
        "id": "number_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Text active",
        "id": "number_active_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color",
        "label": "Background",
        "id": "number_bg_color"
      },
      {
        "type": "color",
        "label": "Background active",
        "id": "number_active_bg_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "number_border_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Border active",
        "id": "number_border_active_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Step colors"
      },         
      {
        "type": "color",
        "label": "Question",
        "id": "question_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Answer",
        "id": "answer_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Answer border",
        "id": "answer_border_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Section colors"
      },
      {
        "type": "color",
        "label": "Heading",
        "id": "heading_color",
        "default": "#000000"
      }, 
      {
        "type": "color",
        "label": "Subheading",
        "id": "sub_heading_color",
        "default": "#000000"
      }, 
      {
        "type": "color",
        "label": "Section background",
        "id": "background_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color_background",
        "id": "background_gradient",
        "label": "Section background gradient"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "border_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Section margin (outside)"
      },
      {
        "type": "range",
        "id": "margin_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin top",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_bottom",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin bottom",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Margin sides",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Margin sides mobile",
        "default": 0
      },
      {
        "type": "header",
        "content": "Section padding (inside)"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Padding top",
        "default": 36
      },
      {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Padding bottom",
         "default": 36
      },
      {
        "type": "range",
        "id": "padding_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Padding sides",
        "default": 5
      },
      {
        "type": "range",
        "id": "padding_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Padding sides mobile",
        "default": 1.5
      },
      {
        "type": "header",
        "content": "Section settings"
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full width",
        "default": false
      },
      {
        "type": "range",
        "id": "content_width",
        "min": 800,
        "max": 2000,
        "step": 100,
        "unit": "px",
        "label": "Section content width",
        "default": 1200
      },
      {
        "type": "range",
        "id": "border_thickness",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 0
      },
      {
        "type": "range",
        "id": "section_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Section roundness",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "lazy",
        "label": "Lazy load",
        "info": "Lazy load images for speed optimisation",
        "default": true
      }
    ],
    "blocks": [
      {
        "type": "step",
        "name": "Step",
        "settings": [
          {
            "type": "text",
            "id": "question",
            "label": "Question",
            "default": "Question"
          },
          {
            "type": "richtext",
            "id": "answer",
            "label": "Answer",
            "default": "<p>Answer</p>"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image"        
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "SS - Steps #5",
        "blocks": [
          {
            "type": "step"
          },
          {
            "type": "step"
          },
          {
            "type": "step"
          }
        ]
      }
    ]
  }
{% endschema %}
