{%- capture minify -%}
    <link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css" />
    {{ 'slider-navigation.css' | asset_url | stylesheet_tag }}
    {{ 'slider-image-sizing.css' | asset_url | stylesheet_tag }}
    {% style %}
      #LT--{{ section.id }} {
        background: {{ section.settings.color_bg }};
        {%- if section.settings.image_bg != blank -%}
          background-image: url("{{ section.settings.image_bg | image_url: width: 2500 }}");
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
        {%- endif -%}
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
        position: relative;
        overflow: hidden;
        width: 100%;
      }
      #LT--{{ section.id }} h2.lt-testimonial-title, #LT--{{ section.id }} p.lt-testimonial-subtitle {
        color: {{ section.settings.color_text }};
        text-align: center;
      }
      #LT--{{ section.id }} .lt-testimonial-titles {
        margin-bottom: 20px;
      }
      #LT--{{ section.id }} h2, #LT--{{ section.id }} h3, #LT--{{ section.id }} p {
        margin: 0;
      }
      #LT--{{ section.id }} .lt-testimonial-block {
        border: {{ section.settings.border_width_block }}px solid {{ section.settings.color_border_block }};
        background: {{ section.settings.color_bg_block }};
      }
      {% if section.settings.border-media == true %}
        #LT--{{ section.id }} .lt-testimonial-block .image-item-wrapper {
          border: 1px solid {{ section.settings.color_border_block }};
        }
      {% endif %}
      #LT--{{ section.id }} .lt-testimonial-block-slider h3, #LT--{{ section.id }} .lt-testimonial-block-slider p, #LT--{{ section.id }} .lt-testimonial-block-slider span {
        color: {{ section.settings.color_text_block }};
        line-height: 1.3;
        {% if section.settings.text_align == 'center' %}
          text-align: center;
        {% endif %}
      }

      #LT--{{ section.id }} .lt-star-container {
        display: flex;
        gap: 4px;
        {% if section.settings.text_align == 'center' %}
          justify-content: center;
          margin: 0 auto;
        {% endif %}
        width: 100%;
      }
      #LT--{{ section.id }} .lt-star-container svg {
        width: 18px;
        height: auto;
      }
      #LT--{{ section.id }} svg {
        color: {{ section.settings.color_text_block }};
        fill: {{ section.settings.color_text_block }};
        user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
      }
      #LT--{{ section.id }} h2.lt-testimonial-title {
        font-size: {{ section.settings.s_title_font_desktop }}px;
      }
      #LT--{{ section.id }} h3.testimonial-title {
        font-size: {{ section.settings.b_title_font_desktop }}px;
      }
      #LT--{{ section.id }} .testimonial-quote p {
        font-size: {{ section.settings.b_text_font_desktop }}px;
      }
      #LT--{{ section.id }} .testimonial-author p {
        font-size: {{ section.settings.b_name_font_desktop }}px;
      }
      {% if section.settings.shape == 'rounded' %}
        #LT--{{ section.id }} .lt-testimonial-block-slider {
          border-radius: 20px;
          overflow: hidden;
          z-index: 1;
        }
        #LT--{{ section.id }} .lt-testimonial-block-slider .image-item-wrapper-slider,
        #LT--{{ section.id }} .lt-testimonial-block-slider img,
        #LT--{{ section.id }} .lt-testimonial-block-slider video {
          border-radius: 20px;
          overflow: hidden;
          z-index: 1;
        }
      {% endif %}
      {% if section.settings.shape == 'rounded-card' %}
        #LT--{{ section.id }} .lt-testimonial-block-slider {
          border-radius: 20px;
          overflow: hidden;
          z-index: 1;
        }
        #LT--{{ section.id }} .lt-testimonial-block-slider .image-item-wrapper-slider,
        #LT--{{ section.id }} .lt-testimonial-block-slider img,
        #LT--{{ section.id }} .lt-testimonial-block-slider video {
          border-radius: 20px 20px 0 0;
          overflow: hidden;
          z-index: 1;
        }
      {% endif %}

      {% if section.settings.block_count == 1 %}
        #LT--{{ section.id }} .lt-testimonial-block-slider .image-item-wrapper-slider {
          width: 40%;
          margin-left: auto;
          margin-right: auto;
        }
      {% endif %}

      #LT--{{ section.id }} .lt-testimonial-block-slider {
        padding: 20px 20px 30px 20px;
      }
      #LT--{{ section.id }} .luna-swiper-testimonials {
        overflow: hidden;
        position: relative;
      }
      #LT--{{ section.id }} .luna-swiper-testimonials {
        padding: 0 {{ section.settings.padding-lr }}rem;
      }
      #LT--{{ section.id }} .swiper-slide {
        align-self: stretch;
        display: flex;
        height: auto;
      }
      #LT--{{ section.id }} .lt-testimonial-block {
        display: block;
      }
      #LT--{{ section.id }} .lt-testimonial-block img, #LT--{{ section.id }} .lt-testimonial-block video {
        height: auto;
        max-width: 100%;
        width: 100%;
      }
      #LT--{{ section.id }} .slider-btn-next:after, #LT--{{ section.id }} .slider-btn-back:after {
        display:none;
      }
      .luna-swiper-testimonials .swiper-wrapper {
        opacity: 0;
        height: auto !important;
        margin-bottom: 20px;
      }
      .luna-swiper-testimonials:not(.swiper-initialized) .swiper-wrapper .swiper-slide {
        flex: 1 1;
      }
      #LT--{{ section.id }} .swiper-pagination-bullet {
        border: 1px solid {{ section.settings.color_text }};
        opacity: 1;
        background: transparent;
      }
      #LT--{{ section.id }} .swiper-pagination-bullet-active {
        background: {{ section.settings.color_text }};
      }
      #LT--{{ section.id }} .slider-btn-next svg path, .slider-btn-back svg path{
        fill:{{ section.settings.color_text}};
      }
      #LT--{{ section.id }} .none-block {
        display: none;
      }
      #LT--{{ section.id }} .none {
        display: none;
      }
      #LT--{{ section.id }} .dots {
        display: block;
      }
      #LT--{{ section.id }} .buttons,
      #LT--{{ section.id }} .arrowButtons {
        display: block;
      }
      #LT--{{ section.id }} .luna-customer-testimonials .swiper-button-next {
        right: 90px;
      }
      #LT--{{ section.id }} .luna-customer-testimonials .image-item-wrapper {
        {%- if section.settings.media_size != "original" -%}
          aspect-ratio: {{ section.settings.media_size }};
        {%- endif -%}
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
        position: relative;
      }
      {%- if section.settings.media_size != "original" -%}
        #LT--{{ section.id }} .luna-customer-testimonials .image-item-wrapper > *:not(.playerBtn) {
          height: 100%;
          object-fit: cover;
          position: absolute;
          width: 100%;
        }
      {%- endif -%}
      #LT--{{ section.id }} .lt-testimonial-block .image-item-wrapper {
        margin-bottom: 0px;
      }
      #LT--{{ section.id }} .lt-testimonial-block .video-item-wrapper + .image-item-wrapper {
        display: none;
      }
      #LT--{{ section.id }} .playerBtn {
        aspect-ratio: 1;
        background-color: #fff;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        left: 50%;
        max-width: 55px;
        place-content: center;
        position: absolute;
        top: 50%;
        translate: -50% -50%;
        z-index: 2;
      }
      #LT--{{ section.id }} .lt-testimonial-block .video-item-wrapper {
        cursor: pointer;
      }
      #LT--{{ section.id }} .playerBtn svg {
        width: 40%;
        color: black;
      }
      #LT--{{ section.id }} .lt-testimonial-block .video-item-wrapper video,
      #LT--{{ section.id }} .playing .playerBtn.play {
        opacity: 0;
      }
      #LT--{{ section.id }} .lt-testimonial-block .video-item-wrapper * {
        pointer-events: none;
      }
      #LT--{{ section.id }} .lt-testimonial-block .video-item-wrapper.playing video {
        opacity: 1;
      }
      #LT--{{ section.id }} .lt-testimonial-block {
        width: 100%;
      }
      {% if section.settings.navigation != 'none' %}
      #LT--{{ section.id }} .luna-swiper-testimonials {
        padding-bottom: 40px !important;
      }
      {% endif %}
      #LT--{{ section.id }} .slider-btn-next svg path, #LT--{{ section.id }} .slider-btn-back svg path {
        fill: inherit !important;
      }
      #LT--{{ section.id }} .luna-swiper-testimonials .swiper-pagination  .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
      }
      #LT--{{ section.id }} .wrap-width-full {
        width: 100%;
        max-width: 100%;
      }
      #LT--{{ section.id }} .wrap-width-contain {
        max-width: 100%;
        padding-left: 15px;
        padding-right: 15px;
        margin: 0 auto;
      }
      #LT--{{ section.id }} .wrap-width-narrow {
        max-width: 800px;
        margin: 0 auto;
      }
      /* Navigation buttons are now controlled by slider-navigation.css */
      .slider-arrow-{{ section.id }} svg {
        width: 50px;
      }
      .slider-arrow-{{ section.id }} {
        display: flex;
        justify-content: center;
        height: 100%;
        max-height: 30px;
      }
      .luna-wrap {
        position: relative;
      }
      .slider-arrow-{{ section.id }} .luna-bottom-next.arrowButtons,
      .slider-arrow-{{ section.id }} .luna-bottom-next.buttons {
        position: relative !important;
        top: 0 !important;
        left: 0 !important;
        transform: translate(0px, 0px) !important;
        margin-left: 10px;
        margin-top: -10px;
      }
      .slider-arrow-{{ section.id }} .luna-bottom-prev.arrowButtons,
      .slider-arrow-{{ section.id }} .luna-bottom-prev.buttons {
        position: relative !important;
        top: 0 !important;
        right: 0 !important;
        transform: translate(0px, 0px) !important;
        margin-right: 10px;
        margin-top: -10px;
      }
      .custom-slider-next, .custom-slider-prev {
      cursor: pointer;
      }
      #LT--{{ section.id }} .swiper-pagination {
        bottom: -10px;
        z-index: 2;
      }
      #LT--{{ section.id }} .lt-testimonial-info-slider {
        display: flex;
        flex-direction: column;
        justify-content: center;
        {% if section.settings.text_align == 'center' %}
          text-align: center;
        {% endif %}
        padding-top: 20px;
        overflow-wrap: break-word;
      }
      #LT--{{ section.id }} .swiper-pagination {
        bottom: 5px !important;
      }

      {% if section.settings.block_padding == false %}
        #LT--{{ section.id }} .lt-testimonial-block-slider {
          padding: 0;
        }
        #LT--{{ section.id }} .lt-testimonial-info-slider {
          padding: 20px;
        }
      {% endif %}
      @media (max-width: 900px) {
        #LT--{{ section.id }} .luna-customer-testimonials .swiper-button-next svg {
          width: 35px;
        }
        #LT--{{ section.id }} .luna-customer-testimonials .swiper-button-prev svg {
          width: 35px;
        }
        #LT--{{ section.id }} .luna-customer-testimonials .swiper-button-next {
          right: 25px;
        }
        #LT--{{ section.id }} .lt-testimonial-info-slider {
          text-align: center;
        }
        #LT--{{ section.id }} .lt-star-container {
          justify-content: center;
        }
      }
      @media only screen and (max-width: 749px) {
        #LT--{{ section.id }} .luna-swiper-testimonials {
          padding: 0 {{ section.settings.padding-lr-m }}rem;
        }
        #LT--{{ section.id }} .lt-testimonial-block-slider {
          grid-template-columns: 100%;
        }
        #LT--{{ section.id }} .swiper-wrapper {
          height: auto !important;
        }
        /* .swiper-slide {
          height: 1px !important;
        } */
        .swiper-slide-active {
          height: auto !important;
        }
        .star-left {
          justify-content: flex-start;
        }
        .star-center {
          justify-content: center;
        }
        #LT--{{ section.id }} h2.lt-testimonial-title {
          font-size: {{ section.settings.s_title_font_mobile }}px;
        }
        #LT--{{ section.id }} h3.testimonial-title {
          font-size: {{ section.settings.b_title_font_mobile }}px;
        }
        #LT--{{ section.id }} .testimonial-quote p {
          font-size: {{ section.settings.b_text_font_mobile }}px;
        }
        #LT--{{ section.id }} .testimonial-author p {
          font-size: {{ section.settings.b_name_font_mobile }}px;
        }
      }
      /* Custom CSS */
      {{ section.settings.custom_css }}
    {% endstyle %}

    {%- assign blockSizeEven = section.settings.block_count | modulo: 2 -%}

  <!-- This section is designed and built by Luna Templates. Find more at https://lunatemplates.co -->
    <section id="LT--{{ section.id }}" class="LT__section luna-templates__testimonial-slider section-slider {{ section.settings.css_class }}">
      <div class="luna-customer-testimonials">
        <div class="luna-wrap wrap-width-{{ section.settings.container }}">
          {%- if section.settings.title != blank or section.settings.subtitle != blank -%}
            <div class="lt-testimonial-titles">
              {%- if section.settings.title != blank -%}
                <h2 class="lt-testimonial-title">{{ section.settings.title | replace: 'p>', 'span>' }}</h2>
              {%- endif -%}
              {%- if section.settings.subtitle != blank -%}
                <p class="lt-testimonial-subtitle">{{ section.settings.subtitle }}</p>
              {%- endif -%}
            </div>
          {%- endif -%}

          <div class="luna-swiper-testimonials swiper-{{ section.id }}">
            <div class="swiper-wrapper">
              {%- for block in section.blocks -%}
                {% if block.settings.image.src != blank or block.settings.video or block.settings.title != blank or block.settings.text != blank %}
                  <div class="swiper-slide" {{ block.shopify_attributes }}>
                    <div class="lt-testimonial-block-slider">

                      {% if block.settings.video != blank %}
                        {% if block.settings.link_url != blank %}
                          <a href="{{ block.settings.link_url }}" class="media-link">
                        {% endif %}
                          <div class="image-item-wrapper-slider video-item-wrapper" {% if section.settings.media_size != "original" %}style="aspect-ratio: {{ section.settings.media_size }};"{% else %}style=""{% endif %}>
                            <video playsinline autoplay muted loop class="slider-video" poster="{{ block.settings.video.preview_image | image_url: width: 1000 }}">
                              {%- for source in block.settings.video.sources -%}
                                <source src="{{ source.url }}" type="{{ source.mime_type }}">
                              {%- endfor -%}
                            </video>
                          </div>
                        {% if block.settings.link_url != blank %} </a> {% endif %}
                      {% endif %}

                      {% if block.settings.image != blank %}
                        {% if block.settings.link_url != blank %}
                          <a href="{{ block.settings.link_url }}" class="media-link">
                        {% endif %}
                        <div class="image-item-wrapper-slider" {% if section.settings.media_size != "original" %}style="aspect-ratio: {{ section.settings.media_size }};"{% else %}style=""{% endif %}>
                          {{ block.settings.image | image_url: width: 1000 | image_tag: class: 'slider-image', loading: 'lazy' }}
                        </div>
                        {% if block.settings.link_url != blank %} </a> {% endif %}
                      {% endif %}

                      {% if section.settings.show_stars or block.settings.title != blank or block.settings.text != blank or block.settings.name != blank %}
                        <div class="lt-testimonial-info-slider">
                          {% if section.settings.show_stars %}
                            <div class="lt-star-container {% if section.settings.text_align == 'left' %}star-left{% endif %}  {% if section.settings.text_align == 'center' %}star-center{% endif %}">
                              {% for i in (1..5) %}
                                <svg id="lt-star" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 19.021"><path d="M19.856,9l2.361,7.265h7.639l-6.18,4.49,2.361,7.265-6.18-4.49-6.18,4.49,2.361-7.265-6.18-4.49H17.5Z" transform="translate(-9.856 -9)"/></svg>
                              {% endfor %}
                            </div>
                          {% endif %}

                          {% if block.settings.text != blank %}<span class="testimonial-quote-slider">{{ block.settings.text }}</span>{% endif %}
                          {% if block.settings.title != blank %}<h3 class="testimonial-title-slider">{{ block.settings.title | replace: 'p>', 'span>' }}</h3>{% endif %}
                          {% if block.settings.name != blank %}<span class="testimonial-author">{{ block.settings.name }}</span>{% endif %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                {% endif %}
              {%- endfor -%}
            </div>

            {% if section.settings.rotate_blocks %}
              <div class="swiper-pagination none-block {% if section.settings.navigation == 'dots' %}dots{% endif %} {% if section.settings.navigation == 'none' %}none{% endif %}swiper-pagination-{{ section.id }}"></div>
            {% else %}
              <div class="swiper-button-next luna-bottom-next none-block{% if section.settings.navigation == 'arrows1' or section.settings.navigation == 'arrows2' %} buttons{% endif %}{% if section.settings.navigation == 'arrows3' or section.settings.navigation == 'arrows4' %} arrowButtons{% endif %}{% if section.settings.navigation == 'none' %} none{% endif %} slider-btn-next swiper-next-{{ section.id }}">
                <svg xmlns="SVG namespace" width="91.118" height="37.293" viewBox="0 0 91.118 37.293">
                  <path id="Path_1161" data-name="Path 1161" d="M985.472,477.146l-1.409,1.409,16.24,16.24H913v1.994h87.3l-16.24,16.24,1.409,1.41,18.646-18.647Z" transform="translate(-913 -477.146)" />
                </svg>
              </div>
              <div class="swiper-button-prev luna-bottom-prev none-block{% if section.settings.navigation == 'arrows1' or section.settings.navigation == 'arrows2' %} buttons{% endif %}{% if section.settings.navigation == 'arrows3' or section.settings.navigation == 'arrows4' %} arrowButtons{% endif %}{% if section.settings.navigation == 'none' %} none{% endif %} slider-btn-back swiper-prev-{{ section.id }}">
                <svg xmlns="SVG namespace" width="91.118" height="37.293" viewBox="0 0 91.118 37.293">
                  <path id="Path_1162" data-name="Path 1162" d="M931.646,477.146l1.409,1.409-16.24,16.24h87.3v1.994h-87.3l16.24,16.24-1.409,1.41L913,495.793Z" transform="translate(-913 -477.146)" />
                </svg>
              </div>
              <div class="swiper-pagination none-block {% if section.settings.navigation == 'dots' %}dots{% endif %} {% if section.settings.navigation == 'none' %}none{% endif %}swiper-pagination-{{ section.id }}"></div>
            {% endif %}
          </div>

          <div class="slider-arrow-{{ section.id }}">
            <div class=" custom-slider-prev luna-bottom-prev none-block{% if section.settings.navigation == 'arrows1' or section.settings.navigation == 'arrows2' %} buttons{% endif %}{% if section.settings.navigation == 'arrows3' or section.settings.navigation == 'arrows4' %} arrowButtons{% endif %}{% if section.settings.navigation == 'none' %} none{% endif %} slider-btn-back swiper-prev-{{ section.id }}">
              {% if section.settings.navigation == 'arrows1' %}
                <svg xmlns="SVG namespace" width="91.118" height="37.293" viewBox="0 0 91.118 37.293">
                  <path id="arrow-1L" data-name="arrow-1L" d="M931.646,477.146l1.409,1.409-16.24,16.24h87.3v1.994h-87.3l16.24,16.24-1.409,1.41L913,495.793Z" transform="translate(-913 -477.146)" fill="{{ section.settings.color_text_block }}"/>
                </svg>
              {% endif %}
              {% if section.settings.navigation == 'arrows2' %}
                <svg width="95.947" height="42.95" viewBox="0 0 95.947 42.95">
                  <path id="arrow-2L" data-name="arrow-2L" d="M985.472,477.146l-1.409,1.409,16.24,16.24H913v1.994h87.3l-16.24,16.24,1.409,1.41,18.646-18.647Z" transform="translate(1006.947 517.268) rotate(180)" fill="{{ section.settings.color_text_block }}" stroke="{{ section.settings.color_text_block }}" stroke-width="4"/>
                </svg>
              {% endif %}
              {% if section.settings.navigation == 'arrows3' %}
                <svg viewBox="0 0 105 105">
                  <g id="arrow-3L" data-name="arrow-3L" transform="translate(4819 2983) rotate(180)">
                    <circle id="Ellipse_46" data-name="Ellipse 46" cx="52.5" cy="52.5" r="52.5" transform="translate(4714 2878)" fill="transparent"/>
                    <path id="Path_1166" data-name="Path 1166" d="M0,0,24.884,24.884" transform="translate(4756.485 2905.919)" fill="none" stroke="{{ section.settings.color_text_block }}" stroke-linecap="round" stroke-width="4"/>
                    <line id="Line_2" data-name="Line 2" y1="25" x2="25" transform="translate(4756 2931)" fill="none" stroke="{{ section.settings.color_text_block }}" stroke-linecap="round" stroke-width="4"/>
                  </g>
                </svg>
              {% endif %}
              {% if section.settings.navigation == 'arrows4' %}
                <svg viewBox="0 0 105 105">
                  <g id="arrow-4L" data-name="arrow-4L" transform="translate(4819 2983) rotate(180)">
                    <circle id="Ellipse_46" data-name="Ellipse 46" cx="52.5" cy="52.5" r="52.5" transform="translate(4714 2878)" fill="#2A2A2A"/>
                    <path id="Path_1166" data-name="Path 1166" d="M0,0,24.884,24.884" transform="translate(4756.485 2905.919)" fill="none" stroke="#E7E8E5" stroke-linecap="round" stroke-width="4"/>
                    <line id="Line_2" data-name="Line 2" y1="25" x2="25" transform="translate(4756 2931)" fill="none" stroke="#E7E8E5" stroke-linecap="round" stroke-width="4"/>
                  </g>
                </svg>
              {% endif %}
            </div>

            <div class="custom-slider-next luna-bottom-next none-block{% if section.settings.navigation == 'arrows1' or section.settings.navigation == 'arrows2' %} buttons{% endif %}{% if section.settings.navigation == 'arrows3' or section.settings.navigation == 'arrows4' %} arrowButtons{% endif %}{% if section.settings.navigation == 'none' %} none{% endif %} slider-btn-next swiper-next-{{ section.id }}">
              {% if section.settings.navigation == 'arrows1' %}
                <svg xmlns="SVG namespace" width="91.118" height="37.293" viewBox="0 0 91.118 37.293">
                  <path id="arrow-1R" data-name="arrow-1R" d="M985.472,477.146l-1.409,1.409,16.24,16.24H913v1.994h87.3l-16.24,16.24,1.409,1.41,18.646-18.647Z" transform="translate(-913 -477.146)" fill="{{ section.settings.color_text_block }}"/>
                </svg>
              {% endif %}
              {% if section.settings.navigation == 'arrows2' %}
                <svg width="95.947" height="42.95" viewBox="0 0 95.947 42.95">
                  <path id="arrow-2R" data-name="arrow-2R" d="M985.472,477.146l-1.409,1.409,16.24,16.24H913v1.994h87.3l-16.24,16.24,1.409,1.41,18.646-18.647Z" transform="translate(-911 -474.318)" fill="{{ section.settings.color_text_block }}" stroke="{{ section.settings.color_text_block }}" stroke-width="4"/>
                </svg>
              {% endif %}
              {% if section.settings.navigation == 'arrows3' %}
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 105 105">
                  <g id="arrow-3R" data-name="arrow-3R" transform="translate(-4714 -2878)">
                    <circle id="Ellipse_46" data-name="Ellipse 46" cx="52.5" cy="52.5" r="52.5" transform="translate(4714 2878)" fill="transparent" />
                    <path id="Path_1166" data-name="Path 1166" d="M0,0,24.884,24.884" transform="translate(4756.485 2905.919)" fill="none" stroke="{{ section.settings.color_text_block }}" stroke-linecap="round" stroke-width="4"/>
                    <line id="Line_2" data-name="Line 2" y1="25" x2="25" transform="translate(4756 2931)" fill="none" stroke="{{ section.settings.color_text_block }}" stroke-linecap="round" stroke-width="4"/>
                  </g>
                </svg>
              {% endif %}
              {% if section.settings.navigation == 'arrows4' %}
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 105 105">
                  <g id="arrow-4R" data-name="arrow-4R" transform="translate(-4714 -2878)">
                    <circle id="Ellipse_46" data-name="Ellipse 46" cx="52.5" cy="52.5" r="52.5" transform="translate(4714 2878)" fill="#2A2A2A"/>
                    <path id="Path_1166" data-name="Path 1166" d="M0,0,24.884,24.884" transform="translate(4756.485 2905.919)" fill="none" stroke="#E7E8E5" stroke-linecap="round" stroke-width="4"/>
                    <line id="Line_2" data-name="Line 2" y1="25" x2="25" transform="translate(4756 2931)" fill="none" stroke="#E7E8E5" stroke-linecap="round" stroke-width="4"/>
                  </g>
                </svg>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      <div class="luna-marquee-slider-helper" style="display: none;" data-speed="{{ section.settings.speed-slider | times: 1000 }}" data-rotate="{{ section.settings.rotate }}" data-space="{{ section.settings.slide-space }}" data-space-m="{{ section.settings.slide-space }}"></div>
    </section>
  {%- endcapture -%}
  {{ minify | strip_newlines }}

  <script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>








  <script>
    ;(function(){
    var sectionId = "{{ section.id }}";
    const sliderNext = document.querySelector('.swiper-{{ section.id }} .slider-btn-next'),
    sliderCustomNext = document.querySelector('.slider-arrow-{{ section.id }} .custom-slider-next'),
    sliderPrev = document.querySelector('.swiper-{{ section.id }} .swiper-button-prev'),
    sliderCustomPrev = document.querySelector('.slider-arrow-{{ section.id }} .custom-slider-prev')

    // Don't hide the navigation buttons - they're positioned by CSS
    sliderCustomNext.addEventListener('click', function() {
      sliderNext.click()
    })
    sliderCustomPrev.addEventListener('click', function() {
      sliderPrev.click()
    })

      function marqueeLunaSlider(sectId){
        let sectionIdSelector = '#section-'+sectId;
        sliderInfo = document.querySelector('#LT--'+sectId + ' .luna-marquee-slider-helper'),
        sliderSpeed =  +sliderInfo.getAttribute('data-speed'),
        sliderSpace =  +sliderInfo.getAttribute('data-space'),
        sliderSpaceM =  +sliderInfo.getAttribute('data-space-m'),
        sliderRotate =  sliderInfo.getAttribute('data-rotate');

        var Swipes = new Swiper(".swiper-{{ section.id }}", {
          preventClicks: false,
          autoHeight: false, // Force fixed height slides
          calculateHeight: false, // Don't recalculate heights
          autoplay: {
            delay: sliderSpeed,
            disableOnInteraction: true,
          },
          breakpoints: {
            749: {
              slidesPerView: {{ section.settings.block_count }},
              spaceBetween: sliderSpace,
            }
          },
          centeredSlides: {% if blockSizeEven == 1 %}true{% else %}false{% endif %},
          loop: true,
          navigation: {
            nextEl: ' .swiper-button-next',
            prevEl: ' .swiper-button-prev',
          },
          pagination: {
            el: ".swiper-pagination-{{ section.id }} ",
            clickable: true,
          },
          spaceBetween: sliderSpaceM,
          slidesPerView: 1.4, // Show more of adjacent slides on mobile
          effect: 'coverflow', // Use coverflow effect for consistent sizing
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 100,
            modifier: 1.5,
            slideShadows: false,
          },
          on: {
            init: function () {
              document.querySelector('.swiper-{{ section.id }} .swiper-wrapper').style.opacity = 1;
            }
          },
        });
        if(sliderRotate == 'false'){
          Swipes.autoplay.stop();
        }
      }


    marqueeLunaSlider(sectionId);
    document.addEventListener("readystatechange", function() {
      if(document.readyState == "complete") {}
    })
    document.addEventListener("shopify:section:load", function(event) {
      marqueeLunaSlider(sectionId);
    });

    const videoWrappers = document.querySelectorAll("#LT--{{ section.id }} .video-item-wrapper")
    videoWrappers.forEach(videoWrapper => {
      const video = videoWrapper.querySelector("video")
      const videoStatus = (status) => videoWrapper.classList.toggle("playing", status)

      video.addEventListener('play', function() {
        videoStatus(true)
        const checkList = document.querySelectorAll("#LT--{{ section.id }} .video-item-wrapper")
        checkList.forEach(v => {
          if(videoWrapper != v && v.classList.contains("playing")) {
            v.querySelector("video").pause();
          }
        })
      })
      video.addEventListener('pause', function() {
        videoStatus(false)
      })

      // Remove the click event listener from the videoWrapper
      // Instead, add a play/pause toggle to the video element itself
      video.addEventListener('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        if(this.paused) {
          this.play()
        } else {
          this.pause()
        }
      })
    })
  }());
  </script>

  {% schema %}
    {
        "name": "Slider",
        "settings": [
        {
          "type": "header",
          "content": "Section Content"
        },
        {
          "type": "richtext",
          "id": "title",
          "label": "Title",
          "default": "<p>Your Heading</p>"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle"
        },
        {
          "type": "header",
          "content": "Section Color & Design"
        },
        {
          "type": "color",
          "id": "color_text",
          "label": "Font Color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "color_bg",
          "label": "Section Background Color",
          "default": "#fff",
          "info": "Choose either background color or image"
        },
        {
          "type": "image_picker",
          "id": "image_bg",
          "label": "Section Background Image",
          "info": "Choose either background color or image"
        },
        {
          "type": "select",
          "id": "container",
          "label": "Container width",
          "options": [
            {
              "value": "full",
              "label": "Full width"
            },
            {
              "value": "contain",
              "label": "Contained"
            },
            {
              "value": "narrow",
              "label": "Narrow"
            }
          ],
          "default": "contain"
        },
        {
          "type": "header",
          "content": "Block Color & Design"
        },
        {
          "type": "color",
          "id": "color_bg_block",
          "label": "Block Background Color",
          "default": "transparent"
        },
        {
          "type": "color",
          "id": "color_text_block",
          "label": "Block Text & Icon Color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "color_border_block",
          "label": "Block Border Color",
          "default": "#000"
        },
        {
          "type": "range",
          "id": "border_width_block",
          "min": 0,
          "max": 6,
          "step": 1,
          "label": "Block Border Width",
          "default": 0
        },
        {
          "type": "checkbox",
          "id": "border-media",
          "label": "Border around media",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "block_padding",
          "label": "Block Padding",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_stars",
          "label": "Show 5 stars",
          "default": true
        },
        {
          "type": "select",
          "id": "shape",
          "label": "Shape of Edges",
          "options": [
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "rounded-card",
              "label": "Rounded Card"
            }
          ],
          "default": "square"
        },
        {
          "type": "select",
          "id": "text_align",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            }
          ],
          "default": "center",
          "label": "Text Align (Desktop)"
        },
        {
          "type": "select",
          "id": "media_size",
          "options": [
            {
              "value": "original",
              "label": "Original size"
            },
            {
              "value": "1/1",
              "label": "1:1"
            },
            {
              "value": "3/4",
              "label": "3:4"
            },
            {
              "value": "9/16",
              "label": "9:16"
            }
          ],
          "default": "3/4",
          "label": "Media Dimensions"
        },
        {
          "type": "header",
          "content": "Desktop Font Sizes"
        },
        {
          "type": "range",
          "id": "s_title_font_desktop",
          "label": "Section Title Font Size (Desktop)",
          "default": 50,
          "min": 20,
          "max": 160,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "b_title_font_desktop",
          "label": "Block Title Font Size (Desktop)",
          "default": 22,
          "min": 14,
          "max": 60,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "b_text_font_desktop",
          "label": "Block Text Font Size (Desktop)",
          "default": 22,
          "min": 14,
          "max": 60,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "b_name_font_desktop",
          "label": "Block Name Font Size (Desktop)",
          "default": 22,
          "min": 14,
          "max": 60,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "header",
          "content": "Mobile Font Sizes"
        },
        {
          "type": "range",
          "id": "s_title_font_mobile",
          "label": "Section Title Font Size (Mobile)",
          "default": 22,
          "min": 14,
          "max": 60,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "b_title_font_mobile",
          "label": "Block Title Font Size (Mobile)",
          "default": 22,
          "min": 14,
          "max": 60,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "b_text_font_mobile",
          "label": "Block Text Font Size (Mobile)",
          "default": 22,
          "min": 14,
          "max": 60,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "b_name_font_mobile",
          "label": "Block Name Font Size (Mobile)",
          "default": 22,
          "min": 14,
          "max": 60,
          "step": 2,
          "unit": "px"
        },
        {
          "type": "header",
          "content": "Slider Design & Function"
        },
        {
          "type": "range",
          "id": "block_count",
          "min": 1,
          "max": 5,
          "step": 1,
          "label": "Blocks shown (Desktop)",
          "default": 3,
          "info": "Click save to see changes"
        },
        {
          "type": "select",
          "id": "navigation",
          "label": "Navigation",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "dots",
              "label": "Dots"
            },
            {
              "value": "arrows1",
              "label": "Arrows 1"
            },
            {
              "value": "arrows2",
              "label": "Arrows 2"
            },
            {
              "value": "arrows3",
              "label": "Arrows 3"
            },
            {
              "value": "arrows4",
              "label": "Arrows 4"
            }
          ],
          "default": "dots"
        },
        {
          "type": "checkbox",
          "id": "rotate",
          "label": "Rotate automatically",
          "default": false
        },
        {
          "type": "range",
          "id": "speed-slider",
          "min": 2,
          "max": 8,
          "step": 1,
          "label": "Slider speed",
          "default": 4,
          "unit": "s",
          "info": "Applies when 'Rotate automatically' is true."
        },
        {
          "type": "range",
          "id": "padding-lr",
          "min": 0,
          "max": 20,
          "step": 0.2,
          "label": "Show adjacent blocks (Desktop)",
          "default": 0,
          "unit": "rem"
        },
        {
          "type": "range",
          "id": "padding-lr-m",
          "min": 0,
          "max": 10,
          "step": 0.2,
          "label": "Show adjacent blocks (Mobile)",
          "default": 0,
          "unit": "rem"
        },
        {
          "type": "range",
          "id": "slide-space",
          "min": 0,
          "max": 60,
          "step": 1,
          "label": "Space Between Blocks",
          "default": 30,
          "unit": "px"
        },
        {
          "type": "header",
          "content": "Section Spacing"
        },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 140,
          "step": 5,
          "label": "Padding Top",
          "default": 60
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 140,
          "step": 5,
          "label": "Padding Bottom",
          "default": 60
        },
        {
          "type": "header",
          "content": "Advanced"
        },
        {
          "type": "text",
          "id": "css_class",
          "label": "CSS Class"
        },
        {
          "type": "html",
          "id": "custom_css",
          "label": "Custom CSS",
          "info": "Write your CSS here. Use !important when necessary."
        }
        ],
        "blocks": [
        {
          "type": "testimonial",
          "name": "Testimonial",
          "settings": [
            {
              "type": "image_picker",
              "id": "image",
              "label": "Image",
              "info": "When a video is uploaded, this image will not be displayed."
            },
            {
              "type": "video",
              "id": "video",
              "label": "Video",
              "info": "Accepts Shopify-hosted videos. To change the cover image, click edit."
            },
            {
              "type": "richtext",
              "id": "title",
              "label": "Testimonial Title"
            },
            {
              "type": "richtext",
              "id": "text",
              "label": "Testimonial Text",
              "default": "<p>Add an amazing testimonial from your customers.</p>"
            },
            {
              "type": "richtext",
              "id": "name",
              "label": "Name"
            },
            {
              "type": "url",
              "id": "link_url",
              "label": "Link URL",
              "info": "Optional: Add a link to make the image/video clickable."
            }
          ]
        }
        ],
      "presets": [
        {
          "name": "Slider",
          "category": "Luna Templates",
          "blocks": [
            {
              "type": "testimonial"
            },
            {
              "type": "testimonial"
            },
            {
              "type": "testimonial"
            }
          ]
        }
        ]
    }
  {% endschema %}