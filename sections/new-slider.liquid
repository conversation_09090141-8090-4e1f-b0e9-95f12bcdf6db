<link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css" />

<div class="magnesium-section-wrapper">
  <div class="magnesium-heading">
    {% if section.settings.title != blank %}
      <h2>{{ section.settings.title }}</h2>
    {% endif %}
    {% if section.settings.description != blank %}
      <p>{{ section.settings.description }}</p>
    {% endif %}
  </div>

  <div class="magnesium-swiper-container swiper magnesium-swiper">
    <div class="swiper-wrapper">
      {% for block in section.blocks %}
        <div class="swiper-slide magnesium-card">
          {% if block.settings.image != blank %}
            <img src="{{ block.settings.image | image_url: width: 600 }}" alt="{{ block.settings.title }}" class="magnesium-img" />
          {% endif %}
          <div class="magnesium-info">
            <h3>{{ block.settings.title }}</h3>
            <p>{{ block.settings.subtitle }}</p>
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Arrows -->
    <div class="swiper-button-next magnesium-next"></div>
    <div class="swiper-button-prev magnesium-prev"></div>
  </div>
</div>
  <script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>
<style>
.magnesium-section-wrapper {
  padding: 60px 20px;
  background: #000;
  color: #fff;
  text-align: center;
}

.magnesium-heading h2 {
  font-size: 32px;
  margin-bottom: 10px;
  color: #fff;
}

.magnesium-heading p {
  font-size: 16px;
  color: #ccc;
  max-width: 700px;
  margin: 0 auto 40px auto;
}

.magnesium-swiper-container {
  position: relative;
}

.magnesium-card {
  background: #111;
  border-radius: 12px;
  overflow: hidden;
  padding: 20px;
  max-width: 350px;
  margin: auto;
  color: #fff;
  text-align: center;
}

.magnesium-img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: cover;
}

.magnesium-info h3 {
  margin-top: 15px;
  font-size: 18px;
  font-weight: bold;
}

.magnesium-info p {
  font-size: 14px;
  color: #aaa;
}

.swiper-button-next,
.swiper-button-prev {
  color: #fff;
}
</style>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    new Swiper('.magnesium-swiper', {
      loop: true,
      slidesPerView: 2.8,
      spaceBetween: 30,
      navigation: {
        nextEl: '.magnesium-next',
        prevEl: '.magnesium-prev',
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
        1024: {
          slidesPerView: 3,
        }
      }
    });
  });
</script>

{% schema %}
{
  "name": "Magnesium Slider",
  "tag": "section",
  "class": "magnesium-slider-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Explore Our Magnesium Types"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Section Description",
      "default": "Each form of magnesium supports your body differently. Discover which is right for your needs."
    }
  ],
  "blocks": [
    {
      "type": "magnesium",
      "name": "Magnesium Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Magnesium Type"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Effect"
        }
      ]
    }
  ],
  "max_blocks": 10,
  "presets": [
    {
      "name": "Magnesium Slider",
      "category": "Custom",
      "blocks": [
        {
          "type": "magnesium",
          "settings": {
            "title": "MAGNESIUM MALATE",
            "subtitle": "ENERGIZING"
          }
        },
        {
          "type": "magnesium",
          "settings": {
            "title": "MAGNESIUM GLYCINATE",
            "subtitle": "CALMING"
          }
        },
        {
          "type": "magnesium",
          "settings": {
            "title": "MAGNESIUM TAURATE",
            "subtitle": "FOCUSED"
          }
        }
      ]
    }
  ]
}
{% endschema %}
