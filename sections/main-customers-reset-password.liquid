<div class="sm:container">
  <div class="customer-form">
    <div {% render 'surface', class: 'customer-form__box', background_fallback: 'bg-secondary' %}>
      <div class="customer-form__box-inner text-center">
        <div class="v-stack gap-12">
          <h1 class="h2">
            {%- assign content = 'customer.reset_password.title' | t -%}
            {%- render 'styled-text', content: content, gradient: section.settings.heading_gradient -%}
          </h1>

          {%- form 'reset_customer_password', class: 'form' -%}
            <div class="fieldset">
              {%- if form.errors -%}
                {%- assign form_errors = form.errors | default_errors -%}
                {%- render 'banner', status: 'error', content: form_errors -%}
              {%- endif -%}

              {%- assign password_label = 'customer.reset_password.password' | t -%}
              {%- render 'input', type: 'password', name: 'customer[password]', label: password_label, autocomplete: 'new-password', required: true -%}

              {%- assign password_confirmation_label = 'customer.reset_password.password_confirmation' | t -%}
              {%- render 'input', type: 'password', name: 'customer[password_confirmation]', label: password_confirmation_label, autocomplete: 'new-password', required: true -%}
            </div>

            {%- assign submit_label = 'customer.reset_password.submit' | t -%}
            {%- render 'button', content: submit_label, type: 'submit', size: 'xl', stretch: true, secondary: true -%}
          {%- endform -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Customer reset password",
  "class": "shopify-section--main-customers-reset-password",
  "tag": "section",
  "settings": []
}
{% endschema %}