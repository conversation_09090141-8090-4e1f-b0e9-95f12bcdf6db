{%- render 'section-spacing-collapsing' -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
CSS
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<style>
  #shopify-section-{{ section.id }} .product-list {
    --product-list-gap: {% if section.settings.stack_products %}{% if section.settings.products_per_row_mobile == '1' %}var(--grid-gutter){% else %}var(--product-list-row-gap){% endif %} var(--spacing-2){% else %}var(--product-list-row-gap) var(--product-list-column-gap){% endif %};
    --product-list-items-per-row: {{ section.settings.products_per_row_mobile | times: 1 }};
    --product-list-carousel-item-width: 74vw;
    --product-list-grid: {% if section.settings.stack_products %}auto / repeat(var(--product-list-items-per-row), minmax(0, 1fr)){% else %}auto / auto-flow var(--product-list-carousel-item-width){% endif %};
  }

  @media screen and (min-width: 700px) {
    #shopify-section-{{ section.id }} .product-list {
      --product-list-gap: var(--product-list-row-gap) var(--product-list-column-gap);
      --product-list-items-per-row: 2;
      --product-list-carousel-item-width: 36vw;
    }
  }

  @media screen and (min-width: 1000px) {
    #shopify-section-{{ section.id }} .product-list {
      --product-list-items-per-row: {{ section.settings.products_per_row_desktop }};
      --product-list-carousel-item-width: calc(var(--container-inner-width) / {{ section.settings.products_per_row_desktop }} - (var(--product-list-column-gap) / {{ section.settings.products_per_row_desktop }} * {{ section.settings.products_per_row_desktop | minus: 1 }}));
    }
  }
</style>

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
LIQUID
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<div {% render 'section-properties' %}>
  <div class="section-stack">
    {%- render 'section-header', subheading: section.settings.subheading, heading: section.settings.title, heading_color: section.settings.heading_color, heading_gradient: section.settings.heading_gradient, content: section.settings.content -%}

    <div class="{% if section.settings.show_progress_bar %}scrollable-with-controls{% else %}floating-controls-container{% endif %}">
      {%- assign scroll_area_id = 'scroll-area-' | append: section.id -%}

      {%- if section.settings.products.count > 0 -%}
        <scroll-carousel selector="product-card" id="{{ scroll_area_id }}" class="scroll-area bleed {% if section.settings.products.count > section.settings.products_per_row_desktop %}is-scrollable{% endif %}">
          <reveal-items selector=".product-list > *">
            <product-list class="product-list">
              {%- for product in section.settings.products -%}
                {%- render 'product-card', product: product, stacked: section.settings.stack_products, position: forloop.index, background: section.settings.product_card_background, text_color: section.settings.product_card_text_color, show_badges: true -%}
              {%- endfor -%}
            </product-list>
          </reveal-items>
        </scroll-carousel>

        {%- if section.settings.stack_products == false -%}
          {%- if section.settings.show_progress_bar -%}
            {%- assign default_progress = section.settings.products_per_row_desktop | times: 1.0 | divided_by: section.settings.products.count -%}
            {%- render 'scrollbar', observes: scroll_area_id, default_progress: default_progress, show_buttons: true -%}
          {%- else -%}
            <button is="prev-button" class="circle-button circle-button--lg circle-button--fill border group" aria-controls="{{ scroll_area_id }}" disabled>
              <span class="sr-only">{{ 'general.accessibility.previous' | t }}</span>
              <span class="animated-arrow animated-arrow--reverse"></span>
            </button>

            <button is="next-button" class="circle-button circle-button--lg circle-button--fill border group" aria-controls="{{ scroll_area_id }}">
              <span class="sr-only">{{ 'general.accessibility.next' | t }}</span>
              <span class="animated-arrow"></span>
            </button>
          {%- endif -%}
        {%- endif -%}
      {%- else -%}
        <product-recommendations product="{{ product.id }}" limit="{{ section.settings.recommendations_count }}" intent="related" class="contents">
          {%- if recommendations.performed and recommendations.products_count > 0 -%}
            <scroll-carousel selector="product-card" id="{{ scroll_area_id }}" class="scroll-area bleed {% if section.settings.recommendations_count > section.settings.products_per_row_desktop %}is-scrollable{% endif %}">
              <reveal-items selector=".product-list > *">
                <product-list class="product-list">
                  {%- for product in recommendations.products -%}
                    {%- render 'product-card', product: product, stacked: section.settings.stack_products, position: forloop.index, background: section.settings.product_card_background, text_color: section.settings.product_card_text_color, show_badges: true -%}
                  {%- endfor -%}
                </product-list>
              </reveal-items>
            </scroll-carousel>

            {%- if section.settings.stack_products == false -%}
              {%- if section.settings.show_progress_bar -%}
                {%- assign default_progress = section.settings.products_per_row_desktop | times: 1.0 | divided_by: section.settings.recommendations_count -%}
                {%- render 'scrollbar', observes: scroll_area_id, default_progress: default_progress, show_buttons: true -%}
              {%- else -%}
                <button is="prev-button" class="circle-button circle-button--lg circle-button--fill border group" aria-controls="{{ scroll_area_id }}" disabled>
                  <span class="sr-only">{{ 'general.accessibility.previous' | t }}</span>
                  <span class="animated-arrow animated-arrow--reverse"></span>
                </button>

                <button is="next-button" class="circle-button circle-button--lg circle-button--fill border group" aria-controls="{{ scroll_area_id }}">
                  <span class="sr-only">{{ 'general.accessibility.next' | t }}</span>
                  <span class="animated-arrow"></span>
                </button>
              {%- endif -%}
            {%- endif -%}
          {%- endif -%}
        </product-recommendations>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Related products",
  "class": "shopify-section--product-recommendations",
  "tag": "section",
  "enabled_on": {
    "templates": ["product"]
  },
  "settings": [
    {
      "type": "paragraph",
      "content": "Dynamic recommendations change and improve with time. Create manual product recommendations using the Shopify Search & Discovery app. [Learn more](https://help.shopify.com/en/manual/online-store/search-and-discovery/product-recommendations)."
    },
    {
      "type": "range",
      "id": "recommendations_count",
      "min": 2,
      "max": 10,
      "label": "Dynamic recommendations count",
      "default": 6
    },
    {
      "type": "product_list",
      "id": "products",
      "limit": 20,
      "label": "Manual recommendations",
      "info": "Replaces automatic recommendations when selected."
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_products",
      "label": "Stack products",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_progress_bar",
      "label": "Show carousel progress bar",
      "default": false
    },
    {
      "type": "select",
      "id": "products_per_row_mobile",
      "label": "Products per row (mobile)",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "products_per_row_desktop",
      "min": 2,
      "max": 5,
      "label": "Products per row (desktop)",
      "default": 3
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "You may also like"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Heading gradient"
    },
    {
      "type": "color",
      "id": "product_card_background",
      "label": "Product card background"
    },
    {
      "type": "color",
      "id": "product_card_text_color",
      "label": "Product card text"
    }
  ],
  "presets": [
    {
      "name": "Related products"
    }
  ]
}
{% endschema %}