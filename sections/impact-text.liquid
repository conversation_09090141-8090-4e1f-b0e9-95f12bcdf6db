{%- if section.blocks.size > 0 -%}
  {%- render 'section-spacing-collapsing' -%}

  {%- comment -%}
    ------------------------------------------------------------------------------------------------------------------------
    CSS
    ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <style>
    #shopify-section-{{ section.id }} {
      --impact-text-font-size: calc(min(20vw, 65px) * {{ section.settings.impact_text_size_ratio }});
      --impact-text-auto-columns: {% if section.blocks.size == 1 %}minmax(0, 1fr){% else %}{% if section.settings.text_divider != 'none' %}48vw auto{% else %}64vw{% endif %}{% endif %};
    }

    @media screen and (min-width: 1000px) {
      #shopify-section-{{ section.id }} {
        --impact-text-font-size: calc(min(15vw, var(--container-max-width) * 0.15) / {{ section.blocks.size }} * {{ section.settings.impact_text_size_ratio }});
        /* --impact-text-auto-columns: {% if section.settings.text_divider != 'none' %}minmax(0, 1fr) auto{% else %}minmax(0, 1fr){% endif %}; */
      }
    }

    .impact-text {
      display: flex;
      flex-direction: column;
      gap: 0rem;
    }

    .impact-text .snap-center {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 3rem;
    }

    .impact-text .qwe {
      flex-shrink: 0;
    }

    .impact-text__content {
      text-align: left;
    }

    .impact-text .prose {
      margin: 0;
    }


       /* .text-custom {
        display: none; 
       } */
  </style>
  

<script>
  function createAni() {
    let arr = ["71", "80", "75", "95"];
    Array.from(document.querySelectorAll('.impact-text .snap-center')).forEach((parent, i) => {
      if (!parent.firstChild.classList.contains('qwe')) {
        let elem = document.createElement('div');
        elem.className = "qwe";
        elem.style.cssText = `
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 0;
        `;
        elem.innerHTML = `
          <div role="progressbar" 
               aria-valuenow="${arr[i]}" 
               aria-valuemin="0" 
               aria-valuemax="100" 
               style="--value: ${arr[i]}">
          </div>
        `;
        parent.prepend(elem);
        parent.querySelector('.impact-text__text.heading.break-all').style.display = "block";
      }
    });
  }

  document.addEventListener('DOMContentLoaded', () => {

    createAni();

    const progressBars = document.querySelectorAll('[role="progressbar"]');
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const observerCallback = (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-progress');
          observer.unobserve(entry.target);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);
    progressBars.forEach(bar => {
      bar.classList.remove('animate-progress');
      observer.observe(bar);
    });
  });
</script>

<style>
  @keyframes progress {
    0% {
      --percentage: 0;
    }

    100% {
      --percentage: var(--value);
    }
  }

  .animate-progress {
    animation: progress 5s 0.5s forwards;
  }

  @property --percentage {
    syntax: '<number>';
    inherits: true;
    initial-value: 0;
  }

  [role="progressbar"] {
    --percentage: 0;
    --primary: #1a1a1a;
    --secondary: #e7e8e6;
    --size: 120px;
    width: var(--size);
    aspect-ratio: 1;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    display: grid;
    place-items: center;
    border: 5px solid #1a1a1a; 
    background: var(--secondary);
    transition: background 0.5s ease;
  }

  [role="progressbar"]::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(var(--primary) calc(var(--percentage) * 1%), var(--secondary) 0);
    mask: radial-gradient(var(--secondary) 55%, transparent 55%, transparent 75%, var(--secondary) 75%);
    mask-mode: alpha;
    -webkit-mask: radial-gradient(var(--primary) 65%, var(--primary) 65%, var(--primary) 75%, var(--primary) 75%);
    -webkit-mask-mode: alpha;
  }

  [role="progressbar"]::after {
    content: attr(aria-valuenow) '%';
    font-size: calc(var(--size) / 5);
    color: var(--primary);
    position: absolute;
    width: 90%;
    height: 90%;
    background: #e7e8e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid var(--primary);
  }

  .animate-progress::after {
    animation: none;
  }
</style>
  
  <style>
    @media screen and (min-width: 1000px) {
      [role="progressbar"] {
        --size: 180px;
      }
    }
  </style>
  
  {%- comment -%}
    ------------------------------------------------------------------------------------------------------------------------
    LIQUID
    ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <style>
    @media screen and (max-width: 999px) {
      .impact-text-container {
        padding-left: 24px;
        padding-right: 24px;
      }
    }
  </style>
  
  <style>
    @media screen and (min-width: 1000px) {
      .impact-text-container {
        max-width: var( --container-max-width); 
        margin-left: auto;
        margin-right: auto;
        padding-left: 48px;
        padding-right: 48px;
        padding-bottom: 2rem;
      }

    }
  </style>

  <style>
    .impact-text-container {
      text-align: center !important;
    }
    .impact-text {
      display: inline-block;
      vertical-align: middle;
      text-align: left !important;
    }
  </style>
  <!-- New media query for desktop side-by-side layout -->
  <style>
    @media screen and (min-width: 1000px) {
      .impact-text {
        display: flex !important;
        flex-wrap: wrap;
        flex-direction: row;
        justify-content: space-between;
      }
      .impact-text .snap-center {
        width: calc(50% - 1rem);
        margin-bottom: 1.5rem;
      }
    }
  </style>

  <div class="impact-text-container" {% render 'section-properties' %}>
    <div
      {% unless section.settings.stack_mobile %}
        class="scroll-area bleed {% if section.blocks.size < 3 %}sm:unbleed{% else %}md:unbleed{% endif %}"
      {% endunless %}
    >
      <div class="impact-text impact-text--{{ section.settings.text_alignment }} {% unless section.settings.stack_mobile %}impact-text--scroll{% endunless %}">
        {%- for block in section.blocks -%}
          <div class="snap-center" {{ block.shopify_attributes }}>
{%- if block.settings.title != blank -%}
<h2 class="impact-text__text heading break-all"></h2>
{%- endif -%}

            {%- if block.settings.subheading != blank
              or block.settings.content != blank
              or block.settings.button_text != blank
            -%}
              <div class="impact-text__content">
                <div class="prose">
                  {%- if block.settings.subheading != blank -%}
                    <h3 class="h4">{{ block.settings.subheading | escape }}</h3>
                  {%- endif -%}

                  {{- block.settings.content -}}

                  {%- if block.settings.button_text != blank -%}
                    {%- render 'button',
                      content: block.settings.button_text,
                      href: block.settings.button_url,
                      size: 'xl',
                      background: section.settings.button_background,
                      text_color: section.settings.button_text_color
                    -%}
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}
          </div>

          {%- unless forloop.last or section.settings.text_divider == 'none' -%}
            {%- case section.settings.text_divider -%}
              {%- when 'square' -%}
                <span class="shape-square shape--lg place-self-center"></span>
              {%- when 'circle' -%}
                <span class="shape-circle shape--lg place-self-center"></span>
              {%- when 'diamond' -%}
                <span class="shape-diamond shape--lg place-self-center"></span>
              {%- when 'line' -%}
                <span class="shape-line {% if section.settings.stack_mobile %}hidden sm:block{% endif %}"></span>
            {%- endcase -%}
          {%- endunless -%}
        {%- endfor -%}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "Impact text",
  "class": "shopify-section--impact-text",
  "tag": "section",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 3,
  "blocks": [
    {
      "type": "item",
      "name": "Item",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Impact text",
          "info": "For best results, keep this text short and impactful.",
          "default": "100%"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Showcase a benefit of your product"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Button link"
        },
        {
          "type": "header",
          "content": "Number animation"
        },
        {
          "type": "paragraph",
          "content": "Only number can be animated. Dots, commas and spaces can be used as separators."
        },
        {
          "type": "checkbox",
          "id": "animate_impact_text",
          "label": "Show count up animation",
          "default": false
        },
        {
          "type": "range",
          "id": "animate_impact_text_duration",
          "label": "Count up duration",
          "min": 0.5,
          "max": 5,
          "step": 0.1,
          "unit": "s",
          "default": 2
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_mobile",
      "label": "Stack on mobile",
      "default": true
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "impact_text_style",
      "label": "Impact text style",
      "options": [
        {
          "value": "outline",
          "label": "Outline"
        },
        {
          "value": "fill",
          "label": "Fill"
        }
      ],
      "default": "fill"
    },
    {
      "type": "select",
      "id": "text_divider",
      "label": "Multiple texts divider",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "circle",
          "label": "Circle"
        },
        {
          "value": "diamond",
          "label": "Diamond"
        },
        {
          "value": "line",
          "label": "Line"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "impact_text_size_ratio",
      "label": "Impact text size ratio",
      "min": 0.5,
      "max": 1.5,
      "step": 0.1,
      "default": 1
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set. Gradient text outline and gradient background cannot be combined."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "heading_text_color",
      "label": "Impact text"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Impact text gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    }
  ],
  "presets": [
    {
      "name": "Impact text",
      "blocks": [
        {
          "type": "item"
        }
      ]
    }
  ]
}
{% endschema %}
