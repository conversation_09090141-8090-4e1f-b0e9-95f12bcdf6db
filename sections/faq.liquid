{%- render 'section-spacing-collapsing' -%}

{%- assign text_position = section.settings.text_position -%}

<style>
  #shopify-section-{{ section.id }} {
    --section-stack-intro: {% if text_position == 'center' %}60%{% else %}50%{% endif %};
    --section-stack-main: {% if text_position == 'center' %}60%{% else %}50%{% endif %};
  }
</style>

<div {% render 'section-properties' %}>
  <div class="faq-main-section section-stack {% if text_position != 'center' %}section-stack--horizontal{% else %}section-stack--center{% endif %} {% if text_position == 'end' %}section-stack--reverse{% endif %}">
    {%- capture content -%}
      {%- if section.settings.subheading != blank -%}
        <p class="subheading">{{ section.settings.subheading | escape }}</p>
      {%- endif -%}

      {%- if section.settings.title != blank -%}
        <h2 class="h2">
          {%- render 'styled-text', content: section.settings.title, text_color: section.settings.heading_color, gradient: section.settings.heading_gradient -%}
        </h2>
      {%- endif -%}

      {{- section.settings.content -}}
    {%- endcapture -%}

    {%- capture subcontent -%}
      <div class="faq-availability">
        {%- if section.settings.team_avatar != blank or section.settings.support_hours != blank or section.settings.answer_time != blank -%}
          <div class="v-stack gap-4">
            {%- if section.settings.team_avatar != blank -%}
              {%- capture sizes -%}{{ section.settings.team_avatar_width }}px{%- endcapture -%}
              {%- capture widths -%}{{ section.settings.team_avatar_width }}, {{ section.settings.team_avatar_width | times: 2 | at_most: section.settings.team_avatar.width }}{%- endcapture -%}
              {%- capture style -%}max-width: {{ section.settings.team_avatar_width }}px{%- endcapture -%}
              {{- section.settings.team_avatar | image_url: width: section.settings.team_avatar.width | image_tag: style: style, sizes: sizes, widths: widths -}}
            {%- endif -%}

            {%- if section.settings.support_hours != blank or section.settings.answer_time != blank -%}
              <div class="v-stack">
                {{- section.settings.support_hours -}}

                {%- if section.settings.answer_time != blank -%}
                  <span class="text-subdued">{{ section.settings.answer_time }}</span>
                {%- endif -%}
              </div>
            {%- endif -%}
          </div>
        {%- endif -%}

        {%- if section.settings.button_text != blank -%}
          {% render 'button', content: section.settings.button_text, href: section.settings.button_url, size: 'xl', background: section.settings.button_background, text_color: section.settings.button_text_color %}
        {%- endif -%}
      </div>
    {%- endcapture -%}

    {%- if content != blank or subcontent != blank -%}
      <div class="section-stack__intro">
        <div class="v-stack gap-10">
          {%- if content != blank -%}
            <div class="prose {% if text_position == 'center' %}text-center{% endif %}">
              {{- content -}}
            </div>
          {%- endif -%}

          {%- if text_position != 'center' and subcontent != blank -%}
            <div class="hidden lg:block">
              {{- subcontent -}}
            </div>
          {%- endif -%}
        </div>
      </div>
    {%- endif -%}

    {%- if section.blocks.size > 0 -%}
      <div class="section-stack__main" >
        <div {% render 'surface', class: 'accordion-box rounded', background: section.settings.accordion_background, text_color: section.settings.accordion_text_color, background_fallback: 'bg-secondary' %}>
          {%- for block in section.blocks -%}
            {%- if block.settings.title != blank and block.settings.content != blank -%}
              {%- capture accordion_content -%}
                <div class="prose">{{ block.settings.content }}</div>
              {%- endcapture -%}

              {%- render 'accordion', title: block.settings.title, content: accordion_content, block: block -%}
            {%- endif -%}
          {%- endfor -%}
        </div>
      </div>
    {%- endif -%}


  </div>
</div>

{%- comment -%}On the FAQ page, we also output the content with JSON microdata for SEO{%- endcomment -%}

<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {%- for block in section.blocks -%}
        {
          "@type": "Question",
          "name": {{ block.settings.title | json }},
          "acceptedAnswer": {
            "@type": "Answer",
            "text": {{ block.settings.content | json }}
          }
        }{% unless forloop.last %},{% endunless %}
      {%- endfor -%}
    ]
  }
</script>


<style>
.circle-chevron {
  background: transparent;
}
details .progress-bottom-link-minus {
  display: none;
}

details[open] .progress-bottom-link-plus {
  display: none;
}

details[open] .progress-bottom-link-minus {
  display: inline;
}
span.text-custom {
    font-size: 62px;
    line-height: 100%;
}
 @media only screen and (min-width: 769px) {
   .faq-main-section {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
} 
.faq-main-section .section-stack__intro {
    width: 40%;
}
.faq-main-section .section-stack__main {
    width: 60%;
}
.faq-main-section .prose.text-center {
    text-align: left;
}
 }  
@media only screen and (min-width: 481px) and (max-width: 768px) {

.prose.text-center h2.h2 {
     text-align: left;
   }
   .prose.text-center h2.h2 span {
    font-size: 45px !important;
   }
  .accordion-box {
    padding: 0;
  }
}
 @media only screen and (max-width: 480px) {
   .prose.text-center h2.h2 {
     text-align: left;
   }
   .prose.text-center h2.h2 span {
    font-size: 30px !important;
   }
    .accordion-box {
    padding: 0;
  }
 }
</style>



{% schema %}
{
  "name": "FAQ",
  "class": "shopify-section--faq",
  "tag": "section",
  "disabled_on": {
    "groups": ["header"]
  },
  "blocks": [
    {
      "name": "Item",
      "type": "item",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Question",
          "default": "Question"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Answer",
          "default": "<p>Write content to answer to common questions your customers may have about your products, shipping policies..</p>"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "paragraph",
      "content": "Structured data is automatically created on FAQ page to improve SEO. [Learn more](https://developers.google.com/search/docs/advanced/structured-data/faqpage)"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "FAQ"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content",
      "default": "<p>Use this text to share information about your product or shipping policies.</p>"
    },
    {
      "type": "image_picker",
      "id": "team_avatar",
      "label": "Team avatar",
      "info": "700 x 160px .jpg recommended"
    },
    {
      "type": "range",
      "id": "team_avatar_width",
      "min": 50,
      "max": 350,
      "step": 10,
      "unit": "px",
      "label": "Team avatar width",
      "default": 160
    },
    {
      "type": "text",
      "id": "support_hours",
      "label": "Support operating hours"
    },
    {
      "type": "text",
      "id": "answer_time",
      "label": "Average answer time"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Heading gradient"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text"
    },
    {
      "type": "color",
      "id": "accordion_background",
      "label": "Accordion background"
    },
    {
      "type": "color",
      "id": "accordion_text_color",
      "label": "Accordion text"
    }
  ],
  "presets": [
    {
      "name": "FAQ",
      "blocks": [
        {
          "type": "item",
          "settings": {
            "title": "Do you ship overseas?",
            "content": "<p>Yes, we ship all over the world. Shipping costs will apply, and will be added at checkout. We run discounts and promotions all year, so stay tuned for exclusive deals.</p>"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "How long will it take to get my orders?",
            "content": "<p>It depends on where you are. Orders processed here will take 5-7 business days to arrive. Overseas deliveries can take anywhere from 7-16 days. Delivery details will be provided in your confirmation email.</p>"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Any question?",
            "content": "<p>You can contact us through our contact page! We will be happy to assist you.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}
