{%- capture minify -%}
    {%- style -%}
      #LT--{{ section.id }} {
        background: {{ section.settings.color_bg }};
        {%- if section.settings.image_bg != blank -%}
          background-image: url("{{ section.settings.image_bg | image_url: width: 2500 }}");
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
        {%- endif -%}
        border-top: {{ section.settings.border_width }}px solid {{ section.settings.color_border }};
        border-bottom: {{ section.settings.border_width }}px solid {{ section.settings.color_border }};
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
        margin-top: {{ section.settings.margin_top }}px;
        margin-bottom: {{ section.settings.margin_bottom }}px;
        position: relative;
        overflow: hidden;
        width: 100%;
        cursor: default;
      }
      #LT--{{ section.id }} .path-container .scroll-shift {
        align-items: center;
        display: flex;
        gap: 1em;
        position: relative;
      }
      #LT--{{ section.id }} .path-container .scroll-shift > * {
        flex: 1 1 auto;
        line-height: 1;
        white-space: nowrap;
      }
      #LT--{{ section.id }} p {
        display: inline-block;
        margin: 0;
      }
      #LT--{{ section.id }} path {
        fill: currentColor;
        transform-origin: center;
      }
      #LT--{{ section.id }} svg {
        vertical-align: middle;
        overflow: visible;
      }
      #LT--{{ section.id }} a {
        text-decoration: none;
      }
      #LT--{{ section.id }} .shapeWrapper {
        position: relative;
        will-change: transform !important;
      }
      #LT--{{ section.id }} .shapeText {
        display: flex;
        height: calc(100% - 1em);
        justify-content: center;
        left: 50%;
        line-height: 1;
        place-items: center;
        position: absolute;
        text-align: center;
        top: 50%;
        translate: -50% -50%;
        width: calc(100% - 1em);
        white-space: initial;
        will-change: transform !important;
      }
      {%- for block in section.blocks -%}
        {%- if block.type == "text" -%}
          #block-{{ block.id }}.luna-templates__scroll-content-text, #block-{{ block.id }}.luna-templates__scroll-content-text p {
            font-size: {{ block.settings.text_size }}px;
          }
        {%- endif -%}
        {%- if block.type == "image" -%}
          #block-{{ block.id }}.luna-templates__scroll-content-image img {
            box-shadow: 0 0 0 {{ block.settings.border_width }}px {{ block.settings.color_border }};
            display: block;
            height: auto;
            width: {{ block.settings.image_width }}px;
            max-width: none;
          }
        {%- endif -%}
        {%- if block.type == "shape" -%}
          #block-{{ block.id }}.luna-templates__scroll-content-shape svg {
            width: {{ block.settings.shape_width }}px;
            height: auto;
          }
          #block-{{ block.id }}.luna-templates__scroll-content-shape .shapeText {
            color: {{ block.settings.color }};
            font-size: {{ block.settings.text_size }}px;
          }
        {%- endif -%}
      {%- endfor -%}
      .LT-moving-text-container {
        overflow: visible;
      }
      @media screen and (max-width: 749px) {
        #LT--{{ section.id }} {
          padding-top: {{ section.settings.padding_top | divided_by: 2 }}px;
          padding-bottom: {{ section.settings.padding_bottom | divided_by: 2 }}px;
        }
    
        {%- for block in section.blocks -%}
          {%- if block.type == "text" -%}
            #block-{{ block.id }}.luna-templates__scroll-content-text, #block-{{ block.id }}.luna-templates__scroll-content-text p {
              font-size: {{ block.settings.text_size_mobile }}px;
            }
          {%- endif -%}
          {%- if block.type == "image" -%}
            #block-{{ block.id }}.luna-templates__scroll-content-image img {
              width: {{ block.settings.image_width_mobile }}px;
            }
            #block-{{ block.id }}.luna-templates__scroll-content-image {
              grid-template-columns: {% if block.settings.image != blank %}{{ block.settings.image_width_mobile }}px {% endif %}auto;
            }
          {%- endif -%}
          {%- if block.type == "shape" -%}
            #block-{{ block.id }}.luna-templates__scroll-content-shape svg {
              width: {{ block.settings.shape_width_mobile }}px;
            }
            #block-{{ block.id }}.luna-templates__scroll-content-shape .shapeText {
              font-size: {{ block.settings.text_size_mobile }}px;
            }
          {%- endif -%}
        {%- endfor -%}
      }
      {{ section.settings.custom_css }}
    {%- endstyle -%}
    
    <!-- This section is designed and built by Luna Templates. Find more at https://lunatemplates.co -->
    
    <section id="LT--{{ section.id }}" class="LT__section luna-templates__text-on-scroll {{ section.settings.css_class }}">
      <div class="path-container">
        <div class="scroll-shift{% if section.settings.reverse_direction %} scroll-reverse_direction{% endif %}">
          {% for i in (1..10) %}
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when 'text' -%}
                  {%- if block.settings.text != blank -%}
                    <div id="block-{{ block.id }}" class="luna-templates__scroll-content-text" {{ block.shopify_attributes }}>
                      {%- if block.settings.link_url != blank -%}<a href="{{ block.settings.link_url }}" {%- if block.settings.external_link -%}target="_blank"{%- endif -%}>{%- endif -%}
                        <span style="color: {{ block.settings.color }}">{{ block.settings.text }}</span>
                      {%- if block.settings.link_url != blank -%}</a>{%- endif -%}
                    </div>
                  {%- endif -%}
                {%- when 'image' -%}
                  {%- if block.settings.image != blank -%}
                    <div id="block-{{ block.id }}" class="luna-templates__scroll-content-image" {{ block.shopify_attributes }}>
                      {%- if block.settings.link_url != blank -%}<a href="{{ block.settings.link_url }}" {%- if block.settings.external_link -%}target="_blank"{%- endif -%}>{%- endif -%}
                      {{ block.settings.image | image_url: width: 500 | image_tag }}
                      {%- if block.settings.link_url != blank -%}</a>{%- endif -%}
                    </div>
                  {%- endif -%}
                {%- when 'shape' -%}
                  <div id="block-{{ block.id }}" class="luna-templates__scroll-content-shape" {{ block.shopify_attributes }}>
                    {%- if block.settings.link_url != blank -%}<a href="{{ block.settings.link_url }}" {%- if block.settings.external_link -%}target="_blank"{%- endif -%}>{%- endif -%}
                    {%- liquid
                      capture sticker_path
                        case block.settings.shape
                          when 'circle'
                            echo 'M0,50c0,27.6,22.4,50,50,50s50-22.4,50-50 S77.6,0,50,0S0,22.4,0,50'
                          when 'star'
                            echo 'M50,5l7.9-5l6,7.2L73,4.9l3.5,8.7l9.3,0.6l0.6,9.3 l8.7,3.5l-2.3,9.1l7.2,6L95,50l5,7.9l-7.2,6l2.3,9.1l-8.7,3.5l-0.6,9.4l-9.3,0.6L73,95.1l-9.1-2.3l-6,7.2L50,95l-7.9,5l-6-7.2 L27,95.1l-3.5-8.7l-9.3-0.6l-0.6-9.4L4.9,73l2.3-9.1l-7.2-6L5,50l-5-7.9l7.2-6L4.9,27l8.7-3.5l0.6-9.3l9.3-0.6L27,4.9l9.1,2.3l6-7.2 L50,5z'
                          when 'scalloped_circle'
                            echo 'M100,50c0-3.7-1.3-7.2-3.7-10 c-2.4-2.8-5.6-4.7-9.2-5.3c2.1-3,3.1-6.7,2.7-10.3s-1.9-7.1-4.5-9.7c-2.6-2.6-6-4.2-9.7-4.5c-3.7-0.3-7.3,0.7-10.3,2.7 C64.7,9.3,62.8,6,60,3.6C57.2,1.3,53.7,0,50,0c-3.7,0-7.2,1.3-10,3.6c-2.8,2.4-4.7,5.6-5.3,9.2c-3-2.1-6.7-3.1-10.3-2.7 c-3.7,0.3-7.1,1.9-9.7,4.5c-2.6,2.6-4.2,6-4.5,9.7c-0.3,3.7,0.7,7.3,2.7,10.3C9.3,35.3,6,37.2,3.6,40C1.3,42.8,0,46.3,0,50 c0,3.7,1.3,7.2,3.6,10c2.4,2.8,5.6,4.7,9.2,5.3c-2.1,3-3.1,6.7-2.7,10.3c0.3,3.7,1.9,7.1,4.5,9.7c2.6,2.6,6,4.2,9.7,4.5c3.7,0.3,7.3-0.7,10.3-2.7c0.6,3.6,2.5,6.9,5.3,9.2c2.8,2.4,6.4,3.6,10,3.6c3.7,0,7.2-1.3,10-3.6c2.8-2.4,4.7-5.6,5.3-9.2 c3,2.1,6.7,3.1,10.3,2.7c3.7-0.3,7.1-1.9,9.7-4.5c2.6-2.6,4.2-6,4.5-9.7c0.3-3.7-0.7-7.3-2.7-10.3c3.6-0.6,6.9-2.5,9.2-5.3C98.7,57.2,100,53.7,100,50z'  
                          when 'flower'
                            echo 'm99.9,41.51c-1.04-5.72-10.41-8.79-20.96-6.84-.82.14-1.68.32-2.59.55.63-.68,1.23-1.36,1.77-1.99,6.82-8.2,8.78-17.85,4.27-21.57-4.45-3.72-13.64-.04-20.45,8.16-.55.63-1.09,1.32-1.64,2.08.04-.91.09-1.81.09-2.63C60.31,8.61,55.58-.04,49.76,0c-5.82.04-10.45,8.7-10.41,19.4,0,.82.04,1.73.09,2.63-.55-.73-1.09-1.45-1.63-2.08-6.9-8.16-16.14-11.69-20.55-7.98-4.41,3.76-2.41,13.37,4.5,21.52.55.63,1.14,1.27,1.77,1.95-.91-.23-1.77-.41-2.59-.55-10.56-1.81-19.92,1.37-20.87,7.07-.96,5.72,6.78,11.78,17.31,13.6.82.14,1.68.27,2.63.36-.82.41-1.63.86-2.32,1.27-9.23,5.39-14.37,13.78-11.45,18.81,2.9,5.03,12.78,4.66,22.05-.68.73-.41,1.45-.91,2.23-1.4-.37.86-.68,1.67-.96,2.44-3.59,10.06-2.09,19.76,3.37,21.7,5.45,1.95,12.82-4.62,16.45-14.64.27-.77.55-1.63.82-**********.55,1.73.82,2.49,3.73,10.02,11.14,16.49,16.59,14.5s6.86-11.74,3.14-21.76c-.27-.77-.63-1.59-1-**********,1.55.95,2.27,1.36,9.31,5.3,19.19,5.48,22.05.45s-2.32-13.41-11.64-18.67c-.73-.41-1.49-.81-2.32-1.22.91-.09,1.82-.23,2.64-.41,10.45-1.85,18.14-8.02,17.14-13.74v.02Z'
                          when 'ellipse'
                            echo 'M0,50c0,17.4,22.4,31.6,50,31.6 s50-14.1,50-31.6S77.6,18.4,50,18.4S0,32.6,0,50'
                          when 'semicircle'
                            echo 'M100,75c0-13.3-5.3-26-14.6-35.4C76,30.3,63.3,25,50,25 c-13.3,0-26,5.3-35.4,14.6C5.3,49,0,61.7,0,75h50H100z'
                          when 'wave_ribbon'
                            echo 'M100,21.1c-2.1-0.1-4.1,1.1-5.8,3.3 c-1.4,1.8-4.1,1.8-5.5,0c-1.6-2-3.6-3.3-5.6-3.3s-3.9,1.2-5.5,3.3c-1.4,1.8-4.1,1.8-5.5,0c-1.6-2-3.6-3.3-5.5-3.3s-3.9,1.2-5.5,3.3 c-1.4,1.8-4.1,1.8-5.5,0c-1.6-2-3.5-3.3-5.5-3.3s-3.9,1.2-5.5,3.3c-1.4,1.8-4.1,1.8-5.5,0c-1.6-2-3.6-3.3-5.5-3.3s-3.9,1.2-5.5,3.3 c-1.4,1.8-4.1,1.8-5.5,0c-1.6-2-3.5-3.3-5.5-3.3s-3.9,1.2-5.5,3.3c-1.4,1.8-4.1,1.8-5.5,0c-1.6-2-3.6-3.3-5.5-3.3v57.9 c2,0,3.9-1.2,5.5-3.3c1.4-1.8,4.1-1.8,5.5,0c1.6,2,3.6,3.3,5.5,3.3s3.9-1.2,5.5-3.3c1.4-1.8,4.1-1.8,5.5,0c1.6,2,3.6,3.3,5.5,3.3 s3.9-1.2,5.5-3.3c1.4-1.8,4.1-1.8,5.5,0c1.6,2,3.6,3.3,5.5,3.3s3.9-1.2,5.5-3.3c1.4-1.8,4.1-1.8,5.5,0c1.6,2,3.6,3.3,5.5,3.3 s3.9-1.2,5.5-3.3c1.4-1.8,4.1-1.8,5.5,0c1.6,2,3.6,3.3,5.5,3.3s3.9-1.2,5.6-3.3c1.4-1.8,4.1-1.8,5.5,0c1.7,2.2,3.7,3.3,5.8,3.3 L100,21.1L100,21.1z'
                        endcase
                      endcapture
                    -%}
                    <div class="shapeWrapper">
                      <svg width="100" viewBox="0 0 100 100" style="color: {{ block.settings.shape_color }}">
                        <path dy="50%" data-rotate="{{ block.settings.rotate_on_scroll }}" class="svgPath" id="outline-{{ block.id }}" vector-effect="non-scaling-stroke" d="{{ sticker_path }}"></path>
                      </svg>
                      <div class="shapeText" data-rotate="{{ block.settings.rotate_text_on_scroll }}">{{ block.settings.text }}</div>
                    </div>
                    {%- if block.settings.link_url != blank -%}</a>{%- endif -%}
                  </div>
              {%- endcase -%}
            {%- endfor -%}
          {% endfor %}
        </div>
      </div>
    </section>
    {%- endcapture -%}
    {{ minify | strip_newlines }}
    
    {% schema %}
      {
        "name": "💫 Move on Scroll Banner",
        "settings": [
          {
            "type": "header",
            "content": "Background"
          },
          {
            "type": "color",
            "id": "color_bg",
              "label": "Background Color",
              "default": "#BED9BF"
          },
          {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background Image",
            "info": "Choose either background color or image"
          },
          {
            "type": "color",
            "id": "color_border",
              "label": "Border Color",
              "default": "#000"
          },
          {
            "type": "range",
            "id": "border_width",
            "min": 0,
            "max": 6,
            "step": 1,
            "label": "Border Width",
            "default": 0
          },
          {
            "type": "header",
            "content": "Movement"
          },
          {
            "type": "checkbox",
            "id": "reverse_direction",
            "label": "Reverse Direction"
          },
          {
            "type": "range",
            "id": "scroll_speed_desktop",
            "min": 10,
            "max": 100,
            "step": 5,
            "label": "Scroll Speed (Desktop)",
            "default": 40,
            "unit": "%"
          },
          {
            "type": "range",
            "id": "scroll_speed_mobile",
            "min": 10,
            "max": 100,
            "step": 5,
            "label": "Scroll Speed (Mobile)",
            "default": 20,
            "unit": "%"
          },
          {
            "type": "header",
            "content": "Section Spacing"
          },
          {
            "type": "range",
            "id": "padding_top",
            "min": 0,
            "max": 140,
            "step": 5,
            "label": "Padding Top",
            "default": 30
          },
          {
            "type": "range",
            "id": "padding_bottom",
            "min": 0,
            "max": 140,
            "step": 5,
            "label": "Padding Bottom",
            "default": 30
          },
          {
            "type": "range",
            "id": "margin_top",
            "min": 0,
            "max": 140,
            "step": 5,
            "label": "Margin Top",
            "default": 20
          },
          {
            "type": "range",
            "id": "margin_bottom",
            "min": 0,
            "max": 140,
            "step": 5,
            "label": "Margin Bottom",
            "default": 20
          },    
          {
            "type": "header",
            "content": "Advanced"
          },
          {
            "type": "text",
            "id": "css_class",
            "label": "CSS Class"
          },
          {
            "type": "html",
            "id": "custom_css",
            "label": "Custom CSS",
            "info": "Write your CSS here. Use !important when necessary."
          }
        ],
        "blocks": [
          {
            "type": "text",
            "name": "Text",
            "settings": [
              {
                "type": "richtext",
                "id": "text",
                "label": "Text",
                "default": "<p>Some Text</p>"
              },
              {
                "type": "url",
                "id": "link_url",
                "label": "Link"
              },
              {
                "type": "checkbox",
                "id": "external_link",
                "label": "Open this link in a new tab"
              },
              {
                "type": "header",
                "content": "style"
              },
              {
                "type": "range",
                "id": "text_size",
                "label": "Text Size (Desktop)",
                "default": 50,
                "min": 20,
                "max": 250,
                "step": 5,
                "unit": "px"
              },
              {
                "type": "range",
                "id": "text_size_mobile",
                "label": "Text Size (Mobile)",
                "default": 20,
                "min": 20,
                "max": 200,
                "step": 5,
                "unit": "px"
              },
              {
                "type": "color",
                "id": "color",
                "label": "Text Color",
                "default": "#000"
              }
            ]
          },
          {
            "type": "image",
            "name": "Image",
            "settings": [
              {
                "type": "image_picker",
                "id": "image",
                "label": "Image"
              },
              {
                "type": "url",
                "id": "link_url",
                "label": "Link"
              },
              {
                "type": "range",
                "id": "image_width",
                "label": "Image Width (Desktop)",
                "min": 50,
                "max": 500,
                "step": 5,
                "unit": "px",
                "default": 100
              },
              {
                "type": "range",
                "id": "image_width_mobile",
                "label": "Image Width (Mobile)",
                "min": 20,
                "max": 300,
                "step": 5,
                "unit": "px",
                "default": 80
              },
              {
                "type": "range",
                "id": "border_width",
                "min": 0,
                "max": 6,
                "step": 1,
                "label": "Border Width (px)",
                "default": 0
              },
              {
                "type": "color",
                "id": "color_border",
                  "label": "Border Color",
                  "default": "#000"
              }
            ]
          },
          {
            "type": "shape",
            "name": "Shape with Text",
            "settings": [
              {
                "type": "header",
                "content": "Content"
              },
              {
                "type": "select",
                "id": "shape",
                "options": [
                  {
                    "value": "circle",
                    "label": "Circle"
                  },
                  {
                    "value": "star",
                    "label": "Star"
                  },
                  {
                    "value": "scalloped_circle",
                    "label": "Scalloped Circle"
                  },
                  {
                    "value": "flower",
                    "label": "Flower"
                  },
                  {
                    "value": "ellipse",
                    "label": "Ellipse"
                  },
                  {
                    "value": "semicircle",
                    "label": "Semicircle"
                  },
                  {
                    "value": "wave_ribbon",
                    "label": "Wave Ribbon"
                  }
                ],
                "default": "star",
                "label": "Shape"
              },
              {
                "type": "text",
                "id": "text",
                "label": "Text",
                "default": "CTA"
              },
              {
                "type": "url",
                "id": "link_url",
                "label": "Link"
              },
              {
                "type": "header",
                "content": "Style"
              },
              {
                "default": true,
                "type": "checkbox",
                "id": "rotate_on_scroll",
                "label": "Rotate on scroll"
              },
              {
                "default": false,
                "type": "checkbox",
                "id": "rotate_text_on_scroll",
                "label": "Rotate text with shape"
              },
              {
                "type": "color",
                "id": "shape_color",
                "label": "Shape Color",
                "default": "#F8FCA2"
              },
              {
                "type": "color",
                "id": "color",
                "label": "Text Color",
                "default": "#000"
              },
              {
                "type": "range",
                "id": "shape_width",
                "label": "Shape Width (Desktop)",
                "min": 50,
                "max": 300,
                "step": 5,
                "unit": "px",
                "default": 100
              },
              {
                "type": "range",
                "id": "shape_width_mobile",
                "label": "Shape Width (Mobile)",
                "min": 20,
                "max": 200,
                "step": 5,
                "unit": "px",
                "default": 60
              },
              {
                "type": "range",
                "id": "text_size",
                "label": "Text Size (Desktop)",
                "default": 30,
                "min": 10,
                "max": 100,
                "step": 2,
                "unit": "px"
              },
              {
                "type": "range",
                "id": "text_size_mobile",
                "label": "Text Size (Mobile)",
                "default": 16,
                "min": 10,
                "max": 60,
                "step": 2,
                "unit": "px"
              }
            ]
          }
        ],
        "presets": [
          {
            "name": "💫 Move on Scroll Banner",
            "blocks": [
              {
                "type": "text"
              },
              {
                "type": "shape"
              }
            ]
          }
        ]
      }
    {% endschema %}

    <script type="module">
      import { scroll, animate } from 'vendor';

      document.addEventListener('DOMContentLoaded', () => {
        const shifts = document.querySelectorAll('.scroll-shift');
        
        shifts.forEach((shiftEl) => {
          if (window.matchMedia("(prefers-reduced-motion: no-preference)").matches) {
            const isMobile = window.matchMedia("(max-width: 699px)").matches;
            const distance = isMobile ? "{{ section.settings.scroll_speed_mobile }}%" : "{{ section.settings.scroll_speed_desktop }}%";
            
            scroll(
              animate(shiftEl, {
                transform: [
                  "translateX(0)",
                  `translateX(calc(var(--transform-logical-flip) * -${distance}))`
                ]
              }),
              {
                target: shiftEl,
                offset: ["start end", "end start"]
              }
            );
          }
        });
      });
    </script>
