{%- if section.blocks.size > 0 -%}
  {%- render 'section-spacing-collapsing' -%}

  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  CSS
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <style>
    #shopify-section-{{ section.id }} {
      --press-items-border: {{ section.settings.logo_item_border.rgb }} / {{ section.settings.logo_item_border.alpha }};
      --press-max-width: {% if section.settings.content_size == 'small' %}760{% elsif section.settings.content_size == 'medium' %}1000{% elsif section.settings.content_size == 'large' %}1260{% endif %}px;
    }
  </style>

  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  LIQUID
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <div {% render 'section-properties' %}>
    <div class="section-stack">
      <div class="press">
        <press-carousel id="carousel-{{ section.id }}" class="press__list full-bleed text-center sm:unbleed" role="region">
          {%- for block in section.blocks -%}
            <div id="block-{{ section.id }}-{{ block.id }}" class="press__list-item snap-center {% if forloop.first %}is-selected{% endif %}" role="group" aria-label="{{ 'general.accessibility.item_nth_of_count' | t: index: forloop.index, count: section.blocks.size }}" {{ block.shopify_attributes }}>
              {%- if block.settings.show_rating -%}
                <div class="rating">
                  <div class="rating__stars" role="img" aria-label="{{ 'general.rating.info' | t: rating_value: block.settings.rating, rating_max: 5 }}">
                    {%- for i in (1..5) -%}
                      {%- if i <= block.settings.rating -%}
                        {%- render 'icon' with 'rating-star', class: 'rating__star' -%}
                      {%- else -%}
                        {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--empty' -%}
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
              {%- endif -%}

              {%- if block.settings.content != blank -%}
                <blockquote class="blockquote h3" reveal-on-scroll="true">
                  {%- render 'styled-text', content: block.settings.content, text_color: section.settings.heading_color, gradient: section.settings.heading_gradient -%}
                </blockquote>
              {%- endif -%}

              {%- if block.settings.logo != blank or block.settings.author != blank -%}
                <div class="v-stack gap-2">
                  {%- if block.settings.logo != blank -%}
                    {%- liquid
                      assign loading_strategy = nil
            
                      if section.index > 3 or forloop.first == false
                        assign loading_strategy = 'lazy'
                      endif
                    -%}

                    <div {% render 'surface', class: 'press__logo rounded', background: section.settings.logo_item_background %}>
                      {%- capture sizes -%}{{ block.settings.logo_width }}px{%- endcapture -%}
                      {%- capture widths -%}{{ block.settings.logo_width }}, {{ block.settings.logo_width | times: 2 | at_most: block.settings.logo.width }}{%- endcapture -%}
                      {%- capture max_width -%}--press-image-max-width: {{ block.settings.logo_width }}px{%- endcapture -%}
                      {{- block.settings.logo | image_url: width: block.settings.logo.width | image_tag: style: max_width, loading: loading_strategy, widths: widths, class: 'press__image', sizes: sizes -}}
                    </div>
                  {%- endif -%}

                  {%- if block.settings.author != blank -%}
                    <p class="press__author text-subdued">{{ block.settings.author }}</p>
                  {%- endif -%}
                </div>
              {%- endif -%}
            </div>
          {%- endfor -%}
        </press-carousel>

        {%- if section.blocks.size > 1 -%}
          <div class="press__controls">
            <button class="circle-button ring group" is="prev-button" aria-controls="carousel-{{ section.id }}">
              <span class="sr-only">{{ 'general.accessibility.previous' | t }}</span>
              <span class="animated-arrow animated-arrow--reverse"></span>
            </button>

            <page-dots aria-controls="carousel-{{ section.id }}" class="page-dots">
              {%- for index in (1..section.blocks.size) -%}
                <button type="button" class="tap-area" aria-current="{% if forloop.first %}true{% else %}false{% endif %}">
                  <span class="sr-only">{{ 'general.accessibility.go_to_item' | t: index: index }}</span>
                </button>
              {%- endfor -%}
            </page-dots>

            <button class="circle-button ring group" is="next-button" aria-controls="carousel-{{ section.id }}">
              <span class="sr-only">{{ 'general.accessibility.next' | t }}</span>
              <span class="animated-arrow"></span>
            </button>
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "Press",
  "class": "shopify-section--press",
  "tag": "section",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "max_blocks": 6,
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "select",
      "id": "content_size",
      "label": "Content size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fill",
          "label": "Fill page"
        }
      ],
      "default": "medium"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color_background",
      "id": "heading_gradient",
      "label": "Heading gradient"
    },
    {
      "type": "color",
      "id": "logo_item_background",
      "label": "Logo background"
    },
    {
      "type": "color",
      "id": "logo_item_border",
      "label": "Logo border"
    }
  ],
  "blocks": [
    {
      "type": "quote",
      "name": "Quote",
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Logo",
          "info": "300 x 90px .jpg recommended"
        },
        {
          "type": "range",
          "id": "logo_width",
          "min": 50,
          "max": 350,
          "unit": "px",
          "step": 5,
          "label": "Logo width",
          "default": 100
        },
        {
          "type": "text",
          "id": "author",
          "label": "Author"
        },
        {
          "type": "text",
          "id": "content",
          "label": "Quote",
          "default": "Write some content about what they says about your store."
        },
        {
          "type": "checkbox",
          "id": "show_rating",
          "label": "Show rating",
          "default": true
        },
        {
          "type": "range",
          "min": 0,
          "max": 5,
          "id": "rating",
          "label": "Rating",
          "default": 4
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Press",
      "blocks": [
        {
          "type": "quote"
        },
        {
          "type": "quote"
        },
        {
          "type": "quote"
        }
      ]
    }
  ]
}
{% endschema %}