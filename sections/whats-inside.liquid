{%- if section.blocks.size > 0 -%}
    {%- render 'section-spacing-collapsing' -%}
  
    {%- comment -%}
    ------------------------------------------------------------------------------------------------------------------------
    CSS
    ------------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}
  
    <style>
      #shopify-section-{{ section.id }} {
        --whats-inside-font-size: calc(min(20vw, 65px) * {{ section.settings.impact_text_size_ratio }});
        --whats-inside-auto-columns: {% if section.blocks.size == 1 %}minmax(0, 1fr){% else %}{% if section.settings.text_divider != 'none' %}48vw auto{% else %}64vw{% endif %}{% endif %};
      }
  
      @media screen and (min-width: 700px) {
        #shopify-section-{{ section.id }} {
          --whats-inside-font-size: calc(min(15vw, var(--container-max-width) * 0.15) / {{ section.blocks.size }} * {{ section.settings.impact_text_size_ratio }});
          --whats-inside-auto-columns: {% if section.settings.text_divider != 'none' %}minmax(0, 1fr) auto{% else %}minmax(0, 1fr){% endif %};
        }
      }
    </style>
  
    {%- comment -%}
    ------------------------------------------------------------------------------------------------------------------------
    LIQUID
    ------------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}
  
    <div {% render 'section-properties' %}>
      <div {% unless section.settings.stack_mobile %}class="scroll-area bleed {% if section.blocks.size < 3 %}sm:unbleed{% else %}md:unbleed{% endif %}"{% endunless %}>
        <div class="whats-inside whats-inside--{{ section.settings.text_alignment }} {% unless section.settings.stack_mobile %}whats-inside--scroll{% endunless %}">
            {%- for block in section.blocks -%}
              <div class="snap-center" {{ block.shopify_attributes }}>
                {%- if block.settings.title != blank -%}
                  <h2 class="whats-inside__text heading break-all">
                    <whats-inside {% if block.settings.animate_impact_text %}count-up="{{ block.settings.animate_impact_text_duration }}"{% endif %} reveal-js>
                      {%- render 'styled-text', content: block.settings.title, text_color: section.settings.heading_text_color, gradient: section.settings.heading_gradient, style: section.settings.impact_text_style -%}
                    </whats-inside>
                  </h2>
                {%- endif -%}
  
                {%- if block.settings.subheading != blank or block.settings.content != blank or block.settings.button_text != blank -%}
                  <div class="whats-inside__content">
                    <div class="prose">
                      {%- if block.settings.subheading != blank -%}
                        <h3 class="h4">{{ block.settings.subheading | escape }}</h3>
                      {%- endif -%}
  
                      {{- block.settings.content -}}
  
                      {%- if block.settings.button_text != blank -%}
                        {%- render 'button', content: block.settings.button_text, href: block.settings.button_url, size: 'xl', background: section.settings.button_background, text_color: section.settings.button_text_color -%}
                      {%- endif -%}
                    </div>
                  </div>
                {%- endif -%}
              </div>
  
              {%- unless forloop.last or section.settings.text_divider == 'none' -%}
                {%- case section.settings.text_divider -%}
                  {%- when 'square' -%}
                    <span class="shape-square shape--lg place-self-center"></span>
                  {%- when 'circle' -%}
                    <span class="shape-circle shape--lg place-self-center"></span>
                  {%- when 'diamond' -%}
                    <span class="shape-diamond shape--lg place-self-center"></span>
                  {%- when 'line' -%}
                    <span class="shape-line {% if section.settings.stack_mobile %}hidden sm:block{% endif %}"></span>
                {%- endcase -%}
              {%- endunless -%}
            {%- endfor -%}
        </div>
      </div>
    </div>
  {%- endif -%}
  
  {% schema %}
  {
    "name": "Whats inside",
    "class": "shopify-section--whats-inside",
    "tag": "section",
    "disabled_on": {
      "templates": ["password"],
      "groups": ["header", "custom.overlay"]
    },
    "max_blocks": 3,
    "blocks": [
      {
        "type": "item",
        "name": "Item",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Whats inside",
            "info": "For best results, keep this text short and impactful.",
            "default": "100%"
          },
          {
            "type": "text",
            "id": "subheading",
            "label": "Subheading",
            "default": "Showcase a benefit of your product"
          },
          {
            "type": "richtext",
            "id": "content",
            "label": "Content"
          },
          {
            "type": "text",
            "id": "button_text",
            "label": "Button text"
          },
          {
            "type": "url",
            "id": "button_url",
            "label": "Button link"
          },
          {
            "type": "header",
            "content": "Number animation"
          },
          {
            "type": "paragraph",
            "content": "Only number can be animated. Dots, commas and spaces can be used as separators."
          },
          {
            "type": "checkbox",
            "id": "animate_impact_text",
            "label": "Show count up animation",
            "default": false
          },
          {
            "type": "range",
            "id": "animate_impact_text_duration",
            "label": "Count up duration",
            "min": 0.5,
            "max": 5,
            "step": 0.1,
            "unit": "s",
            "default": 2
          }
        ]
      }
    ],
    "settings": [
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full width",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "stack_mobile",
        "label": "Stack on mobile",
        "default": true
      },
      {
        "type": "select",
        "id": "text_alignment",
        "label": "Text alignment",
        "options": [
          {
            "value": "start",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "end",
            "label": "Right"
          }
        ],
        "default": "center"
      },
      {
        "type": "select",
        "id": "impact_text_style",
        "label": "Whats inside style",
        "options": [
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "fill",
            "label": "Fill"
          }
        ],
        "default": "fill"
      },
      {
        "type": "select",
        "id": "text_divider",
        "label": "Multiple texts divider",
        "options": [
          {
            "value": "none",
            "label": "None"
          },
          {
            "value": "square",
            "label": "Square"
          },
          {
            "value": "circle",
            "label": "Circle"
          },
          {
            "value": "diamond",
            "label": "Diamond"
          },
          {
            "value": "line",
            "label": "Line"
          }
        ],
        "default": "none"
      },
      {
        "type": "range",
        "id": "impact_text_size_ratio",
        "label": "Whats inside size ratio",
        "min": 0.5,
        "max": 1.5,
        "step": 0.1,
        "default": 1
      },
      {
        "type": "header",
        "content": "Colors",
        "info": "Gradient replaces solid colors when set. Gradient text outline and gradient background cannot be combined."
      },
      {
        "type": "color",
        "id": "background",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "background_gradient",
        "label": "Background gradient"
      },
      {
        "type": "color",
        "id": "heading_text_color",
        "label": "Whats inside"
      },
      {
        "type": "color_background",
        "id": "heading_gradient",
        "label": "Whats inside gradient"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text"
      },
      {
        "type": "color",
        "id": "button_background",
        "label": "Button background"
      },
      {
        "type": "color",
        "id": "button_text_color",
        "label": "Button text"
      }
    ],
    "presets": [
      {
        "name": "Whats inside",
        "blocks": [
          {
            "type": "item"
          }
        ]
      }
    ]
  }
  {% endschema %}