{{ 'section-instagram.min.css' | asset_url | stylesheet_tag }}

{%- if section.blocks.size > 0 -%}
  <div 
    data-wetheme-section-type="scrolling-banner" 
    data-wetheme-section-id="{{ section.id }}"
    style="padding-top: {{ section.settings.section_padding_top }}px; padding-bottom: {{ section.settings.section_padding_bottom }}px;"
  >
    {% if section.settings.hedding != blank %}
      <h2 class="instagram-hedding h2" >{{ section.settings.hedding }}</h2>
    {% endif %}
    <scrolling-banner
      class="animation-direction-{{ section.settings.animation_direction }}"
      style="
      --scrolling-banner-gutter-desktop: {{ section.settings.space_between_blocks_desktop | divided_by: 2 }}px; 
      --scrolling-banner-gutter-mobile: {{ section.settings.space_between_blocks_mobile | divided_by: 2 }}px; 
      --scrolling-banner-animation-speed: {{ section.settings.animation_speed | times: 0.1 | times: section.blocks.size }}s;
      --scrolling-banner-rotate: {{ section.settings.rotate | times: 1.0 }}deg;"
    >
      {%- for content in (1..10) -%}
        <ol class="banner"{% if forloop.index > 1 %} data-duplicated aria-hidden="true"{% endif %}>
          {%- for block in section.blocks -%}
            <li class="banner-item {{ block.id }}" {{ block.shopify_attributes }}>
                  {% if block.settings.link %}
                    <a href="{{ block.settings.link }}">
                  {% endif %}
                    <style>
                      .{{ block.id }} .icon--placeholder,
                      .{{ block.id }} .content-image {
                        height: {{ block.settings.height_mobile }}px;
                      }
                      @media (min-width: 768px) {
                        .{{ block.id }} .icon--placeholder,
                        .{{ block.id }} .content-image {
                          height: {{ block.settings.height_desktop }}px;
                        }
                      }
                      @media only screen and (min-width:768px) and (max-width:1024px){
                        .{{ block.id }} .icon--placeholder,
                        .{{ block.id }} .content-image {
                          height: 228px;
                        }
                      }
                      @media (max-width: 480px) {
                        .{{ block.id }} .icon--placeholder,
                        .{{ block.id }} .content-image {
                          height: 116px;
                        }
                      }
    
                      
                    </style>
                    {%- if block.settings.image != blank -%}
                      {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: loading_strategy, sizes: '(max-width: 699px) 160px, 285px', widths: '200,300,400,500,600,700,800,900,1000,1200', class: 'zoom-image content-image' -}}
                    {%- else -%}
                      {% capture current %}{% cycle 1, 2, 3, 4, 5, 6 %}{% endcapture %}
                      {{ 'product-' | append: current | placeholder_svg_tag: 'icon icon--placeholder' }}
                    {%- endif -%}
                    <div class="instafeed-hover-layer">
                      <div class="instafeed-hover-icon">
                        <img class="instafeed-lazy-image instafeed-lazy-image--handled"
                             src="https://cdn.shopify.com/s/files/1/0878/9170/6174/files/ins.svg?v=1750397082"
                             alt="Instagram Icon">
                      </div>
                    </div>

                  {% if block.settings.link %}
                    </a>
                  {% endif %}
            </li>
          {%- endfor -%}
        </ol>
      {%- endfor -%}
    </scrolling-banner>
    
    <safe-load-scripts class="hidden">
      <script src="{{ 'section-instagram.js' | asset_url }}" type="module" defer="defer" data-flow-load-key="section-instagram"></script>
    </safe-load-scripts>

  </div>
{%- endif -%}

{% schema %}
{
  "name": "Instagram",
  "tag": "section",
  "class": "scrolling-banner",
  "settings": [
    {
      "type": "text",
      "id": "hedding",
      "label": "Heading"
    },
    {
      "type": "header",
      "content": "Animation"
    },
    {
      "type": "radio",
      "id": "animation_direction",
      "label": "Direction",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "Left"
        },
        {
          "value": "reverse",
          "label": "Right"
        }
      ]
    },
    {
      "type": "range",
      "id": "animation_speed",
      "label": "speed",
      "min": 15,
      "max": 80,
      "step": 1,
      "unit": "s",
      "default": 60
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "section_padding_top",
      "label": "Spacing top",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "default": 60
    },
    {
      "type": "range",
      "id": "section_padding_bottom",
      "label": "Spacing bottom",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "default": 60
    },
    {
      "type": "range",
      "id": "space_between_blocks_desktop",
      "label": "Space between blocks desktop",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "space_between_blocks_mobile",
      "label": "Space between blocks mobile",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 20
    },
    {
      "type": "select",
      "id": "rotate",
      "label": "Rotate",
      "default": "0",
      "options": [
        {
          "value": "-5",
          "label": "-5"
        },
        {
          "value": "0",
          "label": "0"
        },
        {
          "value": "+5",
          "label": "+5"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "height_desktop",
          "label": "Height desktop",
          "min": 16,
          "max": 300,
          "step": 4,
          "unit": "px",
          "default": 60
        },
        {
          "type": "range",
          "id": "height_mobile",
          "label": "Height mobile",
          "min": 16,
          "max": 300,
          "step": 4,
          "unit": "px",
          "default": 60
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Instagram",
      "settings": {
      },
      "blocks": [
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}