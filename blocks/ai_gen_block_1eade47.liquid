{% doc %}
  @prompt
    image card slider with Flickity Card Slider section title section sub title and 3.5 card in display card one image and card title and Description

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-image-slider-{{ ai_gen_id }} {
    padding: 40px 20px;
    background-color: {{ block.settings.background_color }};
  }

  .ai-image-slider-container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-image-slider-header-{{ ai_gen_id }} {
    text-align: {{ block.settings.text_alignment }};
    margin-bottom: 40px;
  }

  .ai-image-slider-title-{{ ai_gen_id }} {
    color: {{ block.settings.title_color }};
    font-size: {{ block.settings.title_size }}px;
    margin: 0 0 16px 0;
    font-weight: 600;
  }

  .ai-image-slider-subtitle-{{ ai_gen_id }} {
    color: {{ block.settings.subtitle_color }};
    font-size: {{ block.settings.subtitle_size }}px;
    margin: 0;
    opacity: 0.8;
  }

  .ai-image-slider-wrapper-{{ ai_gen_id }} {
    position: relative;
    overflow: hidden;
  }

  .ai-image-slider-track-{{ ai_gen_id }} {
    display: flex;
    transition: transform 0.3s ease;
    gap: {{ block.settings.card_gap }}px;
  }

  .ai-image-slider-card-{{ ai_gen_id }} {
    flex: 0 0 calc((100% - {{ block.settings.card_gap | times: 2.5 }}px) / 3.5);
    background-color: {{ block.settings.card_background }};
    border-radius: {{ block.settings.card_border_radius }}px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .ai-image-slider-card-{{ ai_gen_id }}:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .ai-image-slider-card-image-{{ ai_gen_id }} {
    width: 100%;
    height: {{ block.settings.image_height }}px;
    overflow: hidden;
    position: relative;
  }

  .ai-image-slider-card-image-{{ ai_gen_id }} img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .ai-image-slider-card-{{ ai_gen_id }}:hover .ai-image-slider-card-image-{{ ai_gen_id }} img {
    transform: scale(1.05);
  }

  .ai-image-slider-card-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-image-slider-card-placeholder-{{ ai_gen_id }} svg {
    width: 60%;
    height: 60%;
    opacity: 0.3;
  }

  .ai-image-slider-card-content-{{ ai_gen_id }} {
    padding: {{ block.settings.card_padding }}px;
  }

  .ai-image-slider-card-title-{{ ai_gen_id }} {
    color: {{ block.settings.card_title_color }};
    font-size: {{ block.settings.card_title_size }}px;
    font-weight: 600;
    margin: 0 0 12px 0;
    line-height: 1.3;
  }

  .ai-image-slider-card-description-{{ ai_gen_id }} {
    color: {{ block.settings.card_description_color }};
    font-size: {{ block.settings.card_description_size }}px;
    line-height: 1.5;
    margin: 0;
  }

  .ai-image-slider-controls-{{ ai_gen_id }} {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
  }

  .ai-image-slider-btn-{{ ai_gen_id }} {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background-color: {{ block.settings.button_color }};
    color: {{ block.settings.button_text_color }};
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .ai-image-slider-btn-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_color }};
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .ai-image-slider-btn-{{ ai_gen_id }}:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
  }

  .ai-image-slider-dots-{{ ai_gen_id }} {
    display: flex;
    gap: 8px;
  }

  .ai-image-slider-dot-{{ ai_gen_id }} {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background-color: rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .ai-image-slider-dot-{{ ai_gen_id }}.active {
    background-color: {{ block.settings.button_color }};
    transform: scale(1.2);
  }

  @media screen and (max-width: 768px) {
    .ai-image-slider-card-{{ ai_gen_id }} {
      flex: 0 0 calc((100% - {{ block.settings.card_gap }}px) / 2);
    }

    .ai-image-slider-{{ ai_gen_id }} {
      padding: 30px 15px;
    }

    .ai-image-slider-header-{{ ai_gen_id }} {
      margin-bottom: 30px;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-image-slider-card-{{ ai_gen_id }} {
      flex: 0 0 calc(100% - {{ block.settings.card_gap }}px);
    }
  }
{% endstyle %}

<image-card-slider-{{ ai_gen_id }}
  class="ai-image-slider-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-image-slider-container-{{ ai_gen_id }}">
    {% if block.settings.section_title != blank or block.settings.section_subtitle != blank %}
      <div class="ai-image-slider-header-{{ ai_gen_id }}">
        {% if block.settings.section_title != blank %}
          <h2 class="ai-image-slider-title-{{ ai_gen_id }}">{{ block.settings.section_title }}</h2>
        {% endif %}
        {% if block.settings.section_subtitle != blank %}
          <p class="ai-image-slider-subtitle-{{ ai_gen_id }}">{{ block.settings.section_subtitle }}</p>
        {% endif %}
      </div>
    {% endif %}

    <div class="ai-image-slider-wrapper-{{ ai_gen_id }}">
      <div class="ai-image-slider-track-{{ ai_gen_id }}" data-slider-track>
        {% for i in (1..6) %}
          {% liquid
            assign image_key = 'card_' | append: i | append: '_image'
            assign title_key = 'card_' | append: i | append: '_title'
            assign description_key = 'card_' | append: i | append: '_description'
            assign card_image = block.settings[image_key]
            assign card_title = block.settings[title_key]
            assign card_description = block.settings[description_key]
          %}

          {% if card_title != blank or card_description != blank or card_image != blank %}
            <div class="ai-image-slider-card-{{ ai_gen_id }}">
              <div class="ai-image-slider-card-image-{{ ai_gen_id }}">
                {% if card_image %}
                  <img
                    src="{{ card_image | image_url: width: 400 }}"
                    alt="{{ card_image.alt | escape }}"
                    loading="lazy"
                    width="400"
                    height="{{ block.settings.image_height }}"
                  >
                {% else %}
                  <div class="ai-image-slider-card-placeholder-{{ ai_gen_id }}">
                    {{ 'image' | placeholder_svg_tag }}
                  </div>
                {% endif %}
              </div>
              <div class="ai-image-slider-card-content-{{ ai_gen_id }}">
                {% if card_title != blank %}
                  <h3 class="ai-image-slider-card-title-{{ ai_gen_id }}">{{ card_title }}</h3>
                {% endif %}
                {% if card_description != blank %}
                  <p class="ai-image-slider-card-description-{{ ai_gen_id }}">{{ card_description }}</p>
                {% endif %}
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>

    <div class="ai-image-slider-controls-{{ ai_gen_id }}">
      <button
        class="ai-image-slider-btn-{{ ai_gen_id }}"
        data-slider-prev
        aria-label="Previous slide"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
      </button>

      <div class="ai-image-slider-dots-{{ ai_gen_id }}" data-slider-dots></div>

      <button
        class="ai-image-slider-btn-{{ ai_gen_id }}"
        data-slider-next
        aria-label="Next slide"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>
    </div>
  </div>
</image-card-slider-{{ ai_gen_id }}>

<script>
  (function() {
    class ImageCardSlider{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
        this.currentSlide = 0;
        this.cardWidth = 0;
        this.visibleCards = 3.5;
        this.totalCards = 0;
        this.maxSlides = 0;
      }

      connectedCallback() {
        this.track = this.querySelector('[data-slider-track]');
        this.prevBtn = this.querySelector('[data-slider-prev]');
        this.nextBtn = this.querySelector('[data-slider-next]');
        this.dotsContainer = this.querySelector('[data-slider-dots]');
        this.cards = this.track.querySelectorAll('.ai-image-slider-card-{{ ai_gen_id }}');

        this.totalCards = this.cards.length;
        this.calculateDimensions();
        this.createDots();
        this.updateSlider();
        this.setupEventListeners();
        this.setupResizeObserver();
      }

      calculateDimensions() {
        if (window.innerWidth <= 480) {
          this.visibleCards = 1;
        } else if (window.innerWidth <= 768) {
          this.visibleCards = 2;
        } else {
          this.visibleCards = 3.5;
        }

        this.maxSlides = Math.max(0, Math.ceil(this.totalCards - this.visibleCards));
      }

      createDots() {
        this.dotsContainer.innerHTML = '';
        for (let i = 0; i <= this.maxSlides; i++) {
          const dot = document.createElement('button');
          dot.className = 'ai-image-slider-dot-{{ ai_gen_id }}';
          dot.setAttribute('aria-label', `Go to slide ${i + 1}`);
          dot.addEventListener('click', () => this.goToSlide(i));
          this.dotsContainer.appendChild(dot);
        }
      }

      updateSlider() {
        if (this.totalCards === 0) return;

        const cardWidth = this.cards[0].offsetWidth;
        const gap = parseInt(getComputedStyle(this.track).gap) || 0;
        const translateX = -(this.currentSlide * (cardWidth + gap));

        this.track.style.transform = `translateX(${translateX}px)`;

        this.prevBtn.disabled = this.currentSlide === 0;
        this.nextBtn.disabled = this.currentSlide >= this.maxSlides;

        this.dotsContainer.querySelectorAll('.ai-image-slider-dot-{{ ai_gen_id }}').forEach((dot, index) => {
          dot.classList.toggle('active', index === this.currentSlide);
        });
      }

      goToSlide(slideIndex) {
        this.currentSlide = Math.max(0, Math.min(slideIndex, this.maxSlides));
        this.updateSlider();
      }

      nextSlide() {
        if (this.currentSlide < this.maxSlides) {
          this.currentSlide++;
          this.updateSlider();
        }
      }

      prevSlide() {
        if (this.currentSlide > 0) {
          this.currentSlide--;
          this.updateSlider();
        }
      }

      setupEventListeners() {
        this.prevBtn.addEventListener('click', () => this.prevSlide());
        this.nextBtn.addEventListener('click', () => this.nextSlide());

        let startX = 0;
        let isDragging = false;

        this.track.addEventListener('touchstart', (e) => {
          startX = e.touches[0].clientX;
          isDragging = true;
        });

        this.track.addEventListener('touchmove', (e) => {
          if (!isDragging) return;
          e.preventDefault();
        });

        this.track.addEventListener('touchend', (e) => {
          if (!isDragging) return;
          isDragging = false;

          const endX = e.changedTouches[0].clientX;
          const diff = startX - endX;

          if (Math.abs(diff) > 50) {
            if (diff > 0) {
              this.nextSlide();
            } else {
              this.prevSlide();
            }
          }
        });
      }

      setupResizeObserver() {
        const resizeObserver = new ResizeObserver(() => {
          this.calculateDimensions();
          this.createDots();
          this.currentSlide = Math.min(this.currentSlide, this.maxSlides);
          this.updateSlider();
        });

        resizeObserver.observe(this);
      }
    }

    customElements.define('image-card-slider-{{ ai_gen_id }}', ImageCardSlider{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Image Card Slider",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Section Header"
    },
    {
      "type": "text",
      "id": "section_title",
      "label": "Section title",
      "default": "Featured Collection"
    },
    {
      "type": "textarea",
      "id": "section_subtitle",
      "label": "Section subtitle",
      "default": "Discover our carefully curated selection of premium products"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Card 1"
    },
    {
      "type": "image_picker",
      "id": "card_1_image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "card_1_title",
      "label": "Title",
      "default": "Premium Quality"
    },
    {
      "type": "textarea",
      "id": "card_1_description",
      "label": "Description",
      "default": "Experience the finest materials and craftsmanship in every product we offer."
    },
    {
      "type": "header",
      "content": "Card 2"
    },
    {
      "type": "image_picker",
      "id": "card_2_image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "card_2_title",
      "label": "Title",
      "default": "Fast Shipping"
    },
    {
      "type": "textarea",
      "id": "card_2_description",
      "label": "Description",
      "default": "Get your orders delivered quickly with our reliable shipping partners."
    },
    {
      "type": "header",
      "content": "Card 3"
    },
    {
      "type": "image_picker",
      "id": "card_3_image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "card_3_title",
      "label": "Title",
      "default": "24/7 Support"
    },
    {
      "type": "textarea",
      "id": "card_3_description",
      "label": "Description",
      "default": "Our dedicated customer support team is here to help you anytime."
    },
    {
      "type": "header",
      "content": "Card 4"
    },
    {
      "type": "image_picker",
      "id": "card_4_image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "card_4_title",
      "label": "Title",
      "default": "Eco Friendly"
    },
    {
      "type": "textarea",
      "id": "card_4_description",
      "label": "Description",
      "default": "Sustainable practices and environmentally conscious manufacturing."
    },
    {
      "type": "header",
      "content": "Card 5"
    },
    {
      "type": "image_picker",
      "id": "card_5_image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "card_5_title",
      "label": "Title"
    },
    {
      "type": "textarea",
      "id": "card_5_description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Card 6"
    },
    {
      "type": "image_picker",
      "id": "card_6_image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "card_6_title",
      "label": "Title"
    },
    {
      "type": "textarea",
      "id": "card_6_description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Title size",
      "default": 32
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subtitle size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Card Settings"
    },
    {
      "type": "color",
      "id": "card_background",
      "label": "Card background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "card_title_color",
      "label": "Card title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "card_description_color",
      "label": "Card description color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "card_title_size",
      "min": 14,
      "max": 28,
      "step": 1,
      "unit": "px",
      "label": "Card title size",
      "default": 18
    },
    {
      "type": "range",
      "id": "card_description_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Card description size",
      "default": 14
    },
    {
      "type": "range",
      "id": "image_height",
      "min": 150,
      "max": 400,
      "step": 10,
      "unit": "px",
      "label": "Image height",
      "default": 200
    },
    {
      "type": "range",
      "id": "card_padding",
      "min": 10,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Card padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "card_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Card border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "card_gap",
      "min": 10,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Gap between cards",
      "default": 20
    },
    {
      "type": "header",
      "content": "Controls"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_hover_color",
      "label": "Button hover color",
      "default": "#333333"
    }
  ],
  "presets": [
    {
      "name": "Image Card Slider"
    }
  ]
}
{% endschema %}