{"general": {"page": "{{ page }}. <PERSON><PERSON>", "home": "<PERSON>", "accessibility": {"skip_to_content": "İçeriğe geç", "pagination": "<PERSON><PERSON><PERSON><PERSON> gez<PERSON>", "go_to_page": "{{ index }}. sayfaya git", "go_to_item": "{{ index }} ögesine git", "item_nth_of_count": "{{ index }}/{{ count }}. öge", "drag": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "next": "İleri", "previous": "<PERSON><PERSON>", "play_video": "<PERSON><PERSON> o<PERSON>t", "read_more": "Daha fazlasını oku"}, "label": {"color": "Renk", "white": "<PERSON><PERSON>", "size": "<PERSON><PERSON>"}, "social": {"follow_on": "{{ social_media }} üzerinde takip edin", "share": "Paylaş", "share_on": "{{ social_media }} üzerinde pay<PERSON>ın", "share_email": "E-posta ile <PERSON>"}, "rating": {"info": "{{ rating_value }}/{{ rating_max }} yıldız"}, "newsletter": {"email": "E-posta", "subscribe": "<PERSON><PERSON> ol", "notify_me": "Bana bildir", "subscribed_successfully": "Haber bültenimize abone oldunuz."}, "localization": {"country": "<PERSON><PERSON><PERSON>", "language": "Dil", "change_country_accessibility_text": "Ülkeyi veya para birimini değiştir", "change_language_accessibility_text": "<PERSON><PERSON>"}, "privacy_bar": {"accept": "Kabul Et", "decline": "<PERSON><PERSON>"}, "form": {"max_characters": "Maks<PERSON>um {{ max_chars }} karakter"}, "on_boarding": {"blog_post_category": "<PERSON><PERSON><PERSON>", "blog_post_title": "Makale", "blog_post_excerpt": "Blog gönderiniz hakkında metin yazın.", "product_vendor": "Satıcı", "product_title": "<PERSON><PERSON><PERSON><PERSON>", "product_description": "Ürününüz hakkında metin yazın.", "collection_title": "Koleksiyon"}}, "header": {"general": {"account": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON> yap", "menu": "<PERSON><PERSON>", "search": "Ara", "cart": "Sepet"}}, "product": {"general": {"description": "<PERSON><PERSON>ı<PERSON><PERSON>", "view_product": "Ürünü <PERSON><PERSON><PERSON><PERSON><PERSON>", "quick_add": "+ <PERSON>ı<PERSON><PERSON><PERSON> ekle", "add_to_cart_short": "+ <PERSON>kle", "add_to_cart_button": "Sepete ekle", "pre_order_button": "<PERSON><PERSON> ver", "sold_out_button": "Tükendi", "unavailable_button": "<PERSON><PERSON><PERSON>", "added_to_cart": "Sepetinize eklendi!", "sold_out_badge": "Tükendi", "on_sale_badge": "İndirimde", "discount_badge_html": "{{ savings }} tasarruf edin", "sku": "SKU:", "variant": "Çeşit", "view_in_space": "<PERSON><PERSON>ı<PERSON>ı<PERSON> görüntüleyin", "taxes_included": "<PERSON><PERSON><PERSON> da<PERSON>.", "taxes_excluded": "<PERSON><PERSON><PERSON>.", "shipping_policy_html": "<a href=\"{{ link }}\" class=\"link\"><PERSON><PERSON><PERSON><PERSON></a> ödeme sırasında hesaplanır", "size_chart": "<PERSON><PERSON> tab<PERSON>u", "available_colors_count": {"one": "{{ count }} renk mevcut", "other": "{{ count }} renk mevcut"}}, "gallery": {"close": "<PERSON><PERSON><PERSON> kapat", "zoom": "Yakınlaştır", "error": "<PERSON><PERSON><PERSON>"}, "price": {"regular_price": "Normal fiyat", "sale_price": "İndirimli fi<PERSON>t", "from_price_html": "{{ price_min }}'den b<PERSON><PERSON><PERSON><PERSON>"}, "quantity": {"label": "<PERSON><PERSON><PERSON>", "increase_quantity": "Miktarı artır", "decrease_quantity": "Miktarı azalt", "minimum_of": "{{ min }} en az", "maximum_of": "{{ max }} en fazla", "increment_of": "{{ step }} artış"}, "volume_pricing": {"title": "Toplu Alım Bazlı Fiyatlandırma", "minimum": "{{ quantity }}+", "price_at_each": "{{ price }}/adet"}, "rating_count": {"zero": "Değerlendirme yok", "one": "{{ count }} değ<PERSON>lendirme", "other": "{{ count }} değ<PERSON>lendirme"}, "inventory": {"in_stock": "<PERSON><PERSON><PERSON> var", "oversell_stock": "Yakında yeniden stoklanıyor", "incoming_stock": "{{ next_incoming_date }} ta<PERSON><PERSON><PERSON> yeniden stok<PERSON>ı<PERSON>r", "low_stock_with_quantity_count": {"one": "Yalnızca {{count}} birim kaldı", "other": "Yalnızca {{count}} birim kaldı"}}, "store_availability": {"view_store_info": "Mağaza bilgilerini görü<PERSON>üle", "check_other_stores": "<PERSON><PERSON><PERSON> ma<PERSON>azalarda olup olmadığını kontrol et", "pick_up_available": "Gel-al mevcut", "pick_up_currently_unavailable": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> anda k<PERSON>lamıyor", "pick_up_available_at": "Gel-al {{ location_name }} konumunda mevcut", "pick_up_unavailable_at": "Gel-al {{ location_name }} konumunda şu anda kullanılamıyor"}}, "collection": {"general": {"empty_collection": "<PERSON><PERSON> kole<PERSON><PERSON><PERSON> boş", "all_collections": "<PERSON><PERSON><PERSON>", "no_collections": "Bu mağazada koleksiyon yok.", "continue_shopping": "Alışverişe devam"}, "products_count": {"zero": "0 ürün", "one": "1 ürün", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "faceting": {"filters": "<PERSON><PERSON><PERSON><PERSON>", "filter_and_sort": "Filtrele ve sırala", "filter_button": "Filtre", "clear_filters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "apply_filters": "<PERSON><PERSON><PERSON><PERSON>", "sort_by": "Şuna göre sırala:", "remove_filter": "\"{{ name }}\" filtresini kaldır", "no_results": "Bu filtrelerle eşleşen ürün yok.", "price_range_to": "-", "price_filter_from": "Başlangıç fiyatı", "price_filter_to": "<PERSON> <PERSON>yat", "price_filter": "{{ min_price }} - {{ max_price }}", "availability_label": "Yalnızca stoktakiler"}}, "blog": {"general": {"empty_blog": "Bu blog boş", "back_to_home": "<PERSON> <PERSON><PERSON> d<PERSON>n", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "all_posts": "<PERSON><PERSON><PERSON>"}, "post": {"written_by": "<PERSON><PERSON>: {{ author }}", "share": "Paylaş", "tags": "<PERSON><PERSON><PERSON><PERSON>", "continue_reading": "<PERSON><PERSON><PERSON>"}, "comments": {"leave_comment": "<PERSON><PERSON> ya<PERSON>ın", "moderated": "<PERSON>ü<PERSON> yo<PERSON>lar yayınlanmadan önce incelenir.", "name": "Ad", "email": "E-posta", "message": "<PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "comment_sent": "Yorumunuz gönderildi. Blogumuz incelendiğinden kısa bir süre sonra yayınlanacak.", "comment_published": "Yorumunuz yayınlandı.", "count": {"zero": "{{ count }} yorum", "one": "{{ count }} yorum", "other": "{{ count }} yorum"}}}, "contact": {"form": {"name": "Ad", "email": "E-posta", "message": "<PERSON><PERSON>", "submit": "<PERSON><PERSON>", "success_message": "Mesajınız gönderildi."}}, "customer": {"account": {"welcome": "<PERSON><PERSON> geldiniz {{first_name}}", "tagline": "Tüm siparişlerinizi görünt<PERSON>leyin ve hesap bilgilerinizi yönetin.", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addresses": "<PERSON><PERSON><PERSON>", "logout": "Çıkış Yap", "no_orders": "Hen<PERSON>z sipariş vermediniz.", "continue_shopping": "Alışverişe devam et"}, "login": {"title": "<PERSON><PERSON><PERSON>", "email": "E-posta", "password": "Şifre", "submit": "<PERSON><PERSON><PERSON>", "forgot_password": "Şifrenizi mi unuttunuz?", "sign_up": "<PERSON><PERSON><PERSON>"}, "recover_password": {"title": "<PERSON><PERSON><PERSON><PERSON> kurtar", "email": "E-posta", "submit": "<PERSON><PERSON>", "back_to_login": "<PERSON><PERSON><PERSON><PERSON>", "success_message": "Şifrenizi sıfırlamanız için talimatlar içeren bir e-posta adresinize gönderildi."}, "register": {"title": "<PERSON><PERSON><PERSON>", "first_name": "Ad", "last_name": "Soyadı", "email": "E-posta", "password": "Şifre", "accepts_marketing": "Haber bültenimize kaydolun", "submit": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>"}, "activate_account": {"title": "Hesabı etkinleştir", "instructions": "Hesabınızı oluşturmak için bir şifre girin:", "password": "Şifre", "password_confirmation": "<PERSON><PERSON><PERSON>", "submit": "Etkinleştir", "cancel": "İptal Et"}, "reset_password": {"title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "password": "Şifre", "password_confirmation": "<PERSON><PERSON><PERSON>", "submit": "Sıfırla"}, "order": {"order": "Sipariş", "order_name": "Sipariş No. {{name}}", "view_details": "Sipariş bilgilerini görüntüle", "back": "<PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON>", "fulfillment_status": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON>", "cancelled_on": "{{date}} tarihinde iptal edildi. <PERSON><PERSON>: {{reason}}", "product": "<PERSON><PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "fulfillment_with_number": "Siparişiniz {{date}} tarihinde gönderildi. {{tracking_number}} ile gönderinizi takip edin.", "fulfillment": "Siparişiniz {{date}} tarihin<PERSON> gö<PERSON>ildi.", "track_shipment": "G<PERSON>nder<PERSON><PERSON> taki<PERSON> et", "subtotal": "<PERSON>", "discount": "İndirim", "shipping": "<PERSON><PERSON><PERSON><PERSON>", "taxes_included": "<PERSON><PERSON><PERSON><PERSON> dahil", "taxes_excluded": "<PERSON><PERSON><PERSON><PERSON>", "total_duties": "Gümrük", "refunded_amount": "İade tutarı", "total": "Toplam", "shipping_address": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "billing_address": "<PERSON><PERSON> ad<PERSON>i"}, "addresses": {"no_addresses": "Hen<PERSON>z adres kaydetmediniz.", "add_address": "<PERSON><PERSON>", "edit_address": "<PERSON><PERSON><PERSON>", "save_address": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "default_address": "Varsayılan adres", "address_title": "{{ position }}. adres", "delete_confirm": "Bu adres kaldırılsın mı? Onaylandığında bu eylem geri alınamaz.", "fill_form": "Lütfen aşağıdaki bilgileri doldurun:", "first_name": "Ad", "last_name": "Soyadı", "company": "Şirket", "phone": "Telefon numarası", "address1": "1. <PERSON><PERSON>", "address2": "2. <PERSON><PERSON>", "city": "Şehir", "zip": "Posta kodu", "country": "<PERSON><PERSON><PERSON>", "province": "İl", "set_default": "Varsayılan adres o<PERSON> a<PERSON>"}}, "cart": {"general": {"title": "Sepet", "empty": "Sepetiniz boş", "item_count": {"other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "continue_shopping": "Alışverişe devam edin", "weight": "Ağırlık", "subtotal": "<PERSON>", "total": "Toplam", "add_order_note": "Sipariş notu ekle", "edit_order_note": "<PERSON><PERSON><PERSON><PERSON> notunu d<PERSON>", "order_note": "Sipariş notu", "save_note": "<PERSON><PERSON>", "view_cart": "Sepeti görüntüle", "checkout": "<PERSON><PERSON><PERSON> yap", "we_accept": "Kabul ettiklerimiz:", "taxes_and_shipping_policy_at_checkout_html": "Vergiler ve <a href=\"{{ link }}\" class=\"link\">g<PERSON><PERSON><PERSON></a> ödeme sırasında hesaplanır", "taxes_included_but_shipping_at_checkout": "<PERSON>ergi da<PERSON> ve gönderi ücreti ödeme sırasında hesaplanır", "taxes_included_and_shipping_policy_html": "<PERSON><PERSON><PERSON>. <a href=\"{{ link }}\" class=\"link\"><PERSON><PERSON><PERSON><PERSON> ücreti</a> ödeme sırasında hesaplanır.", "taxes_and_shipping_at_checkout": "Vergiler ve gönderi ücreti ödeme sırasında hesaplanır"}, "order": {"product": "<PERSON><PERSON><PERSON><PERSON>", "total": "Toplam", "quantity": "<PERSON><PERSON><PERSON>", "change_quantity": "Miktarı değiştir", "increase_quantity": "Miktarı artır", "decrease_quantity": "Miktarı azalt", "remove": "Kaldır", "remove_with_title": "{{ title }} kaldır"}, "free_shipping_bar": {"limit_unreached_html": "{{ remaining_amount }} daha harca<PERSON>ın ve ücretsiz gönderi kazanın!", "limit_reached_html": "Ücretsiz gönderi için u<PERSON>."}, "shipping_estimator": {"estimate_shipping": "<PERSON><PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON><PERSON>", "province": "İl", "zip": "Posta kodu", "estimate": "<PERSON><PERSON><PERSON>", "no_results": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adresinize gönderi yapamıyoruz.", "one_result": "Adresiniz için bir gönderi seçeneği bulunuyor:", "multiple_results": "Adresiniz için birden çok gönderi seçeneği bulunuyor:", "error": "G<PERSON><PERSON>i seçenekleri getirilirken bir veya daha fazla hata o<PERSON>:"}}, "404": {"general": {"title": "Sayfa bulunamadı", "continue_shopping": "Alışverişe devam edin"}}, "search": {"general": {"title": "Ara", "terms": "\"{{ terms }}\" i<PERSON><PERSON>", "search_placeholder": "Arayın...", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestions": "<PERSON><PERSON><PERSON>", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "posts": "Blog gönderileri", "pages": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "view_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view_all_results": "<PERSON><PERSON><PERSON> sonuçları gö<PERSON><PERSON><PERSON>", "no_results": "<PERSON><PERSON><PERSON> bulunamadı."}, "results_count": {"zero": "\"{{ terms }}\" için 0 sonuç", "one": "\"{{ terms }}\" için 1 sonuç", "other": "\"{{ terms }}\" için {{ count }} sonuç"}}, "gift_card": {"general": {"title": "İşte hediye kartınız", "copy": "Kopyala", "print": "Yazdır", "scan": "ya da bu QR kodunu tarayın", "back_to_store": "Mağazaya dön"}, "issued": {"remaining_amount": "<PERSON><PERSON> miktar", "out_of_html": "/{{ initial_value }}", "redeem_instructions": "Ödeme sırasında hediye kartınızı kullanmak için şu kodu girin:", "code": "Hediye kartı kodu", "expires_on": "{{ expires_on }} ta<PERSON><PERSON><PERSON> s<PERSON><PERSON>i doluyor", "expired": "Hediye kartınızın süresi doldu veya devre dışı bırakıldı.", "add_to_apple_wallet": "Apple Cüzdan'a ekleyin"}, "recipient": {"checkbox": "<PERSON><PERSON>u hediye olarak göndermek istiyorum", "email_label": "Alıcı e-postası", "name_label": "Alıcı adı (isteğe bağlı)", "send_on_label": "<PERSON><PERSON> ta<PERSON> (isteğe bağlı)", "message_label": "Mesaj (isteğe bağlı)"}}, "password": {"general": {"follow_us": "Bizi takip edin", "powered_by": "Bu mağazanın teknolojisini sağlayan:", "store_owner": "<PERSON><PERSON><PERSON><PERSON> sahi<PERSON> mi<PERSON>?", "login": "<PERSON><PERSON><PERSON>"}, "storefront_access": {"enter_password": "<PERSON><PERSON><PERSON> girin", "store_access": "Mağaza <PERSON>", "instructions": "Mağazaya erişmek için şifreyi aşağıdaki alana girin", "password": "Şifre", "enter_store": "Mağazaya girin"}}, "apps": {"shopify_reviews": {"review_info": "Bu <PERSON>rün {{ rating_value }}/{{ rating_max }} yıldız aldı.", "leave_review": "Değerlendirme yazın", "review_count": {"zero": "<PERSON>u <PERSON><PERSON><PERSON><PERSON><PERSON>n hen<PERSON>z değerlendirmesi yok.", "one": "{{ count }} değerlendirme aldı.", "other": "{{ count }} değerlendirme aldı."}}}}