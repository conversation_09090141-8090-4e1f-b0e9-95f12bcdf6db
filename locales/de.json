{"general": {"page": "Seite {{ page }}", "home": "Home", "accessibility": {"skip_to_content": "Zum Inhalt springen", "pagination": "Seiten-Navigation", "go_to_page": "<PERSON><PERSON><PERSON> zu Se<PERSON> {{ index }}", "go_to_item": "<PERSON><PERSON><PERSON> zu Element {{ index }}", "item_nth_of_count": "Element {{ index }} von {{ count }}", "drag": "<PERSON><PERSON><PERSON>", "close": "Schließen", "next": "Vor", "previous": "Zurück", "play_video": "Video abspielen", "read_more": "Wei<PERSON>lesen"}, "label": {"color": "Farbe", "white": "<PERSON><PERSON>", "size": "Größe"}, "social": {"follow_on": "Auf {{ social_media }} folgen", "share": "Teilen", "share_on": "Auf {{ social_media }} teilen", "share_email": "Per E-Mail teilen"}, "rating": {"info": "{{ rating_value }} von {{ rating_max }} <PERSON><PERSON>"}, "newsletter": {"email": "E-Mail", "subscribe": "Abonnieren", "notify_me": "Benachrichtige mich", "subscribed_successfully": "Du hast den Newsletter abonniert."}, "localization": {"country": "Land", "language": "<PERSON><PERSON><PERSON>", "change_country_accessibility_text": "Land oder Währung ändern", "change_language_accessibility_text": "Sprache ändern"}, "privacy_bar": {"accept": "Akzeptieren", "decline": "Verweigern"}, "form": {"max_characters": "Maximal {{ max_chars }} <PERSON><PERSON><PERSON>"}, "on_boarding": {"blog_post_category": "<PERSON><PERSON><PERSON>", "blog_post_title": "Artikel", "blog_post_excerpt": "Schreibe eine Zusammenfassung des Artikels.", "product_vendor": "<PERSON><PERSON><PERSON>", "product_title": "Produkt", "product_description": "Beschreibe das Produkt.", "collection_title": "<PERSON><PERSON><PERSON>"}}, "header": {"general": {"account": "Ko<PERSON>", "login": "Anmelden", "menu": "<PERSON><PERSON>", "search": "<PERSON><PERSON>", "cart": "<PERSON><PERSON><PERSON>"}}, "product": {"general": {"description": "Beschreibung", "view_product": "Produkt an<PERSON><PERSON>", "quick_add": "+ In den Warenkorb", "add_to_cart_short": "+ Hinzufügen", "add_to_cart_button": "In den Warenkorb", "pre_order_button": "Vorbestellen", "sold_out_button": "Ausverkauft", "unavailable_button": "Nicht verfügbar", "added_to_cart": "Zum Warenkorb hinzugefügt!", "sold_out_badge": "Ausverkauft", "on_sale_badge": "Sale", "discount_badge_html": "Spare {{ savings }}", "sku": "SKU:", "variant": "<PERSON><PERSON><PERSON>", "view_in_space": "In deinem Bereich an<PERSON>hen", "taxes_included": "inkl. MwSt.", "taxes_excluded": "zzgl. MwSt.", "shipping_policy_html": "<a href=\"{{ link }}\" class=\"link\">Versandkosten</a> werden an der Kasse berechnet", "size_chart": "Größentabelle", "available_colors_count": {"one": "{{ count }} Farbe verfügbar", "other": "{{ count }} <PERSON><PERSON> ve<PERSON>ü<PERSON>bar"}}, "gallery": {"close": "Galerie schließen", "zoom": "Bild vergrößern", "error": "Bild kann nicht geladen werden"}, "price": {"regular_price": "<PERSON><PERSON><PERSON><PERSON>", "sale_price": "<PERSON><PERSON><PERSON>", "from_price_html": "ab {{ price_min }}"}, "quantity": {"label": "<PERSON><PERSON><PERSON>", "increase_quantity": "<PERSON><PERSON><PERSON><PERSON>", "decrease_quantity": "<PERSON><PERSON><PERSON> ve<PERSON>", "minimum_of": "Minimum von {{ min }}", "maximum_of": "Maximum von {{ max }}", "increment_of": "<PERSON><PERSON><PERSON> von {{ step }}"}, "volume_pricing": {"title": "Volumenabhängige Preisgestaltung", "minimum": "{{ quantity }}+", "price_at_each": "bei {{ price }}/Stück"}, "rating_count": {"zero": "<PERSON><PERSON>", "one": "{{ count }} Bewertung", "other": "{{ count }} Bewertungen"}, "inventory": {"in_stock": "<PERSON><PERSON>", "oversell_stock": "Ware im Zulauf", "incoming_stock": "Ware erwartet am {{ next_incoming_date }}", "low_stock_with_quantity_count": {"one": "Nur noch {{count}} Art<PERSON><PERSON>", "other": "Nur noch {{count}} Art<PERSON><PERSON>"}}, "store_availability": {"view_store_info": "Shop-<PERSON><PERSON>", "check_other_stores": "Verfügbarkeit in anderen Shops prüfen", "pick_up_available": "Abholung möglich", "pick_up_currently_unavailable": "Abholung momentan nicht möglich", "pick_up_available_at": "<PERSON><PERSON><PERSON><PERSON> von {{ location_name }} möglich", "pick_up_unavailable_at": "A<PERSON><PERSON><PERSON> von {{ location_name }} momentan nicht möglich"}}, "collection": {"general": {"empty_collection": "<PERSON><PERSON> ist leer", "all_collections": "Alle Kategorien", "no_collections": "Dieser Shop hat keine Kategorien.", "continue_shopping": "<PERSON><PERSON> e<PERSON>"}, "products_count": {"zero": "0 Produkte", "one": "1 Produkt", "other": "{{ count }} Produkte"}, "faceting": {"filters": "Filter", "filter_and_sort": "Filtern und sortieren", "filter_button": "Filtern", "clear_filters": "Z<PERSON>ücksetzen", "apply_filters": "<PERSON><PERSON><PERSON>", "sort_by": "Sortieren nach", "remove_filter": "Filter \"{{ name }}\" entfernen", "no_results": "<PERSON><PERSON> Produkte, die diesen Filterkriterien entsprechen.", "price_range_to": "bis", "price_filter_from": "<PERSON><PERSON> von", "price_filter_to": "Preis bis", "price_filter": "{{ min_price }} - {{ max_price }}", "availability_label": "Nur lagernde Artikel"}}, "blog": {"general": {"empty_blog": "Dieser Blog ist leer", "back_to_home": "Zurück", "view": "<PERSON><PERSON><PERSON>", "all_posts": "Alle Artikel"}, "post": {"written_by": "Von {{ author }}", "share": "Teilen", "tags": "Tags", "continue_reading": "Wei<PERSON>lesen"}, "comments": {"leave_comment": "Hinterlasse einen Kommentar", "moderated": "Alle Kommentare werden vor der Veröffentlichung geprüft.", "name": "Name", "email": "E-Mail", "message": "Kommentar", "submit": "<PERSON><PERSON><PERSON><PERSON>", "comment_sent": "Dein Kommentar wurde abgesendet. Er wird nach der Prüfung durch den Shopinhaber veröffentlicht!", "comment_published": "<PERSON><PERSON> wurde veröffent<PERSON>.", "count": {"zero": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "one": "{{ count }} Kommentar", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}}}, "contact": {"form": {"name": "Name", "email": "E-Mail", "message": "Nachricht", "submit": "Nachricht senden", "success_message": "<PERSON><PERSON> wurde gesendet."}}, "customer": {"account": {"welcome": "<PERSON><PERSON><PERSON><PERSON>, {{first_name}}", "tagline": "Bestellübersicht ansehen und Kundenkonto verwalten.", "orders": "Bestellungen", "addresses": "<PERSON><PERSON><PERSON>", "logout": "Abmelden", "no_orders": "Du hast noch nichts bestellt.", "continue_shopping": "<PERSON><PERSON> e<PERSON>"}, "login": {"title": "Anmelden", "email": "E-Mail", "password": "Passwort", "submit": "Anmelden", "forgot_password": "Passwort vergessen?", "sign_up": "Registrieren"}, "recover_password": {"title": "Passwort zurücksetzen", "email": "E-Mail", "submit": "Z<PERSON>ücksetzen", "back_to_login": "Zurück zur Anmeldung", "success_message": "Eine E-Mail mit Informationen zum Zurücksetzen des Passworts wurde an deine Adresse gesendet."}, "register": {"title": "Registrieren", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "email": "E-Mail", "password": "Passwort", "accepts_marketing": "Newsletter abonnieren", "submit": "Kundenkonto erstellen", "login": "Anmelden"}, "activate_account": {"title": "Kundenkonto aktivieren", "instructions": "Gib ein Passwort ein, um dein Kundenkonto zu erstellen:", "password": "Passwort", "password_confirmation": "Passwort wiederholen", "submit": "Aktivieren", "cancel": "Abbrechen"}, "reset_password": {"title": "Passwort zurücksetzen", "password": "Passwort", "password_confirmation": "Passwort wiederholen", "submit": "Z<PERSON>ücksetzen"}, "order": {"order": "Bestellung", "order_name": "Bestellung {{name}}", "view_details": "Bestelldetails", "back": "Zurück", "date": "Datum", "payment_status": "Zahlungsstatus", "fulfillment_status": "<PERSON><PERSON><PERSON><PERSON>", "cancelled_on": "<PERSON><PERSON><PERSON><PERSON> am {{date}}. Grund: {{reason}}", "product": "Produkt", "quantity": "<PERSON><PERSON><PERSON>", "fulfillment_with_number": "Deine Bestellung wurde am {{date}} verschickt. Verfolge deine Sendung mit der Sendungsnummer {{tracking_number}}.", "fulfillment": "<PERSON>ine Bestellung wurde am {{date}} verschickt.", "track_shipment": "Sendungsverfolgung", "subtotal": "Zwischensumme", "discount": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON>ers<PERSON>", "taxes_included": "inkl. MwSt.", "taxes_excluded": "zzgl. MwSt.", "total_duties": "<PERSON><PERSON>", "refunded_amount": "Erstattungsbetrag", "total": "Gesamtbetrag", "shipping_address": "Lieferadresse", "billing_address": "Re<PERSON>nungsadress<PERSON>"}, "addresses": {"no_addresses": "Du hast noch keine Adressen gespeichert.", "add_address": "<PERSON><PERSON><PERSON>", "edit_address": "<PERSON><PERSON><PERSON> bear<PERSON>", "save_address": "<PERSON><PERSON><PERSON> s<PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "default_address": "Standardadresse", "address_title": "Adresse {{ position }}", "delete_confirm": "Diese Adresse löschen? Dieser Vorgang kann nicht rückgängig gemacht werden.", "fill_form": "<PERSON><PERSON>lle bitte folgende Felder aus:", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "company": "Firma", "phone": "Telefon", "address1": "Adresszeile 1", "address2": "Adresszeile 2", "city": "Ort", "zip": "PLZ", "country": "Land", "province": "Bundesland/Kanton", "set_default": "Als Standardadresse festlegen"}}, "cart": {"general": {"title": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON> ist leer", "item_count": {"one": "{{ count }} <PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON>"}, "continue_shopping": "<PERSON><PERSON> e<PERSON>", "weight": "Gewicht", "subtotal": "Zwischensumme", "total": "Gesamtbetrag", "taxes_and_shipping_policy_at_checkout_html": "Steuern und <a href=\"{{ link }}\" class=\"link\">Versand</a> werden beim Checkout berechnet", "taxes_included_but_shipping_at_checkout": "Inklusive Steuern, Versand wird beim Checkout berechnet", "taxes_included_and_shipping_policy_html": "Inklusive Steuern. <a href=\"{{ link }}\" class=\"link\">Versand </a> wird beim Checkout berechnet", "taxes_and_shipping_at_checkout": "Steuern und Versand werden beim Checkout berechnet", "add_order_note": "Bestellhinweis hinzufügen", "edit_order_note": "Bestellhinweis bearbeiten", "order_note": "Bestellhinweis", "save_note": "Speichern", "view_cart": "<PERSON><PERSON><PERSON>", "checkout": "<PERSON><PERSON>", "we_accept": "Wir akzeptieren"}, "order": {"product": "Produkt", "total": "Summe", "quantity": "<PERSON><PERSON><PERSON>", "change_quantity": "<PERSON><PERSON><PERSON>", "increase_quantity": "<PERSON><PERSON><PERSON><PERSON>", "decrease_quantity": "<PERSON><PERSON><PERSON> ve<PERSON>", "remove": "Entfernen", "remove_with_title": "Entfernen {{ title }}"}, "free_shipping_bar": {"limit_unreached_html": "Füge deiner Bestellung Artikel im Wert von {{ remaining_amount }} hinzu und erhalte kostenlosen Versand!", "limit_reached_html": "Deine Bestellung ist für kostenlosen Versand qualizifiert."}, "shipping_estimator": {"estimate_shipping": "Versandkosten berechnen", "country": "Land", "province": "Bundesland/Kanton", "zip": "PLZ", "estimate": "<PERSON><PERSON><PERSON><PERSON>", "no_results": "Tut uns leid, aber wir verschicken leider nicht an deine Adresse.", "one_result": "<PERSON><PERSON>r deine Adresse gibt es einen Versandtarif:", "multiple_results": "<PERSON>ür deine Adresse gibt es mehrere Versandtarife:", "error": "<PERSON><PERSON>nen der Versandkosten ist ein Fehler aufgetreten:"}}, "404": {"general": {"title": "Seite nicht gefunden", "continue_shopping": "<PERSON><PERSON> e<PERSON>"}}, "search": {"general": {"title": "<PERSON><PERSON>", "terms": "<PERSON><PERSON><PERSON> <PERSON> \"{{ terms }}\"", "search_placeholder": "Gib etwas ein...", "products": "Produkte", "suggestions": "Anregungen", "collections": "<PERSON><PERSON><PERSON>", "posts": "Blog-Artikel", "pages": "Seiten", "clear": "Z<PERSON>ücksetzen", "view_all": "Alle Treffer anzeigen", "view_all_results": "Alle Treffer anzeigen", "no_results": "<PERSON><PERSON>"}, "results_count": {"zero": "0 <PERSON><PERSON><PERSON> für \"{{ terms }}\"", "one": "1 Treffer für \"{{ terms }}\"", "other": "{{ count }} <PERSON><PERSON><PERSON> <PERSON> \"{{ terms }}\""}}, "gift_card": {"general": {"title": "Hier ist dein Geschenkgutschein", "copy": "<PERSON><PERSON><PERSON>", "print": "<PERSON><PERSON><PERSON>", "scan": "oder scanne diesen QR-Code", "back_to_store": "Zurück zum Shop"}, "issued": {"remaining_amount": "Restbetrag", "out_of_html": "von {{ initial_value }}", "redeem_instructions": "<PERSON>utze diesen Code, um deinen Geschenkgutschein an der Kasse einzulösen:", "code": "Gutscheincode", "expires_on": "Läuft ab am: {{ expires_on }}", "expired": "<PERSON><PERSON> Geschenkgutschein ist abgelaufen oder wurde deaktiviert.", "add_to_apple_wallet": "Zu deinem Apple Wallet hinzufügen"}, "recipient": {"checkbox": "Ich möchte dies als Geschenk senden", "email_label": "Empfänger E-Mail", "name_label": "Name des Empfängers (optional)", "send_on_label": "Senden am (optional)", "message_label": "<PERSON><PERSON><PERSON><PERSON> (optional)"}}, "password": {"general": {"follow_us": "Folge uns", "powered_by": "Dieser Shop wird betrieben mit", "store_owner": "<PERSON><PERSON><PERSON><PERSON>?", "login": "Anmelden"}, "storefront_access": {"enter_password": "Mit Passwort betreten", "store_access": "Shopzugang", "instructions": "Gib das Passwort unten ein, um den Shop zu betreten", "password": "Passwort", "enter_store": "Shop betreten"}}, "apps": {"shopify_reviews": {"review_info": "Dieses Produkt wurde mit {{ rating_value }} von {{ rating_max }} Sternen bewertet.", "leave_review": "Hinterlasse eine Bewertung", "review_count": {"zero": "Dieses Produkt hat noch keine Bewertungen.", "one": "Es hat {{ count }} Bewertung erhalten.", "other": "Es hat {{ count }} Bewertungen erhalten."}}}}