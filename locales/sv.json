{"general": {"page": "<PERSON><PERSON> {{ page }}", "home": "<PERSON><PERSON>", "accessibility": {"skip_to_content": "Hoppa till innehållet", "pagination": "Pagineringsnavigering", "go_to_page": "<PERSON><PERSON> till sida {{ index }}", "go_to_item": "G<PERSON> till {{ index }}", "item_nth_of_count": "Artikel {{ index }} av {{ count }}", "drag": "<PERSON>a", "close": "Stäng", "next": "<PERSON><PERSON><PERSON>", "previous": "Föregående", "play_video": "Spela upp video", "read_more": "<PERSON><PERSON><PERSON>r"}, "label": {"color": "<PERSON><PERSON><PERSON>", "white": "Vit", "size": "Storlek"}, "social": {"follow_on": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> {{ social_media }}", "share": "Dela", "share_on": "<PERSON><PERSON> på {{ social_media }}", "share_email": "Dela via e-post"}, "rating": {"info": "{{ rating_value }} av {{ rating_max }} stjärnor"}, "newsletter": {"email": "E-post", "subscribe": "Prenumerera", "notify_me": "Meddela mig", "subscribed_successfully": "Du har nu blivit prenumerant på vårt nyhetsbrev."}, "localization": {"country": "Land", "language": "Språk", "change_country_accessibility_text": "Byt land eller valuta", "change_language_accessibility_text": "<PERSON><PERSON> s<PERSON>"}, "privacy_bar": {"accept": "Godkä<PERSON>", "decline": "Neka"}, "form": {"max_characters": "<PERSON>. {{ max_chars }} tecken"}, "on_boarding": {"blog_post_category": "<PERSON><PERSON><PERSON>", "blog_post_title": "Artikel", "blog_post_excerpt": "Skriv en text om ditt blogginlägg.", "product_vendor": "Leverantör", "product_title": "Produkt", "product_description": "Skriv en text om din produkt.", "collection_title": "<PERSON><PERSON>"}}, "header": {"general": {"account": "Ko<PERSON>", "login": "Logga in", "menu": "<PERSON><PERSON>", "search": "<PERSON>ö<PERSON>", "cart": "Varukorg"}}, "product": {"general": {"description": "Beskrivning", "view_product": "Visa produkt", "quick_add": "+ <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "add_to_cart_short": "+ <PERSON><PERSON><PERSON> till", "add_to_cart_button": "Lägg i varukorgen", "pre_order_button": "Förboka", "sold_out_button": "<PERSON><PERSON><PERSON><PERSON>", "unavailable_button": "Inte tillgänglig", "added_to_cart": "<PERSON><PERSON><PERSON> i din varuk<PERSON>g!", "sold_out_badge": "<PERSON><PERSON><PERSON><PERSON>", "on_sale_badge": "Till salu", "discount_badge_html": "Spara {{savings }}", "sku": "SKU:", "variant": "<PERSON><PERSON><PERSON>", "view_in_space": "Visa i ditt utrymme", "taxes_included": "Inkl. moms.", "taxes_excluded": "Exkl. moms.", "shipping_policy_html": "<a href=\"{{ link }}\" class=\"link\">Frakt</a> beräknas i kassan", "size_chart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "available_colors_count": {"one": "{{ count }} f<PERSON><PERSON>", "other": "{{ count }} färger tillgängliga"}}, "gallery": {"close": "Stäng bildgalleriet", "zoom": "Zooma", "error": "<PERSON><PERSON>n kan inte laddas"}, "price": {"regular_price": "<PERSON><PERSON>", "sale_price": "REA-pris", "from_price_html": "<PERSON><PERSON><PERSON> {{ price_min }}"}, "quantity": {"label": "<PERSON><PERSON>", "increase_quantity": "Ö<PERSON> antal", "decrease_quantity": "Minska antal", "minimum_of": "Minimum av {{ min }}", "maximum_of": "Maximum av {{ max }}", "increment_of": "Ökning med {{ step }}"}, "volume_pricing": {"title": "Volymprissättning", "minimum": "<PERSON>ler än {{ quantity }}", "price_at_each": "för {{ price }}/styck"}, "rating_count": {"zero": "{{ count }} recensioner", "one": "{{ count }} recension", "other": "{{ count }} recensioner"}, "inventory": {"in_stock": "I lager", "oversell_stock": "<PERSON>mmer snart in i lager", "incoming_stock": "Kommer in i lager {{ next_incoming_date }}", "low_stock_with_quantity_count": {"one": "Endast {{count}} st kvar", "other": "Endast {{count}} st kvar"}}, "store_availability": {"view_store_info": "Visa butiksinformation", "check_other_stores": "Kontrollera tillgängligheten i andra butiker", "pick_up_available": "Upphämtning tillgänglig", "pick_up_currently_unavailable": "Upphämtning för nä<PERSON>rande inte tillgänglig", "pick_up_available_at": "Upphämtning tillgänglig på {{ location_name }}", "pick_up_unavailable_at": "Upphämtning för nä<PERSON>e inte tillgänglig på {{ location_name }}"}}, "collection": {"general": {"empty_collection": "Den här samlingen är tom", "all_collections": "<PERSON>a sam<PERSON>", "no_collections": "Den här butiken har ingen samling.", "continue_shopping": "Fortsätt handla"}, "products_count": {"zero": "0 produkter", "one": "1 produkt", "other": "{{ count }} produkter"}, "faceting": {"filters": "<PERSON><PERSON><PERSON>", "filter_and_sort": "Filtrera och sortera", "filter_button": "<PERSON><PERSON><PERSON>", "clear_filters": "<PERSON><PERSON> alla", "apply_filters": "Tillämpa", "sort_by": "Sortera efter", "remove_filter": "Ta bort filtret \"{{ namn }}\"", "no_results": "Inga produkter matchar dessa filter.", "price_range_to": "till", "price_filter_from": "<PERSON><PERSON><PERSON> p<PERSON>", "price_filter_to": "<PERSON> pris", "price_filter": "{{ min_price }} - {{ max_price }}", "availability_label": "Endast produkter i lager"}}, "blog": {"general": {"empty_blog": "Den här bloggen är tom", "back_to_home": "Tillbaka till hem", "view": "Se", "all_posts": "Alla <PERSON>"}, "post": {"written_by": "Av {{ author }}", "share": "Dela", "tags": "Taggar", "continue_reading": "<PERSON><PERSON><PERSON>"}, "comments": {"leave_comment": "Lämna en kommentar", "moderated": "Alla kommentarer modereras innan de publiceras.", "name": "<PERSON><PERSON>", "email": "E-post", "message": "Meddelande", "submit": "<PERSON><PERSON><PERSON> in", "comment_sent": "Din kommentar har skickats. Det kommer att synas när butiksägaren har accepterat det!", "comment_published": "<PERSON> kommentar har publicerats.", "count": {"zero": "{{ count }} kommentarer", "one": "{{ count }} kommentar", "other": "{{ count }} kommentarer"}}}, "contact": {"form": {"name": "namn", "email": "E-post", "message": "Meddelande", "submit": "<PERSON><PERSON><PERSON> medd<PERSON>", "success_message": "<PERSON>tt meddelande har skickats."}}, "customer": {"account": {"welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {{first_name}}", "tagline": "Se alla dina beställningar och hantera din kontoinformation.", "orders": "Order", "addresses": "<PERSON><PERSON><PERSON>", "logout": "Logga ut", "no_orders": "Du har inte lagt några beställningar än.", "continue_shopping": "Fortsätt handla"}, "login": {"title": "Logga in", "email": "E-post", "password": "L<PERSON>senord", "submit": "Logga in", "forgot_password": "Glömt ditt lösenord?", "sign_up": "<PERSON><PERSON>"}, "recover_password": {"title": "Återställa lösenord", "email": "E-post", "submit": "Ta igen sig", "back_to_login": "Tillbaka till login", "success_message": "Ett e-postmeddelande har skickats till din adress med instruktioner för att återställa ditt lösenord."}, "register": {"title": "<PERSON><PERSON>", "first_name": "Förnamn", "last_name": "E<PERSON>nam<PERSON>", "email": "E-post", "password": "L<PERSON>senord", "accepts_marketing": "Registrera dig till vårt nyhetsbrev", "submit": "Skapa konto", "login": "Logga in"}, "activate_account": {"title": "Aktivera konto", "instructions": "Ange ett lösenord för att skapa ditt konto:", "password": "L<PERSON>senord", "password_confirmation": "Bekräfta lösenord", "submit": "Aktivera", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reset_password": {"title": "Återställ lösenord", "password": "L<PERSON>senord", "password_confirmation": "Bekräfta lösenord", "submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "order": {"order": "Ordning", "order_name": "<PERSON><PERSON><PERSON> {{name}}", "view_details": "Se orderdetaljer", "back": "Tillbaka", "date": "Datum", "payment_status": "<PERSON><PERSON><PERSON><PERSON>", "fulfillment_status": "Leveransstatus", "cancelled_on": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> den {{date}}. Orsak: {{reason}}", "product": "Produkt", "quantity": "<PERSON><PERSON>", "fulfillment_with_number": "<PERSON>ning har skickats den {{date}}. Spåra försändelsen med spårningsnummernummer {{tracking_number}}.", "fulfillment": "<PERSON>ning har skickats {{date}}.", "track_shipment": "<PERSON><PERSON><PERSON><PERSON>", "subtotal": "Delsumma", "discount": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON>", "taxes_included": "Inkl. moms", "taxes_excluded": "Exkl. moms", "total_duties": "<PERSON><PERSON>", "refunded_amount": "Återbetalat belopp", "total": "Totalt", "shipping_address": "Leveransadress", "billing_address": "Fakturaadress"}, "addresses": {"no_addresses": "Du har inte sparat några adresser än.", "add_address": "<PERSON><PERSON><PERSON> till adress", "edit_address": "<PERSON><PERSON> ad<PERSON>", "save_address": "<PERSON>ra adress", "edit": "<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "default_address": "Standardadress", "address_title": "Adress {{ position }}", "delete_confirm": "Vill du ta bort den här adressen? Åtgärden kan inte ångras.", "fill_form": "Vänligen fyll i informationen nedan:", "first_name": "Förnamn", "last_name": "E<PERSON>nam<PERSON>", "company": "Företag", "phone": "Telefonnummer", "address1": "<PERSON>ress", "address2": "Adress 2", "city": "Stad", "zip": "Postnummer", "country": "Land", "province": "<PERSON><PERSON><PERSON>", "set_default": "<PERSON><PERSON><PERSON> in som standardadress"}}, "cart": {"general": {"title": "Varukorg", "empty": "<PERSON> är tom", "item_count": {"one": "{{ count }} artikel", "other": "{{ count }} artiklar"}, "continue_shopping": "Fortsätt handla", "weight": "<PERSON><PERSON><PERSON>", "subtotal": "Delsumma", "total": "Totalt", "taxes_and_shipping_policy_at_checkout_html": "<PERSON><PERSON> och <a href=\"{{ link }}\" class=\"link\">frakt</a> ber<PERSON>knas i kassan", "taxes_included_but_shipping_at_checkout": "<PERSON><PERSON> ing<PERSON>r och frakt beräknas i kassan", "taxes_included_and_shipping_policy_html": "<PERSON><PERSON> ingår. <a href=\"{{ link }}\" class=\"link\">Frakt</a> ber<PERSON>knas i kassan.", "taxes_and_shipping_at_checkout": "Moms och frakt beräknas i kassan", "add_order_note": "Lägg till orderkommentar", "edit_order_note": "<PERSON><PERSON><PERSON> ditt <PERSON>kommentar", "order_note": "Orderkommentar", "save_note": "Spara", "view_cart": "Visa varukorg", "checkout": "<PERSON><PERSON>", "we_accept": "Vi accepterar"}, "order": {"product": "Produkt", "total": "Totalt", "quantity": "<PERSON><PERSON>", "change_quantity": "<PERSON><PERSON> antal", "increase_quantity": "Ö<PERSON> antal", "decrease_quantity": "Minska antal", "remove": "<PERSON> bort", "remove_with_title": "Ta bort {{ title }}"}, "free_shipping_bar": {"limit_unreached_html": "<PERSON><PERSON><PERSON><PERSON> för {{ remaining_amount }} till och få gratis frakt!", "limit_reached_html": "Du får fri frakt!"}, "shipping_estimator": {"estimate_shipping": "Uppskatta frakt", "country": "Land", "province": "<PERSON><PERSON><PERSON>", "zip": "Postnummer", "estimate": "Uppskatta", "no_results": "<PERSON><PERSON><PERSON><PERSON> skickar vi inte till din adress.", "one_result": "Det finns en fraktkostnad för din adress:", "multiple_results": "Det finns flera fraktpriser för din adress:", "error": "<PERSON><PERSON> eller flera fel uppstod vid hämtning av fraktpriser:"}}, "404": {"general": {"title": "<PERSON><PERSON> kan inte hittas", "continue_shopping": "Fortsätt handla"}}, "search": {"general": {"title": "<PERSON>ö<PERSON>", "terms": "Resultat för \"{{ terms }}\"", "search_placeholder": "Skriv något...", "products": "<PERSON>du<PERSON><PERSON>", "suggestions": "Förslag", "collections": "<PERSON><PERSON><PERSON>", "posts": "Blogginlägg", "pages": "<PERSON><PERSON>", "clear": "<PERSON> bort", "view_all": "Visa alla", "view_all_results": "Visa alla resultat", "no_results": "Inga resultat kunde hittas."}, "results_count": {"zero": "0 result för \"{{ terms }}\"", "one": "1 resultat för \"{{ terms }}\"", "other": "{{ count }} result för \"{{ terms }}\""}}, "gift_card": {"general": {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>tt <PERSON>", "copy": "<PERSON><PERSON><PERSON>", "print": "Skriva ut", "scan": "eller skanna den här QR-koden", "back_to_store": "Tillbaka till butiken"}, "issued": {"remaining_amount": "<PERSON><PERSON>tåend<PERSON> belopp", "out_of_html": "av {{ initial_value }}", "redeem_instructions": "<PERSON><PERSON><PERSON>nd denna kod för att lösa in ditt presentkort i kassan:", "code": "Presentkortskod", "expires_on": "<PERSON><PERSON><PERSON> ut den: {{ expires_on }}", "expired": "<PERSON><PERSON> present<PERSON>t har löpt ut eller har inaktiverats.", "add_to_apple_wallet": "Lägg till i Apple Wallet"}, "recipient": {"checkbox": "Jag vill skicka detta som en gåva", "email_label": "Mottagarens e-postadress", "name_label": "Mo<PERSON><PERSON>ens namn (valfritt)", "send_on_label": "<PERSON><PERSON><PERSON> den (valfritt)", "message_label": "Meddela<PERSON> (valfritt)"}}, "password": {"general": {"follow_us": "<PERSON><PERSON><PERSON><PERSON> oss", "powered_by": "<PERSON>na butik drivs av", "store_owner": "Butiksägare?", "login": "Logga in"}, "storefront_access": {"enter_password": "Ange med lösenord", "store_access": "Butiksåtkomst", "instructions": "<PERSON><PERSON> lösen<PERSON>et nedan för att komma åt butiken", "password": "L<PERSON>senord", "enter_store": "<PERSON><PERSON> in i butiken"}}, "apps": {"shopify_reviews": {"review_info": "Den här produkten har fått {{ rating_value }} av {{ rating_max }} stjärnor.", "leave_review": "Lämna en recension", "review_count": {"zero": "<PERSON> har fått {{ count }} recensioner.", "one": "<PERSON> har fått {{ count }} recension.", "other": "<PERSON> har fått {{ count }} recensioner."}}}}