<div class="benefits-section">
  <h2>{{ block.settings.title }}</h2>
  <ul class="benefits-list">
    {%- if block.settings.text_1 != blank -%}
      <li>
        {%- if block.settings.icon_1 != blank -%}
          <img src="{{ block.settings.icon_1 | img_url: '40x' }}" class="benefit-icon" alt="{{ block.settings.text_1 }}">
        {%- else -%}
          <img src="{{ 'check-badge.svg' | asset_url }}" class="benefit-icon" alt="{{ block.settings.text_1 }}">
        {%- endif -%}
        <span class="benefit-text">{{ block.settings.text_1 | newline_to_br }}</span>
      </li>
    {%- endif -%}
    {%- if block.settings.text_2 != blank -%}
      <li>
        {%- if block.settings.icon_2 != blank -%}
          <img src="{{ block.settings.icon_2 | img_url: '40x' }}" class="benefit-icon" alt="{{ block.settings.text_2 }}">
        {%- else -%}
          <img src="{{ 'check-badge.svg' | asset_url }}" class="benefit-icon" alt="{{ block.settings.text_2 }}">
        {%- endif -%}
        <span class="benefit-text">{{ block.settings.text_2 | newline_to_br }}</span>
      </li>
    {%- endif -%}
    {%- if block.settings.text_3 != blank -%}
      <li>
        {%- if block.settings.icon_3 != blank -%}
          <img src="{{ block.settings.icon_3 | img_url: '40x' }}" class="benefit-icon" alt="{{ block.settings.text_3 }}">
        {%- else -%}
          <img src="{{ 'check-badge.svg' | asset_url }}" class="benefit-icon" alt="{{ block.settings.text_3 }}">
        {%- endif -%}
        <span class="benefit-text">{{ block.settings.text_3 | newline_to_br }}</span>
      </li>
    {%- endif -%}
    {%- if block.settings.text_4 != blank -%}
      <li>
        {%- if block.settings.icon_4 != blank -%}
          <img src="{{ block.settings.icon_4 | img_url: '40x' }}" class="benefit-icon" alt="{{ block.settings.text_4 }}">
        {%- else -%}
          <img src="{{ 'check-badge.svg' | asset_url }}" class="benefit-icon" alt="{{ block.settings.text_4 }}">
        {%- endif -%}
        <span class="benefit-text">{{ block.settings.text_4 | newline_to_br }}</span>
      </li>
    {%- endif -%}
  </ul>
</div>

<style>
  .benefits-section {
    font-family: var(--text-font-family);
    color: rgb(var(--text-color));
    margin-bottom: var(--spacing-6);
  }

  .benefits-section h2 {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-4);
  }

  .benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
  }

  .benefits-list li {
    width: calc(50% - var(--spacing-2));
    display: flex;
    align-items: center;
  }

  .benefit-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-2);
  }

  .benefit-text {
    text-transform: uppercase; 
  }

  .benefit-text {
    font-size: var(--text-sm);
  }
  

  @media screen and (max-width: 699px) {
    .benefits-list li {
      width: calc(50% - var(--spacing-2)); /* Keep the same width as desktop */
    }
    .benefit-text {
      font-size: var(--text-xs);
    }

    .benefits-list {
      gap: var(--spacing-2); /* Smaller gap for mobile */
    }
  }
</style>
