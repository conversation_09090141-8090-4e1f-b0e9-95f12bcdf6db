/* Custom styles for the sticky add to cart button */

/* Button loader animation */
.button__loader {
  gap: var(--spacing-1-5);
  opacity: 0;
  pointer-events: none;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%; /* Ensure full height */
  transition: opacity 0.2s ease; /* Smooth transition */
  will-change: opacity; /* Optimize for animation */
}

.button__loader > * {
  width: 10px; /* Larger dots for better visibility */
  height: 10px; /* Larger dots for better visibility */
  border-radius: 50%; /* Ensure perfect circles */
  background: currentColor;
  margin: 0 3px; /* Add spacing between dots */
}
.product-quick-add {
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  padding: 0 !important;
  background-color: #E7E8E5 !important;
  border-radius: 0 !important;
  border: none !important;
  display: flex !important;
  justify-content: center !important; /* Changed from space-between to center */
  align-items: center !important;
  z-index: 100 !important;
  transform: translateY(100%) !important;
  transition: transform 0.3s ease-in-out !important;
}

.product-quick-add.is-visible {
  transform: translateY(0) !important;
  box-shadow: 0 -4px 10px rgba(0,0,0,0.2) !important; /* Added shadow at the top of the sticky add-to-cart */
}

.product-quick-add__container {
  display: flex;
  width: 100%;
  max-width: calc(var(--container-max-width) + 60px); /* Add 60px (30px padding on each side) to container max width */
  margin: 0 auto;
  padding: 12px 30px; /* Reduced top and bottom padding from 20px to 12px */
  justify-content: space-between;
  align-items: center;
  min-height: 64px; /* Reduced min-height to match reduced padding */
  flex-wrap: nowrap; /* Prevent wrapping */
  position: relative; /* For absolute positioning of children if needed */
}

.product-quick-add__left {
  display: flex;
  align-items: center;
  gap: 10px; /* Reduced gap */
  min-width: 0; /* Allow content to shrink */
  flex: 0 0 auto; /* Don't grow, don't shrink, auto basis */
  justify-content: flex-start; /* Left align */
  width: 40%; /* Fixed width to ensure consistent layout */
}

.product-quick-add__center {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Align to the right */
  flex: 1 1 auto; /* Grow, shrink, auto basis */
  padding-right: 20px; /* Add padding on the right side */
}

.product-quick-add__right {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Align to the right */
  gap: 0; /* Remove gap */
  flex: 0 0 auto; /* Don't grow, don't shrink, auto basis */
}

.product-quick-add__image-container {
  position: relative;
  width: 180px; /* Increased width for larger image */
  height: 180px; /* Increased height for larger image */
  margin-right: 20px;
  margin-top: -120px; /* Adjusted negative margin for larger image */
  z-index: 101; /* Ensure it appears above the bar */
  display: flex;
  justify-content: center; /* Center the image horizontally */
}

.product-quick-add__image {
  width: 180px; /* Match container width */
  height: 220px; /* Increased height for larger image */
  object-fit: contain; /* Changed from cover to contain to show the full image */
  object-position: center top; /* Align to the top of the image */
  border-radius: 0;
  position: relative; /* Changed from absolute to work with flex container */
  bottom: 0; /* Adjusted to align with the container */
}

.product-quick-add__title {
  color: #2A2A2A;
  font-weight: 600;
  font-size: 18px; /* Slightly smaller font size */
  margin: 0;
  font-family: var(--heading-font-family);
  line-height: 1.2; /* Improved line height for larger font */
  white-space: nowrap; /* Prevent wrapping */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Add ellipsis for overflow */
  max-width: 150px; /* Limit width */
}

.product-quick-add__price-container {
  margin: 5px 0 0;
}

.product-quick-add__price {
  color: #2A2A2A;
  font-size: 16px; /* Slightly smaller font size */
  font-weight: 600; /* Make it bolder */
  margin: 0; /* Reset margin */
  font-family: var(--heading-font-family); /* Match font family with other text */
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 6px; /* Space between price and badge */
  flex-wrap: nowrap;
}

.save-badge {
  background-color: #2A2A2A;
  color: #E7E8E5;
  font-size: 10px; /* Smaller font size */
  font-weight: bold;
  padding: 2px 6px; /* Smaller padding */
  border-radius: 3px; /* Smaller border radius */
  display: inline-block;
  font-family: var(--heading-font-family);
  white-space: nowrap;
  line-height: 1;
}

.product-quick-add__price .sale-price {
  color: #2A2A2A; /* Changed from green to black */
  margin-right: 2px; /* Reduced margin to bring prices closer */
}

.product-quick-add__price .compare-price {
  text-decoration: line-through;
  opacity: 0.7;
  font-size: 14px; /* Slightly smaller than the sale price */
  margin-right: 6px; /* Add space before the SAVE badge */
}

.product-quick-add__button {
  background-color: #47DE47 !important; /* Green button */
  color: #2A2A2A !important;
  border: none;
  border-radius: var(--rounded-button); /* Match border-radius from regular button */
  padding: 12px 60px; /* Increased horizontal padding for wider button */
  font-weight: 700;
  font-size: 20px; /* Larger font size */
  cursor: pointer;
  white-space: nowrap;
  height: 50px; /* Taller button */
  font-family: var(--heading-font-family) !important;
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease; /* Specific transitions */
  width: 100%; /* Use percentage width instead of fixed width */
  max-width: 550px; /* Significantly increased maximum width for desktop */
  text-align: center; /* Center the text */
  display: flex;
  justify-content: center; /* Center horizontally */
  align-items: center; /* Center vertically */
  margin: 0; /* Remove margin */
  box-shadow: 0 3px 6px rgba(0,0,0,0.15); /* Enhanced shadow for emphasis */
  position: relative; /* For absolute positioning of loader */
  min-height: 50px; /* Ensure minimum height is maintained */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Desktop purchase options */
.product-quick-add__purchase-options,
.desktop-purchase-options {
  display: flex;
  gap: 10px; /* Slightly increased gap */
  flex-wrap: nowrap; /* Prevent wrapping */
  width: auto; /* Only take as much width as needed */
  justify-content: flex-end; /* Align to the right */
}

.product-quick-add__option {
  background-color: transparent;
  color: #2A2A2A;
  border: 2px solid #2A2A2A; /* Match 2px border from product page */
  border-radius: var(--rounded-button); /* Match border-radius from product page */
  padding: 8px 15px; /* Increased padding */
  font-size: 12px; /* Slightly larger font size */
  cursor: pointer;
  white-space: nowrap;
  height: 38px; /* Taller height to fit text better */
  font-family: 'Meltmino', sans-serif !important; /* Changed to Meltmino font */
  transition: all 0.3s ease;
  text-transform: uppercase; /* Make text all capitals */
  min-width: 180px; /* Ensure enough width for full text */
  display: flex; /* Use flexbox for centering */
  justify-content: center; /* Center text horizontally */
  align-items: center; /* Center text vertically */
  text-align: center; /* Ensure text is centered */
}

.product-quick-add__option.selected {
  background-color: #2A2A2A;
  color: #FFFFFF;
  border-color: #2A2A2A; /* Match border color to background */
}

/* Medium screens */
@media screen and (max-width: 1024px) and (min-width: 769px) {
  .product-quick-add__title {
    max-width: 120px; /* Smaller max-width for medium screens */
  }

  .product-quick-add__option {
    font-size: 11px; /* Slightly smaller font for medium screens */
    padding: 8px 10px; /* Adjusted padding for medium screens */
    min-width: 160px; /* Ensure enough width for text on medium screens */
  }

  .product-quick-add__button {
    font-size: 18px; /* Keep font size reasonably large */
    padding: 10px 45px; /* Increased horizontal padding for wider button */
    max-width: 480px; /* Increased max-width on medium screens to maintain proportion */
    height: 46px; /* Slightly reduced height for medium screens */
  }
}

/* Hide mobile elements by default */
.mobile-purchase-container,
.mobile-buttons-container {
  display: none; /* Hidden by default */
}

/* Mobile screens */
@media screen and (max-width: 768px) {
  .product-quick-add__container {
    flex-direction: column;
    gap: 0px; /* Reduced gap for more compact layout */
    padding: 10px 0; /* Reduced vertical padding */
    height: auto;
    border-radius: 15px 15px 0 0;
  }

  /* Hide the image, product title and price on mobile */
  .product-quick-add__image-container,
  .product-quick-add__title,
  .product-quick-add__price {
    display: none;
  }

  .product-quick-add__left,
  .product-quick-add__center,
  .product-quick-add__right {
    width: 100%;
    justify-content: center;
  }

  .product-quick-add__left {
    margin-left: 0; /* Reset margin since image is hidden */
    display: none; /* Hide the entire left section on mobile */
  }

  /* Show mobile purchase container and hide desktop options */
  .mobile-purchase-container {
    display: flex;
    width: 100%;
    justify-content: space-between; /* Space between price and buttons */
    align-items: center;
    margin-bottom: 5px; /* Reduced margin for more compact layout */
    padding: 0; /* Remove padding to align with Secure the Bag button */
  }

  /* Mobile buttons container */
  .mobile-buttons-container {
    display: flex;
    justify-content: flex-end; /* Align to the right */
    align-items: center;
    gap: 0; /* No gap between buttons */
    margin-bottom: 10px; /* Reduced margin for more compact layout */
    width: 100%; /* Take full width */
  }

  /* Hide desktop options on mobile */
  .desktop-purchase-options {
    display: none;
  }

  /* Show mobile elements on mobile */
  .mobile-purchase-container,
  .mobile-buttons-container {
    display: flex;
  }

  /* Mobile price display */
  .mobile-price-display {
    display: flex;
    flex-direction: row; /* Changed from column to row */
    align-items: center; /* Center items vertically */
    flex: 0 0 auto; /* Don't grow or shrink */
    gap: 8px; /* Increased space between prices and badge */
    margin-left: 15px; /* Add left margin to align exactly with Secure the Bag button */
  }

  /* Mobile price wrapper */
  .mobile-price-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 5px;
  }

  /* Mobile save badge */
  .mobile-save-badge {
    background-color: #2A2A2A;
    color: #E7E8E5;
    font-size: 10px;
    font-weight: bold;
    padding: 4px 6px;
    border-radius: 3px;
    display: inline-block;
    font-family: var(--heading-font-family);
    white-space: nowrap;
    line-height: 1;
  }

  .mobile-current-price {
    font-size: 20px; /* Slightly smaller font */
    font-weight: 700;
    color: #2A2A2A;
    font-family: var(--heading-font-family);
    line-height: 1.1; /* Tighter line height */
  }

  .mobile-compare-price {
    font-size: 14px; /* Smaller font */
    text-decoration: line-through;
    opacity: 0.7;
    color: #2A2A2A;
    line-height: 1; /* Tighter line height */
    margin-top: 2px; /* Slight adjustment for vertical alignment */
  }

  /* Mobile purchase options */
  .mobile-purchase-options {
    display: flex;
    gap: 8px;
    align-items: center; /* Center buttons vertically */
    justify-content: flex-end; /* Align buttons to the right */
  }

  .product-quick-add__purchase-options {
    width: 100%;
    justify-content: space-between;
    margin: 10px 0 8px; /* Reduced spacing: 10px top, 8px bottom */
  }

  .product-quick-add__option {
    width: auto; /* Auto width based on content */
    min-width: 100px; /* Minimum width */
    text-align: center;
    padding: 6px 12px; /* Reduced vertical padding */
    border-radius: var(--rounded-button); /* Match border-radius from product page */
    font-size: 12px; /* Smaller font size for mobile */
    height: 32px; /* Reduced height for more compact layout */
    border: 2px solid #2A2A2A; /* Match border color to desktop version */
    color: #2A2A2A; /* Match text color to desktop version */
    font-family: 'Meltmino', sans-serif !important; /* Ensure Meltmino font on mobile */
    text-transform: uppercase; /* Make text all capitals */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Style mobile buttons */
  .mobile-buttons-container .product-quick-add__option {
    width: auto; /* Width based on content */
    min-width: 90px; /* Minimum width for each button */
    border-radius: 0; /* Remove border radius for adjacent buttons */
  }

  /* Add border radius only to the outer corners */
  .mobile-buttons-container .product-quick-add__option:first-child {
    border-radius: var(--rounded-button) 0 0 var(--rounded-button); /* Left side rounded */
    border-right: 1px solid #2A2A2A; /* Add divider */
  }

  .mobile-buttons-container .product-quick-add__option:last-child {
    border-radius: 0 var(--rounded-button) var(--rounded-button) 0; /* Right side rounded */
    border-left: 1px solid #2A2A2A; /* Add divider */
  }

  .product-quick-add__option.selected {
    background-color: #2A2A2A; /* Black background for selected option */
    border-color: #2A2A2A; /* Match border color to background */
    color: #FFFFFF; /* White text for better contrast */
    font-weight: bold; /* Keep selected option bold */
  }

  .product-quick-add__button {
    width: calc(100% - 30px); /* Width with 15px padding on each side */
    border-radius: var(--rounded-button); /* Match border-radius from regular button */
    padding: 12px var(--spacing-8); /* Increased vertical padding for better height */
    font-size: 1.2em; /* Exact match to original button font size */
    font-weight: 700; /* Match original button font weight */
    background-color: #47DE47 !important; /* Green button for mobile */
    color: #2A2A2A !important; /* Dark text on green button */
    margin: 0 15px 8px; /* Reduced bottom margin for more compact layout */
    display: block; /* Ensure it takes full width */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow for depth */
    text-align: center; /* Center the text inside the button */
    min-width: 0; /* Remove minimum width to prevent overflow */
    max-width: 100%; /* Ensure it doesn't exceed container width */
    font-family: var(--heading-font-family) !important; /* Match original button font family */
    line-height: 1.6; /* Match original button line height */
    height: auto;
    position: relative; /* For absolute positioning of loader */
    min-height: 50px; /* Ensure minimum height is maintained */
    display: flex; /* Use flexbox for better content alignment */
    align-items: center; /* Center content vertically */
    justify-content: center; /* Center content horizontally */
    box-sizing: border-box; /* Include padding in width calculation */
  }
}
