{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
ACCORDION COMPONENT
----------------------------------------------------------------------------------------------------------------------

This component is used to create an accordion (also called as collapsible). It internally uses the "details" HTML
tag so that it can also be used without any JavaScript.

********************************************
Supported parameters
********************************************

* title: the title to use for the toggle button
* icon: an optional icon attached to the title
* content: the hidden content inside the accordion
* open: if set to true the accordion is open by default
* size: if set to "lg", the font is and spacing is bigger
* class: any additional class
* id: an optional ID for the accordion
{%- endcomment -%}

<details {% if id %}id="{{ id | escape }}"{% endif %} class="{{ class }} accordion {% if size %}accordion--{{ size }}{% endif %} group" aria-expanded="{% if open %}true{% else %}false{% endif %}" is="accordion-disclosure" {% if open %}open{% endif %} {{ block.shopify_attributes }}>
  <summary>
    {%- comment -%}iOS 14 does not support flex on the summary itself, so we have to use this extra div{%- endcomment -%}
    <div class="accordion__toggle bold">
      {%- if icon -%}
        <div class="text-with-icon">
          {%- render 'icon' with icon -%}
          <span {% if size == 'lg' %}class="h6"{% endif %}>{{- title | escape -}}</span>
        </div>
      {%- else -%}
        <span {% if size == 'lg' %}class="h6"{% endif %}>{{- title | escape -}}</span>
      {%- endif -%}

      <span class="circle-chevron group-expanded:rotate">
        <svg class="progress-bottom-link-plus" width="12px" height="12px" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14.505 6.3C14.665 6.3 14.745 6.385 14.745 6.555V8.775C14.745 8.945 14.665 9.03 14.505 9.03H9.27V14.895C9.27 15.065 9.19 15.15 9.03 15.15H6.705C6.545 15.15 6.465 15.065 6.465 14.895V9.03H1.2C1.04 9.03 0.96 8.945 0.96 8.775V6.555C0.96 6.385 1.04 6.3 1.2 6.3H6.465V0.404999C6.465 0.234999 6.545 0.149999 6.705 0.149999H9.03C9.19 0.149999 9.27 0.234999 9.27 0.404999V6.3H14.505Z" fill="#241F23"/>
        </svg>
          
        <svg class="progress-bottom-link-minus" xmlns="http://www.w3.org/2000/svg" width="8" height="3" viewBox="0 0 8 3" fill="none">
        <path d="M0.495 2.695C0.325 2.695 0.24 2.61 0.24 2.44V0.37C0.24 0.209999 0.325 0.129999 0.495 0.129999H7.515C7.685 0.129999 7.77 0.209999 7.77 0.37V2.44C7.77 2.52 7.745 2.585 7.695 2.635C7.655 2.675 7.595 2.695 7.515 2.695H0.495Z" fill="#241F23"/>
        </svg>
      </span>
    </div>
  </summary>

  <div class="accordion__content">
    {{- content -}}
  </div>
</details>