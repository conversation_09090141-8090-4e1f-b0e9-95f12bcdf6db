{%- comment -%}
  hero-image-preload.liquid
  Dynamic hero image preloading for optimal performance
  Automatically detects and preloads the first hero image from any hero section
{%- endcomment -%}

{%- if template.name == 'index' -%}
  {%- comment -%}Ultra-aggressive mobile-first hero image preloading{%- endcomment -%}
  {%- for section in sections -%}
    {%- if section.type == 'images-with-text-scrolling-hero' or section.type contains 'hero' or section.type contains 'banner' -%}
      {%- for block in section.blocks -%}
        {%- if forloop.first -%}
          {%- comment -%}Maximum resolution mobile image preload for 4x+ pixel density{%- endcomment -%}
          {%- if block.settings.mobile_image != blank -%}
            <link rel="preload"
                  href="{{ block.settings.mobile_image | image_url: width: 1200 }}"
                  as="image"
                  fetchpriority="high"
                  media="(max-width: 740px)">
          {%- elsif block.settings.image != blank -%}
            <link rel="preload"
                  href="{{ block.settings.image | image_url: width: 1200 }}"
                  as="image"
                  fetchpriority="high"
                  media="(max-width: 740px)">
          {%- endif -%}

          {%- comment -%}Simple desktop hero image preload{%- endcomment -%}
          {%- if block.settings.image != blank -%}
            <link rel="preload"
                  href="{{ block.settings.image | image_url: width: 800 }}"
                  as="image"
                  fetchpriority="high"
                  media="(min-width: 741px)">
          {%- endif -%}

          {%- comment -%}Only preload the first hero image, then break{%- endcomment -%}
          {%- break -%}
        {%- endif -%}
      {%- endfor -%}
      {%- comment -%}Found a hero section, stop looking{%- endcomment -%}
      {%- break -%}
    {%- endif -%}
  {%- endfor -%}
{%- endif -%}
