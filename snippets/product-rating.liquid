{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
PRODUCT RATING
----------------------------------------------------------------------------------------------------------------------

This component generates a review badge from the product metafields information.

********************************************
Supported variables
********************************************

* product: the product from which reviews are extracted
* show_empty: if set to true, the theme shows a 0.0 if there are no rating yet
* show_placeholder: if set to true, show random value (useful for the product card placeholder)
* display_mode: either "rating" (e.g.: 3.5) or "count" (e.g.: 4 reviews). Default to "rating" if none is passed
* class: extra class to add
* block: the block settings from the section schema
{%- endcomment -%}

<style>
  .star-rating__text {
    {% if block.settings.rating_text_font_family %}
    font-family: '{{ block.settings.rating_text_font_family }}';
    {% endif %}
    {% if block.settings.rating_text_size %}
    font-size: var(--{{ block.settings.rating_text_size }});
    {% else %}
    font-size: var(--text-sm);
    {% endif %}
    {% if block.settings.rating_text_weight %}
    font-weight: {{ block.settings.rating_text_weight }};
    {% endif %}
    align-items: center;
    margin-left: 4px;
    line-height: 20px;
    margin-top: 8px; 
  }
  .rating__stars {
    display: flex;
    align-items: center;
  }
  .rating {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 18px !important;
    margin-top: 10px !important;
  }
</style>

{%- assign rating_max = 5 -%}

{%- if product.metafields.reviews.rating.value != blank -%}
  {%- assign rating_value = product.metafields.reviews.rating.value.rating | round: 1 -%}
  {%- assign rating_count = product.metafields.reviews.rating_count.value -%}
  {%- assign rating_max = product.metafields.reviews.rating.value.scale_max -%}
{%- elsif show_empty -%}
  {%- assign rating_value = 4.9 -%}
  {%- assign rating_count = 1081 -%}
{%- elsif show_placeholder -%}
  {%- assign rating_value = 4.5 -%}
  {%- assign rating_count = 2 -%}
{%- else -%}
  {%- assign hide_rating = true -%}
{%- endif -%}

{%- unless hide_rating -%}
  {%- if request.page_type == 'product' and block != blank -%}
    {%- assign is_main_product = true -%}
  {%- endif -%}

  <a href="{% unless is_main_product %}{{ product.url }}{% endunless %}#shopify-product-reviews" class="rating {{ class }}" title="{{ 'product.rating_count' | t: count: rating_count }}" {{ block.shopify_attributes }}>
    {%- if display_mode == 'count' -%}
      <span class="text-sm">{{- 'product.rating_count' | t: count: rating_count -}}</span>
    {%- else -%}
      {% comment %} <span class="text-sm">{{ rating_value }}</span> {% endcomment %}
    {%- endif -%}

    <div class="rating__stars" role="img" aria-label="{{ 'general.rating.info' | t: rating_value: rating_value, rating_max: rating_max }}">
      {%- render 'icon' with 'rating-star', width: 20, height: 20 -%}
      {%- render 'icon' with 'rating-star', width: 20, height: 20 -%}
      {%- render 'icon' with 'rating-star', width: 20, height: 20 -%}
      {%- render 'icon' with 'rating-star', width: 20, height: 20 -%}
      {%- render 'icon' with 'rating-star', width: 20, height: 20 -%}
    </div>
    <div><span class="star-rating__text">{{ block.settings.rating_text | default: "Rated 4.9 | 27,956 Reviews" }}</span></div>
  </a>
{%- endunless -%}
