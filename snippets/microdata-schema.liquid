{%- comment -%}
This snippet structures the micro-data using JSON-LD specification. Starting from Prestige 10, we are using
the structured_data built-in filter to ensure that Shopify can keep it up to date without requiring theme upgrade. If
you believe some information for products are missing, please reach Shopify support directly, as it is no longer
managed at the theme level.
{%- endcomment -%}

{%- if request.page_type == 'product' -%}
  <script type="application/ld+json">
    {{- product | structured_data -}}
  </script>
{%- elsif request.page_type == 'article' -%}
  <script type="application/ld+json">
    {{- article | structured_data -}}
  </script>
{%- endif -%}

<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [{
        "@type": "ListItem",
        "position": 1,
        "name": {{ 'general.home' | t | json }},
        "item": "{{ shop.url }}"
      }

      {%- if request.page_type == 'product' -%}
        {%- if collection -%}
          ,{
            "@type": "ListItem",
            "position": 2,
            "name": {{ collection.title | json }},
            "item": "{{ shop.url }}{{ collection.url }}"
          }, {
            "@type": "ListItem",
            "position": 3,
            "name": {{ product.title | json }},
            "item": "{{ shop.url }}{{ product.url }}"
          }
        {%- else -%}
          ,{
            "@type": "ListItem",
            "position": 2,
            "name": {{ product.title | json }},
            "item": "{{ shop.url }}{{ product.url }}"
          }
        {%- endif -%}
      {%- elsif request.page_type == 'collection' -%}
          ,{
            "@type": "ListItem",
            "position": 2,
            "name": {{ collection.title | json }},
            "item": "{{ shop.url }}{{ collection.url }}"
          }
      {%- elsif request.page_type == 'blog' -%}
          ,{
            "@type": "ListItem",
            "position": 2,
            "name": {{ blog.title | json }},
            "item": "{{ shop.url }}{{ blog.url }}"
          }
      {%- elsif request.page_type == 'article' -%}
          ,{
            "@type": "ListItem",
            "position": 2,
            "name": {{ blog.title | json }},
            "item": "{{ shop.url }}{{ blog.url }}"
          }, {
            "@type": "ListItem",
            "position": 3,
            "name": {{ blog.title | json }},
            "item": "{{ shop.url }}{{ article.url }}"
          }
      {%- elsif request.page_type == 'page' -%}
        ,{
            "@type": "ListItem",
            "position": 2,
            "name": {{ page.title | json }},
            "item": "{{ shop.url }}{{ page.url }}"
          }
      {%- endif -%}
    ]
  }
</script>

{%- if request.page_type == 'index' -%}
  {%- assign potential_action_target = request.origin | append: routes.search_url | append: "?q={search_term_string}" -%}

  <script type="application/ld+json">
  [
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "url": {{ shop.url | append: page.url | json }},
      "potentialAction": {
        "@type": "SearchAction",
        "target": {{ potential_action_target | json }},
        "query-input": "required name=search_term_string"
      }
    },
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": {{ shop.name | json }},
      {%- if shop.brand.logo -%}
        "logo": {{ shop.brand.logo | image_url: width: shop.brand.logo.width | prepend: "https:" | json }},
      {%- endif -%}
      {%- if shop.brand.short_description -%}
        "description": {{ shop.brand.short_description | json }},
      {%- endif -%}
      {%- if shop.brand.slogan -%}
        "slogan": {{ shop.brand.slogan | json }},
      {%- endif -%}
      {%- if shop.brand.metafields.social_links.size > 0 -%}
        "sameAs": [
          {%- for social_link in shop.brand.metafields.social_links -%}
            {{- social_link.last.value | json -}}{%- unless forloop.last -%},{%- endunless -%}
          {%- endfor -%}
        ],
      {%- endif -%}
      "url": {{ shop.url | append: page.url | json }}
    }
  ]
  </script>
{%- endif -%}