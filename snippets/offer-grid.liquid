<div class="offer-grid {% if block.settings.desktop_layout == 'columns' %}offer-grid--columns{% endif %} {% if block.settings.mobile_layout == 'columns' %}offer-grid--mobile-columns{% endif %}">
  {%- for i in (1..3) -%}
    {%- assign title_key = 'title_' | append: i -%}
    {%- assign content_key = 'content_' | append: i -%}
    
    {%- if block.settings[title_key] != blank or block.settings[content_key] != blank -%}
      <div class="offer-grid__item">
        <div class="offer {% if block.settings.text_alignment == 'center' %}offer--center{% endif %} {{ block.settings.border_radius }}"
             style="background-color: {{ block.settings.background_color }};">
          <span class="offer__title {{ block.settings.title_font_size }}" 
                style="font-family: var(--{{ block.settings.title_font_family }}-font-family); 
                       font-weight: {{ block.settings.title_font_weight }};
                       color: {{ block.settings.title_color }};">
            {{ block.settings[title_key] }}
          </span>
          {%- if block.settings[content_key] != blank -%}
            <div class="offer__content prose {{ block.settings.content_font_size }}"
                 style="font-family: var(--{{ block.settings.content_font_family }}-font-family); 
                        font-weight: {{ block.settings.content_font_weight }};
                        color: {{ block.settings.content_color }};">
              {{ block.settings[content_key] }}
            </div>
          {%- endif -%}
        </div>
      </div>
    {%- endif -%}
  {%- endfor -%}
</div>

<style>
  .offer-grid {
    display: grid !important;
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    gap: 8px !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .offer-grid__item {
    min-width: 0 !important;
    width: 100% !important;
  }

  .offer {
    padding: 12px !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
  }

  .offer__title {
    display: block;
    text-align: center;
  }

  .offer__content {
    width: 100% !important;
    text-align: center;
  }

  /* Font size classes with higher specificity */
  .offer .text-xl,
  .offer__title.text-xl {
    font-size: var(--text-xl) !important;
    line-height: 1.2 !important;
  }

  .offer .text-lg,
  .offer__title.text-lg {
    font-size: var(--text-lg) !important;
    line-height: 1.2 !important;
  }

  .offer .text-base,
  .offer__title.text-base {
    font-size: var(--text-base) !important;
    line-height: 1.4 !important;
  }

  .offer .text-sm,
  .offer__title.text-sm {
    font-size: var(--text-sm) !important;
    line-height: 1.4 !important;
  }

  /* Border radius classes */
  .rounded-none {
    border-radius: 0;
  }

  .rounded {
    border-radius: 4px;
  }

  .rounded-lg {
    border-radius: 8px;
  }

  .rounded-xl {
    border-radius: 12px;
  }

  @media screen and (max-width: 999px) {
    .offer-grid {
      gap: 4px !important;
    }

    .offer {
      padding: 6px 4px !important;
    }

    /* Only apply small font size on mobile if screen width is very small */
    @media screen and (max-width: 375px) {
      .offer span,
      .offer .prose {
        font-size: 11px !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    .offer-grid--mobile-columns {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }

  @media screen and (min-width: 1000px) {
    .offer-grid--columns {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    .offer {
      padding: 16px !important;
      gap: 8px !important;
    }
  }
</style>
