{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
SHIPPING ESTIMATOR COMPONENT
---------------------------------------------------------------------------------------------------------------------

This component creates a shipping estimator block.

********************************************
Supported parameters
********************************************

None
{%- endcomment -%}

<shipping-estimator class="shipping-estimator">
  <form action="{{ routes.cart_url }}" class="shipping-estimator__form fieldset">
    {%- assign country_label = 'cart.shipping_estimator.country' | t -%}
    {%- assign province_label = 'cart.shipping_estimator.province' | t -%}
    {%- assign zip_label = 'cart.shipping_estimator.zip' | t -%}
    {%- assign estimate_button = 'cart.shipping_estimator.estimate' | t -%}

    <country-selector class="contents" {% if customer and customer.default_address %}country="{{ customer.default_address.country | escape }}"{% endif %}>
      {%- render 'select', label: country_label, options: country_option_tags, name: 'address[country]' -%}
      {%- render 'select', label: province_label, name: 'address[province]' -%}
    </country-selector>

    <div style="flex-basis: 120px">
      {%- render 'input', label: zip_label, name: 'address[zip]', autocapitalize: 'characters' -%}
    </div>

    {%- render 'button', type: 'submit', content: estimate_button, size: 'lg', secondary: true -%}
  </form>

  <span class="shipping-estimator__results" role="region" aria-live="polite" hidden><!-- Holder for results --></span>
</shipping-estimator>
