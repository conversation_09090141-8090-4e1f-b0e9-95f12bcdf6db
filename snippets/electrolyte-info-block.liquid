{%- comment -%}
This snippet replaces the offer_grid block for displaying electrolyte information
Parameters:
- block: The block containing the settings
{%- endcomment -%}

<div class="electrolyte-info">
  <div class="electrolyte-grid">
    <div class="electrolyte-item">
      <div class="electrolyte-circle magnesium-circle" data-symbol="Mg"></div>
      <div class="electrolyte-details">
        <div class="electrolyte-name">Magnesium</div>
        <div class="electrolyte-amount">{{ block.settings.content_1 | strip_html }}</div>
      </div>
    </div>
    <div class="electrolyte-item">
      <div class="electrolyte-circle sodium-circle" data-symbol="Na"></div>
      <div class="electrolyte-details">
        <div class="electrolyte-name">Sodium</div>
        <div class="electrolyte-amount">{{ block.settings.content_2 | strip_html }}</div>
      </div>
    </div>
    <div class="electrolyte-item">
      <div class="electrolyte-circle potassium-circle" data-symbol="K"></div>
      <div class="electrolyte-details">
        <div class="electrolyte-name">Potassium</div>
        <div class="electrolyte-amount">{{ block.settings.content_3 | strip_html }}</div>
      </div>
    </div>
  </div>
</div>
