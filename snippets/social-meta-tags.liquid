{%- comment -%}
  seo-optimized.liquid
  Scope-aware SEO, OG, Twitter and JSON-LD snippet
  Enhanced version with comprehensive structured data that scales across page types
{%- endcomment -%}

{%- liquid
  assign root_title      = shop.name | strip
  assign max_title_chars = 60
  assign max_desc_chars  = 155

  comment
    1. PAGE TITLE  ───────────────────────────────────────────
  endcomment
  capture proposed_title
    if template == 'index'
      echo root_title
      unless shop.meta_description == blank
        echo ' | ' | append: shop.meta_description | truncate: 30
      endunless
    else
      echo page_title
      unless page_title contains root_title
        echo ' | ' | append: root_title
      endunless
    endif
  endcapture
  assign meta_title = proposed_title | strip_html | escape | truncate: max_title_chars, ''

  comment
    2. META DESCRIPTION  ────────────────────────────────────
  endcomment
  capture proposed_desc
    case template
      when 'product'
        if product.description != blank
          echo product.description | strip_html | truncate: max_desc_chars, ''
        else
          echo shop.meta_description
        endif
      when 'article'
        if article.excerpt != blank
          echo article.excerpt | strip_html | truncate: max_desc_chars, ''
        else
          echo article.content | strip_html | truncate: max_desc_chars, ''
        endif
      else
        echo shop.meta_description | default: page_description
    endcase
  endcapture
  assign meta_desc = proposed_desc | escape

  comment
    3. CANONICAL URL  ───────────────────────────────────────
  endcomment
  assign canonical = canonical_url | default: shop.url

  comment
    4. IMAGE FOR SOCIAL  ────────────────────────────────────
  endcomment
  assign share_image = ''
  if template == 'product'
    assign share_image = product.featured_image | img_url: '800x' | prepend: 'https:'
  elsif template == 'article'
    assign share_image = article.image | img_url: '800x' | prepend: 'https:'
  elsif template == 'collection'
    assign share_image = collection.image | img_url: '800x' | prepend: 'https:'
  endif
-%}

<title>{{ meta_title }}</title>
<meta name="description" content="{{ meta_desc }}" />
<link rel="canonical" href="{{ canonical }}" />

{%- comment -%} Open Graph {%- endcomment -%}
<meta property="og:site_name" content="{{ root_title }}" />
<meta property="og:title"       content="{{ meta_title }}" />
<meta property="og:description" content="{{ meta_desc }}" />
<meta property="og:url"         content="{{ canonical }}" />
<meta property="og:type"        content="{% if template == 'product' %}product{% elsif template == 'article' %}article{% else %}website{% endif %}" />
{% if share_image != '' %}
  <meta property="og:image" content="{{ share_image }}" />
{% endif %}

{%- comment -%} Twitter Card {%- endcomment -%}
<meta name="twitter:card"  content="summary_large_image" />
<meta name="twitter:title" content="{{ meta_title }}" />
<meta name="twitter:description" content="{{ meta_desc }}" />
{% if share_image != '' %}
  <meta name="twitter:image" content="{{ share_image }}" />
{% endif %}

{%- comment -%} ENHANCED JSON-LD FOR DIETARY SUPPLEMENTS {%- endcomment -%}
{%- if template == 'product' -%}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "DietarySupplement",
  "name": "{{ product.title | strip_html | escape }}",
  "description": "{{ meta_desc }}",
  "url": "{{ canonical }}",
  {% if share_image != '' %}"image": "{{ share_image }}",{% endif %}
  "brand": {
    "@type": "Brand",
    "name": "{{ root_title }}"
  },
  "activeIngredient": [
    {
      "@type": "ChemicalSubstance",
      "name": "Magnesium Glycinate",
      "description": "Calming form of magnesium"
    },
    {
      "@type": "ChemicalSubstance",
      "name": "Magnesium Taurate",
      "description": "Clarity-supporting form of magnesium"
    },
    {
      "@type": "ChemicalSubstance",
      "name": "Magnesium Malate",
      "description": "Recovery-supporting form of magnesium"
    }
  ],
  "servingSize": "1 stick (7g)",
  "servingsPerContainer": "30",
  "targetPopulation": "Adults seeking electrolyte balance and hydration support",
  "mechanismOfAction": "Provides essential electrolytes for cellular hydration and muscle function",
  "dosageForm": "Powder",
  "administrationRoute": "Oral"
}
</script>
{%- endif -%}